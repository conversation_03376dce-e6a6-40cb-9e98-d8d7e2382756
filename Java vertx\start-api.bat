@echo off
echo Starting AWS Textract Document Extraction API Server (Java Vert.x)
echo ================================================================

cd api

echo Building project...
call mvn clean compile

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Starting API server on port 8080...
call mvn exec:java -Dexec.mainClass="io.vertx.core.Launcher" -Dexec.args="run com.textract.api.MainVerticle"

pause
