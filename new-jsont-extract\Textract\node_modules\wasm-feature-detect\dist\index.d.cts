export const
bigInt: () => Promise<boolean>,
bulkMemory: () => Promise<boolean>,
exceptions: () => Promise<boolean>,
exceptionsFinal: () => Promise<boolean>,
extendedConst: () => Promise<boolean>,
gc: () => Promise<boolean>,
jsStringBuiltins: () => Promise<boolean>,
jspi: () => Promise<boolean>,
memory64: () => Promise<boolean>,
multiMemory: () => Promise<boolean>,
multiValue: () => Promise<boolean>,
mutableGlobals: () => Promise<boolean>,
referenceTypes: () => Promise<boolean>,
relaxedSimd: () => Promise<boolean>,
saturatedFloatToInt: () => Promise<boolean>,
signExtensions: () => Promise<boolean>,
simd: () => Promise<boolean>,
streamingCompilation: () => Promise<boolean>,
tailCall: () => Promise<boolean>,
threads: () => Promise<boolean>,
typeReflection: () => Promise<boolean>,
typedFunctionReferences: () => Promise<boolean>;