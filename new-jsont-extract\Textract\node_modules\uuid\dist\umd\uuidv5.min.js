!function(r,e){"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(r=r||self).uuidv5=e()}(this,(function(){"use strict";for(var r=[],e=0;e<256;++e)r[e]=(e+256).toString(16).substr(1);function n(r,e,n,t){switch(r){case 0:return e&n^~e&t;case 1:return e^n^t;case 2:return e&n^e&t^n&t;case 3:return e^n^t}}function t(r,e){return r<<e|r>>>32-e}return function(e,n,t){var a=function(e,a,o,f){var u=o&&f||0;if("string"==typeof e&&(e=function(r){r=unescape(encodeURIComponent(r));for(var e=new Array(r.length),n=0;n<r.length;n++)e[n]=r.charCodeAt(n);return e}(e)),"string"==typeof a&&(a=function(r){var e=[];return r.replace(/[a-fA-F0-9]{2}/g,(function(r){e.push(parseInt(r,16))})),e}(a)),!Array.isArray(e))throw TypeError("value must be an array of bytes");if(!Array.isArray(a)||16!==a.length)throw TypeError("namespace must be uuid string or an Array of 16 byte values");var c=t(a.concat(e));if(c[6]=15&c[6]|n,c[8]=63&c[8]|128,o)for(var i=0;i<16;++i)o[u+i]=c[i];return o||function(e,n){var t=n||0,a=r;return[a[e[t++]],a[e[t++]],a[e[t++]],a[e[t++]],"-",a[e[t++]],a[e[t++]],"-",a[e[t++]],a[e[t++]],"-",a[e[t++]],a[e[t++]],"-",a[e[t++]],a[e[t++]],a[e[t++]],a[e[t++]],a[e[t++]],a[e[t++]]].join("")}(c)};try{a.name=e}catch(r){}return a.DNS="6ba7b810-9dad-11d1-80b4-00c04fd430c8",a.URL="6ba7b811-9dad-11d1-80b4-00c04fd430c8",a}("v5",80,(function(r){var e=[1518500249,1859775393,2400959708,3395469782],a=[1732584193,4023233417,2562383102,271733878,3285377520];if("string"==typeof r){var o=unescape(encodeURIComponent(r));r=new Array(o.length);for(var f=0;f<o.length;f++)r[f]=o.charCodeAt(f)}r.push(128);var u=r.length/4+2,c=Math.ceil(u/16),i=new Array(c);for(f=0;f<c;f++){i[f]=new Array(16);for(var s=0;s<16;s++)i[f][s]=r[64*f+4*s]<<24|r[64*f+4*s+1]<<16|r[64*f+4*s+2]<<8|r[64*f+4*s+3]}for(i[c-1][14]=8*(r.length-1)/Math.pow(2,32),i[c-1][14]=Math.floor(i[c-1][14]),i[c-1][15]=8*(r.length-1)&4294967295,f=0;f<c;f++){for(var d=new Array(80),y=0;y<16;y++)d[y]=i[f][y];for(y=16;y<80;y++)d[y]=t(d[y-3]^d[y-8]^d[y-14]^d[y-16],1);var h=a[0],p=a[1],v=a[2],l=a[3],g=a[4];for(y=0;y<80;y++){var A=Math.floor(y/20),b=t(h,5)+n(A,p,v,l)+g+e[A]+d[y]>>>0;g=l,l=v,v=t(p,30)>>>0,p=h,h=b}a[0]=a[0]+h>>>0,a[1]=a[1]+p>>>0,a[2]=a[2]+v>>>0,a[3]=a[3]+l>>>0,a[4]=a[4]+g>>>0}return[a[0]>>24&255,a[0]>>16&255,a[0]>>8&255,255&a[0],a[1]>>24&255,a[1]>>16&255,a[1]>>8&255,255&a[1],a[2]>>24&255,a[2]>>16&255,a[2]>>8&255,255&a[2],a[3]>>24&255,a[3]>>16&255,a[3]>>8&255,255&a[3],a[4]>>24&255,a[4]>>16&255,a[4]>>8&255,255&a[4]]}))}));