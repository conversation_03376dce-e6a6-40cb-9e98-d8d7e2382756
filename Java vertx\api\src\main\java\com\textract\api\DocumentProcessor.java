package com.textract.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.FileUpload;
import io.vertx.ext.web.RoutingContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Document processor for handling file uploads and OCR extraction
 */
public class DocumentProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(DocumentProcessor.class);
    
    private final Vertx vertx;
    private final ObjectMapper objectMapper;
    private final TextractService textractService;
    
    // In-memory storage for processed documents
    private final Map<String, JsonObject> processedDocuments = new ConcurrentHashMap<>();
    private final List<JsonObject> extractedDocuments = new ArrayList<>();
    
    // Supported file types
    private static final Set<String> SUPPORTED_EXTENSIONS = Set.of(
        ".jpg", ".jpeg", ".png", ".pdf"
    );
    
    // Document type patterns
    private static final Map<String, Pattern> DOCUMENT_PATTERNS = Map.of(
        "AADHAAR", Pattern.compile("(?i)(aadhaar|आधार|unique identification|uid)", Pattern.CASE_INSENSITIVE),
        "PAN", Pattern.compile("(?i)(permanent account number|pan|income tax|पैन)", Pattern.CASE_INSENSITIVE),
        "PASSPORT", Pattern.compile("(?i)(passport|republic of india|भारत गणराज्य)", Pattern.CASE_INSENSITIVE),
        "DRIVING_LICENSE", Pattern.compile("(?i)(driving licence|driving license|dl|ड्राइविंग लाइसेंस)", Pattern.CASE_INSENSITIVE),
        "VOTER_ID", Pattern.compile("(?i)(voter|election|electoral|मतदाता)", Pattern.CASE_INSENSITIVE),
        "GST_CERTIFICATE", Pattern.compile("(?i)(gst|goods and services tax|gstin|tax certificate)", Pattern.CASE_INSENSITIVE)
    );
    
    // Field extraction patterns
    private static final Map<String, Pattern> FIELD_PATTERNS = Map.of(
        "PAN_NUMBER", Pattern.compile("[A-Z]{5}\\d{4}[A-Z]"),
        "AADHAAR_NUMBER", Pattern.compile("\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}"),
        "PASSPORT_NUMBER", Pattern.compile("[A-Z]\\d{7}"),
        "DATE_PATTERN", Pattern.compile("\\d{1,2}[/\\-]\\d{1,2}[/\\-]\\d{4}"),
        "NAME_PATTERN", Pattern.compile("NAME[:\\s]*\\n([A-Z\\s]+?)(?:\\n|FATHER|DOB|GENDER|ADDRESS|$)", Pattern.CASE_INSENSITIVE)
    );

    public DocumentProcessor(Vertx vertx) {
        this.vertx = vertx;
        this.objectMapper = new ObjectMapper();
        this.textractService = new TextractService();

        // Initialize with mock data
        initializeMockData();
    }

    public DocumentProcessor(Vertx vertx, JsonObject config) {
        this.vertx = vertx;
        this.objectMapper = new ObjectMapper();
        this.textractService = new TextractService();

        // Initialize TextractService with configuration
        this.textractService.initialize(config);

        // Initialize with mock data
        initializeMockData();
    }

    /**
     * Handle file upload and processing
     */
    public void handleUpload(RoutingContext context) {
        try {
            List<FileUpload> uploads = context.fileUploads();
            
            if (uploads.isEmpty()) {
                sendErrorResponse(context, 400, "No files uploaded");
                return;
            }
            
            logger.info("Processing {} uploaded files", uploads.size());
            
            JsonArray extractedDocuments = new JsonArray();
            JsonArray errors = new JsonArray();
            int successfullyProcessed = 0;
            
            for (FileUpload upload : uploads) {
                try {
                    JsonObject result = processUploadedFile(upload);
                    if (result != null) {
                        extractedDocuments.add(result);
                        successfullyProcessed++;
                        
                        // Store in memory
                        String documentId = UUID.randomUUID().toString();
                        result.put("document_id", documentId);
                        processedDocuments.put(documentId, result);
                        this.extractedDocuments.add(result);
                    }
                } catch (Exception e) {
                    logger.error("Error processing file {}: {}", upload.fileName(), e.getMessage());
                    errors.add(new JsonObject()
                        .put("filename", upload.fileName())
                        .put("error", e.getMessage()));
                }
            }
            
            JsonObject response = new JsonObject()
                .put("success", true)
                .put("total_files_uploaded", uploads.size())
                .put("successfully_processed", successfullyProcessed)
                .put("errors", errors)
                .put("extracted_documents", extractedDocuments)
                .put("processing_timestamp", Instant.now().toString());
            
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
                
        } catch (Exception e) {
            logger.error("Error handling upload", e);
            sendErrorResponse(context, 500, "Error processing upload: " + e.getMessage());
        }
    }

    /**
     * Handle getting grouped documents
     */
    public void handleGetGroupedDocuments(RoutingContext context) {
        try {
            JsonObject response = createGroupedResponse();
            
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
                
        } catch (Exception e) {
            logger.error("Error getting grouped documents", e);
            sendErrorResponse(context, 500, "Error retrieving grouped documents: " + e.getMessage());
        }
    }

    /**
     * Handle processing personal documents
     */
    public void handleProcessPersonalDocuments(RoutingContext context) {
        try {
            JsonObject response = createGroupedResponse();
            
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(response.encode());
                
        } catch (Exception e) {
            logger.error("Error processing personal documents", e);
            sendErrorResponse(context, 500, "Error processing personal documents: " + e.getMessage());
        }
    }

    /**
     * Process a single uploaded file
     */
    private JsonObject processUploadedFile(FileUpload upload) throws IOException {
        String filename = upload.fileName();
        String uploadedFileName = upload.uploadedFileName();
        
        logger.info("Processing file: {}", filename);
        
        // Validate file type
        if (!isValidFileType(filename)) {
            throw new IllegalArgumentException("Unsupported file type: " + filename);
        }
        
        // Read file content
        Path filePath = Paths.get(uploadedFileName);
        byte[] fileContent = Files.readAllBytes(filePath);
        
        // Process with Textract (or mock processing)
        JsonObject textractResult = textractService.processDocument(fileContent, filename);

        // Extract the fields from the Textract result
        JsonObject extractedFields = textractResult.getJsonObject("extracted_fields", new JsonObject());

        // Create the final document structure
        JsonObject extractedData = new JsonObject();
        extractedData.mergeIn(extractedFields); // Copy all extracted fields

        // Add metadata
        extractedData.put("filename", filename);
        extractedData.put("file_size", fileContent.length);
        extractedData.put("processed_at", Instant.now().toString());
        extractedData.put("raw_text", textractResult.getString("raw_text", ""));
        extractedData.put("processing_method", textractResult.getString("processing_method", "UNKNOWN"));

        return extractedData;
    }

    /**
     * Check if file type is supported
     */
    private boolean isValidFileType(String filename) {
        String extension = filename.toLowerCase();
        return SUPPORTED_EXTENSIONS.stream()
            .anyMatch(extension::endsWith);
    }

    /**
     * Create grouped response with person-based grouping
     */
    private JsonObject createGroupedResponse() {
        Map<String, List<JsonObject>> groupedByPerson = groupDocumentsByPerson();
        
        JsonArray groupedArray = new JsonArray();
        int groupId = 1;
        
        for (Map.Entry<String, List<JsonObject>> entry : groupedByPerson.entrySet()) {
            String personName = entry.getKey();
            List<JsonObject> documents = entry.getValue();
            
            JsonObject group = new JsonObject()
                .put("group_id", groupId++)
                .put("person_name", personName)
                .put("total_documents", documents.size())
                .put("documents", new JsonArray(documents));
            
            groupedArray.add(group);
        }
        
        return new JsonObject()
            .put("success", true)
            .put("total_files", extractedDocuments.size())
            .put("processed_files", extractedDocuments.size())
            .put("personal_documents", new JsonObject()
                .put("count", extractedDocuments.size())
                .put("grouped_by_person", groupedArray))
            .put("timestamp", Instant.now().toString());
    }

    /**
     * Group documents by person name
     */
    private Map<String, List<JsonObject>> groupDocumentsByPerson() {
        Map<String, List<JsonObject>> grouped = new HashMap<>();
        
        for (JsonObject doc : extractedDocuments) {
            String personName = getPersonName(doc);
            grouped.computeIfAbsent(personName, k -> new ArrayList<>()).add(doc);
        }
        
        return grouped;
    }

    /**
     * Extract person name from document
     */
    private String getPersonName(JsonObject document) {
        String firstName = document.getString("FIRST_NAME", "");
        String middleName = document.getString("MIDDLE_NAME", "");
        String lastName = document.getString("LAST_NAME", "");
        
        List<String> nameParts = Arrays.asList(firstName, middleName, lastName)
            .stream()
            .filter(part -> part != null && !part.trim().isEmpty())
            .collect(Collectors.toList());
        
        return nameParts.isEmpty() ? "Unknown Person" : String.join(" ", nameParts);
    }

    /**
     * Send error response
     */
    private void sendErrorResponse(RoutingContext context, int statusCode, String message) {
        JsonObject error = new JsonObject()
            .put("success", false)
            .put("error", message)
            .put("timestamp", Instant.now().toString());
        
        context.response()
            .setStatusCode(statusCode)
            .putHeader("Content-Type", "application/json")
            .end(error.encode());
    }

    /**
     * Initialize with mock data for testing
     */
    private void initializeMockData() {
        // Add some mock documents for testing
        JsonObject mockDoc1 = new JsonObject()
            .put("filename", "john-aadhaar.jpg")
            .put("FIRST_NAME", "John")
            .put("MIDDLE_NAME", "Kumar")
            .put("LAST_NAME", "Doe")
            .put("DOCUMENT_NUMBER", "1234 5678 9012")
            .put("DATE_OF_BIRTH", "01/01/1990")
            .put("ADDRESS", "123 Main Street, Mumbai")
            .put("GENDER", "MALE")
            .put("DOCUMENT_TYPE", "AADHAAR")
            .put("document_id", UUID.randomUUID().toString())
            .put("processed_at", Instant.now().toString());
        
        JsonObject mockDoc2 = new JsonObject()
            .put("filename", "john-pan.jpg")
            .put("FIRST_NAME", "John")
            .put("MIDDLE_NAME", "Kumar")
            .put("LAST_NAME", "Doe")
            .put("DOCUMENT_NUMBER", "**********")
            .put("DATE_OF_BIRTH", "01/01/1990")
            .put("DOCUMENT_TYPE", "PAN")
            .put("document_id", UUID.randomUUID().toString())
            .put("processed_at", Instant.now().toString());
        
        extractedDocuments.add(mockDoc1);
        extractedDocuments.add(mockDoc2);
        
        processedDocuments.put(mockDoc1.getString("document_id"), mockDoc1);
        processedDocuments.put(mockDoc2.getString("document_id"), mockDoc2);
        
        logger.info("Initialized with {} mock documents", extractedDocuments.size());
    }
}
