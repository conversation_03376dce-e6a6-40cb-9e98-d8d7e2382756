{"http": {"port": 3333, "host": "0.0.0.0"}, "aws": {"accessKeyId": "${AWS_ACCESS_KEY_ID:********************}", "secretAccessKey": "${AWS_SECRET_ACCESS_KEY:kzLpPpgc9nX0nOCGFf5xcBxlBz+PH/66Hyd0BmU8}", "region": "${AWS_REGION:ap-south-1}", "s3": {"bucketName": "${S3_BUCKET_NAME:doc-scan-textract}"}, "textract": {"enabled": true, "timeout": 30000}}, "upload": {"maxFileSize": 52428800, "allowedExtensions": [".jpg", ".jpeg", ".png", ".pdf"], "uploadDirectory": "uploads"}, "logging": {"level": "INFO"}}