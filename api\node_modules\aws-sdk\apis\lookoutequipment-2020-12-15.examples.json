{"version": "1.0", "examples": {"CreateRetrainingScheduler": [{"input": {"ClientToken": "sample-client-token", "LookbackWindow": "P360D", "ModelName": "sample-model", "PromoteMode": "MANUAL", "RetrainingFrequency": "P1M"}, "output": {"ModelArn": "arn:aws:lookoutequipment:us-east-1:123456789012:model/sample-model/a1b2c3d4-5678-90ab-cdef-EXAMPLE11111", "ModelName": "sample-model", "Status": "PENDING"}, "comments": {"input": {}, "output": {}}, "description": "", "id": "creates-a-retraining-scheduler-with-manual-promote-mode-1694018486212", "title": "Creates a retraining scheduler with manual promote mode"}, {"input": {"ClientToken": "sample-client-token", "LookbackWindow": "P360D", "ModelName": "sample-model", "RetrainingFrequency": "P1M", "RetrainingStartDate": "2024-01-01T00:00:00Z"}, "output": {"ModelArn": "arn:aws:lookoutequipment:us-east-1:123456789012:model/sample-model/a1b2c3d4-5678-90ab-cdef-EXAMPLE11111", "ModelName": "sample-model", "Status": "PENDING"}, "comments": {"input": {}, "output": {}}, "description": "", "id": "creates-a-retraining-scheduler-with-a-specific-start-date-1694018790519", "title": "Creates a retraining scheduler with a specific start date"}], "DeleteRetrainingScheduler": [{"input": {"ModelName": "sample-model"}, "comments": {"input": {}, "output": {}}, "description": "", "id": "deletes-a-retraining-scheduler-1694019240097", "title": "Deletes a retraining scheduler"}], "DescribeRetrainingScheduler": [{"input": {"ModelName": "sample-model"}, "output": {"CreatedAt": "2023-10-01T15:00:00Z", "LookbackWindow": "P360D", "ModelArn": "arn:aws:lookoutequipment:us-east-1:123456789012:model/sample-model/a1b2c3d4-5678-90ab-cdef-EXAMPLE11111", "ModelName": "sample-model", "PromoteMode": "MANAGED", "RetrainingFrequency": "P1M", "RetrainingStartDate": "2023-11-01T00:00:00Z", "Status": "RUNNING", "UpdatedAt": "2023-10-01T15:00:00Z"}, "comments": {"input": {}, "output": {}}, "description": "", "id": "describes-a-retraining-scheduler-1694019344252", "title": "Describes a retraining scheduler"}], "ListRetrainingSchedulers": [{"input": {"MaxResults": 50}, "output": {"RetrainingSchedulerSummaries": [{"LookbackWindow": "P180D", "ModelArn": "arn:aws:lookoutequipment:us-east-1:123456789012:model/sample-model-1/a1b2c3d4-5678-90ab-cdef-EXAMPLE11111", "ModelName": "sample-model-1", "RetrainingFrequency": "P1M", "RetrainingStartDate": "2023-06-01T00:00:00Z", "Status": "RUNNING"}, {"LookbackWindow": "P180D", "ModelArn": "arn:aws:lookoutequipment:us-east-1:123456789012:model/sample-model-2/a1b2c3d4-5678-90ab-cdef-EXAMPLE22222", "ModelName": "sample-model-2", "RetrainingFrequency": "P30D", "RetrainingStartDate": "2023-08-15T00:00:00Z", "Status": "RUNNING"}, {"LookbackWindow": "P360D", "ModelArn": "arn:aws:lookoutequipment:us-east-1:123456789012:model/sample-model-3/a1b2c3d4-5678-90ab-cdef-EXAMPLE33333", "ModelName": "sample-model-3", "RetrainingFrequency": "P1M", "RetrainingStartDate": "2023-09-01T00:00:00Z", "Status": "STOPPED"}]}, "comments": {"input": {}, "output": {}}, "description": "", "id": "listing-retraining-schedulers-1694016740503", "title": "Listing retraining schedulers"}], "StartRetrainingScheduler": [{"input": {"ModelName": "sample-model"}, "output": {"ModelArn": "arn:aws:lookoutequipment:us-east-1:123456789012:model/sample-model/a1b2c3d4-5678-90ab-cdef-EXAMPLE11111", "ModelName": "sample-model", "Status": "PENDING"}, "comments": {"input": {}, "output": {}}, "description": "", "id": "starts-a-retraining-scheduler-1694019629413", "title": "Starts a retraining scheduler"}], "StopRetrainingScheduler": [{"input": {"ModelName": "sample-model"}, "output": {"ModelArn": "arn:aws:lookoutequipment:us-east-1:123456789012:model/sample-model/a1b2c3d4-5678-90ab-cdef-EXAMPLE11111", "ModelName": "sample-model", "Status": "STOPPING"}, "comments": {"input": {}, "output": {}}, "description": "", "id": "stops-a-retraining-scheduler-1694019734149", "title": "Stops a retraining scheduler"}], "UpdateModel": [{"input": {"LabelsInputConfiguration": {"LabelGroupName": "sample-label-group"}, "ModelName": "sample-model"}, "comments": {"input": {}, "output": {}}, "description": "", "id": "updates-a-model-1694020683458", "title": "Updates a model"}], "UpdateRetrainingScheduler": [{"input": {"ModelName": "sample-model", "RetrainingFrequency": "P1Y", "RetrainingStartDate": "2024-01-01T00:00:00Z"}, "comments": {"input": {}, "output": {}}, "description": "", "id": "updates-a-retraining-scheduler-1694019840918", "title": "Updates a retraining scheduler"}]}}