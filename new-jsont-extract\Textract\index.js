const AWS = require("aws-sdk");
const fs = require("fs");

// Configure Textract client with credentials
const textract = new AWS.Textract({
  accessKeyId: "********************",
  secretAccessKey: "kzLpPpgc9nX0nOCGFf5xcBxlBz+PH/66Hyd0BmU8",
  region: "ap-south-1"
});

// --------------------------
// Textract response parser
// --------------------------
function mapToStandardJson(response) {
  const fields = {
    FIRST_NAME: "",
    MIDDLE_NAME: "",
    LAST_NAME: "",
    DOCUMENT_NUMBER: "",
    DATE_OF_BIRTH: "",
    ADDRESS: ""
  };

  if (!response.Blocks) return fields;

  const blockMap = {};
  const keyMap = {};
  const valueMap = {};

  response.Blocks.forEach((block) => {
    blockMap[block.Id] = block;
    if (block.BlockType === "KEY_VALUE_SET") {
      if (block.EntityTypes && block.EntityTypes.includes("KEY")) {
        keyMap[block.Id] = block;
      } else {
        valueMap[block.Id] = block;
      }
    }
  });

  function getText(block) {
    let text = "";
    if (block.Relationships) {
      block.Relationships.forEach((rel) => {
        if (rel.Type === "CHILD") {
          rel.Ids.forEach((cid) => {
            const child = blockMap[cid];
            if (child && child.Text) text += child.Text + " ";
          });
        }
      });
    }
    return text.trim();
  }

  Object.keys(keyMap).forEach((keyId) => {
    const keyBlock = keyMap[keyId];
    const key = getText(keyBlock).toUpperCase();

    if (keyBlock.Relationships) {
      keyBlock.Relationships.forEach((rel) => {
        if (rel.Type === "VALUE") {
          rel.Ids.forEach((valueId) => {
            const valueBlock = valueMap[valueId];
            if (valueBlock) {
              const value = getText(valueBlock);

              if (key.includes("NAME")) {
                const parts = value.split(" ");
                if (parts.length >= 1) fields.FIRST_NAME = parts[0];
                if (parts.length >= 3) fields.MIDDLE_NAME = parts.slice(1, -1).join(" ");
                if (parts.length >= 2) fields.LAST_NAME = parts[parts.length - 1];
              } else if (key.includes("DOB") || key.includes("BIRTH")) {
                fields.DATE_OF_BIRTH = value;
              } else if (key.includes("NUMBER") || key.includes("PAN") || key.includes("AADHAAR")) {
                fields.DOCUMENT_NUMBER = value;
              } else if (key.includes("ADDRESS")) {
                fields.ADDRESS = value;
              }
            }
          });
        }
      });
    }
  });

  return fields;
}

// --------------------------
// Grouping logic
// --------------------------
function getFullName(data) {
  return [data.FIRST_NAME, data.MIDDLE_NAME, data.LAST_NAME].filter(Boolean).join(" ").toUpperCase().trim();
}

function getFirstLast(data) {
  return [data.FIRST_NAME, data.LAST_NAME].filter(Boolean).join(" ").toUpperCase().trim();
}

function groupDocuments(docs) {
  const groups = [];
  const used = new Set();

  for (let i = 0; i < docs.length; i++) {
    if (used.has(i)) continue;

    const group = [docs[i]];
    used.add(i);

    const name1 = getFullName(docs[i].extracted_data);
    const short1 = getFirstLast(docs[i].extracted_data);

    for (let j = i + 1; j < docs.length; j++) {
      if (used.has(j)) continue;

      const name2 = getFullName(docs[j].extracted_data);
      const short2 = getFirstLast(docs[j].extracted_data);

      if ((name1 && name2 && name1 === name2) || (short1 && short2 && short1 === short2)) {
        group.push(docs[j]);
        used.add(j);
      }
    }

    groups.push(group);
  }

  return { grouped_documents: groups };
}

// --------------------------
// Main function (local test)
// --------------------------
async function run() {
  try {
    const files = ["aadhaar.jpg", "pan.jpg"]; // put your local test files here
    const extractedDocs = [];

    for (const file of files) {
      const fileBytes = fs.readFileSync(file);

      const response = await textract.analyzeDocument({
        Document: { Bytes: fileBytes },
        FeatureTypes: ["FORMS"],
      }).promise();

      const extracted = mapToStandardJson(response);

      extractedDocs.push({
        filename: file,
        extracted_data: extracted
      });
    }

    const groups = groupDocuments(extractedDocs);
    console.log(JSON.stringify(groups, null, 2));
  } catch (err) {
    console.error("Error:", err);
  }
}

run();
