const express = require('express');
const multer = require('multer');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// Try to require AWS SDK modules, fallback if not available
let TextractClient, AnalyzeDocumentCommand, DetectDocumentTextCommand;
try {
  const textractModule = require("@aws-sdk/client-textract");
  TextractClient = textractModule.TextractClient;
  AnalyzeDocumentCommand = textractModule.AnalyzeDocumentCommand;
  DetectDocumentTextCommand = textractModule.DetectDocumentTextCommand;
} catch (err) {
  console.log('AWS Textract SDK not available, using mock responses');
}

// Try to load other optional dependencies
let pdfParse, Tesseract, pdfPoppler, S3Client;
try {
  pdfParse = require('pdf-parse');
} catch (err) {
  console.log('pdf-parse not available');
}

try {
  Tesseract = require('tesseract.js');
} catch (err) {
  console.log('tesseract.js not available');
}

try {
  pdfPoppler = require('pdf-poppler');
} catch (err) {
  console.log('pdf-poppler not available');
}

try {
  const s3Module = require("@aws-sdk/client-s3");
  S3Client = s3Module.S3Client;
} catch (err) {
  console.log('AWS S3 SDK not available');
}

try {
  require('dotenv').config();
} catch (err) {
  console.log('dotenv not available, using environment variables');
}

const app = express();
const PORT = 3005; // Different port for API

// Configure Textract client with credentials (if available)
let textract;
if (TextractClient) {
  textract = new TextractClient({
    region: process.env.AWS_REGION || "ap-south-1",
    credentials: {
      accessKeyId: "********************",
      secretAccessKey: "kzLpPpgc9nX0nOCGFf5xcBxlBz+PH/66Hyd0BmU8"
    }
  });
} else {
  console.log('Textract client not available - using mock responses');
}

// Middleware
app.use(cors({
  origin: ['http://localhost:3004', 'http://localhost:3000'], // Allow UI servers
  credentials: true
}));
app.use(express.json());

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir);
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB per file
    files: 20, // Allow up to 20 files
    fields: 30, // Increase field limit
    fieldSize: 2 * 1024 * 1024 // 2MB field size
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|pdf/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only images (JPEG, JPG, PNG) and PDF files are allowed!'));
    }
  }
});

// Textract response parser
function mapToStandardJson(response) {
  // First detect document type
  const allText = response.Blocks
    .filter(block => block.BlockType === 'LINE')
    .map(block => block.Text)
    .join(' ').toUpperCase();

  let isGSTDocument = allText.includes('GSTIN') ||
                     allText.includes('GST') ||
                     allText.includes('GOODS AND SERVICES TAX') ||
                     allText.includes('REGISTRATION CERTIFICATE');

  let isPersonalDocument = allText.includes('AADHAAR') ||
                          allText.includes('PAN') ||
                          allText.includes('PERMANENT ACCOUNT NUMBER') ||
                          allText.includes('GOVERNMENT OF INDIA');

  // Initialize fields based on document type
  let fields = {};

  if (isGSTDocument) {
    fields = {
      GSTIN: "",
      LEGAL_NAME: "",
      TRADE_NAME: "",
      REGISTRATION_DATE: "",
      CONSTITUTION: "",
      ADDRESS: "",
      STATE: "",
      STATUS: "",
      DOCUMENT_TYPE: "GST_CERTIFICATE"
    };
  } else {
    fields = {
      FIRST_NAME: "",
      MIDDLE_NAME: "",
      LAST_NAME: "",
      DOCUMENT_NUMBER: "",
      DATE_OF_BIRTH: "",
      ADDRESS: "",
      GENDER: "",
      DOCUMENT_TYPE: ""
    };
  }

  if (!response.Blocks) return fields;

  const blockMap = {};
  const keyMap = {};
  const valueMap = {};

  response.Blocks.forEach((block) => {
    blockMap[block.Id] = block;
    if (block.BlockType === "KEY_VALUE_SET") {
      if (block.EntityTypes && block.EntityTypes.includes("KEY")) {
        keyMap[block.Id] = block;
      } else {
        valueMap[block.Id] = block;
      }
    }
  });

  function getText(block, blockMap) {
    let text = "";
    if (block.Relationships) {
      block.Relationships.forEach((rel) => {
        if (rel.Type === "CHILD") {
          rel.Ids.forEach((cid) => {
            const child = blockMap[cid];
            if (child && child.Text) text += child.Text + " ";
          });
        }
      });
    }
    return text.trim();
  }

  function getValueBlock(keyBlock, valueMap) {
    if (keyBlock.Relationships) {
      for (const rel of keyBlock.Relationships) {
        if (rel.Type === "VALUE") {
          for (const valueId of rel.Ids) {
            return valueMap[valueId];
          }
        }
      }
    }
    return null;
  }

  // Extract key-value pairs based on document type
  Object.keys(keyMap).forEach((keyId) => {
    const keyBlock = keyMap[keyId];
    const valueBlock = getValueBlock(keyBlock, valueMap);

    if (keyBlock && valueBlock) {
      const key = getText(keyBlock, blockMap).toUpperCase();
      const value = getText(valueBlock, blockMap);

      if (isGSTDocument) {
        // GST Certificate specific key matching
        if (key.match(/GSTIN|GST.*NUMBER|REGISTRATION.*NUMBER/)) {
          fields.GSTIN = value;
        } else if (key.match(/LEGAL.*NAME|BUSINESS.*NAME|NAME.*BUSINESS/)) {
          fields.LEGAL_NAME = value;
        } else if (key.match(/TRADE.*NAME|TRADING.*NAME/)) {
          fields.TRADE_NAME = value;
        } else if (key.match(/REGISTRATION.*DATE|DATE.*REGISTRATION|EFFECTIVE.*DATE/)) {
          fields.REGISTRATION_DATE = value;
        } else if (key.match(/CONSTITUTION|TYPE.*BUSINESS|BUSINESS.*TYPE/)) {
          fields.CONSTITUTION = value;
        } else if (key.match(/ADDRESS|PRINCIPAL.*PLACE/)) {
          fields.ADDRESS = value;
        } else if (key.match(/STATE|STATE.*CODE/)) {
          fields.STATE = value;
        } else if (key.match(/STATUS|REGISTRATION.*STATUS/)) {
          fields.STATUS = value;
        }
      } else {
        // Personal document key matching
        if (key.includes("NAME") && !fields.FIRST_NAME) {
          const parts = value.split(/\s+/).filter(Boolean);
          if (parts.length === 1) {
            fields.FIRST_NAME = parts[0];
          } else if (parts.length === 2) {
            fields.FIRST_NAME = parts[0];
            fields.LAST_NAME = parts[1];
          } else if (parts.length >= 3) {
            fields.FIRST_NAME = parts[0];
            fields.MIDDLE_NAME = parts.slice(1, -1).join(" ");
            fields.LAST_NAME = parts[parts.length - 1];
          }
        } else if (key.includes("DOB") || key.includes("BIRTH")) {
          fields.DATE_OF_BIRTH = value;
        } else if (key.includes("GENDER") || key.includes("SEX")) {
          fields.GENDER = value.toUpperCase();
        } else if (key.includes("ADDRESS")) {
          fields.ADDRESS = value;
        }
      }
    }
  });

  // Detect document type based on extracted data and text content
  if (!isGSTDocument) {
    const panPattern = /^[A-Z]{5}\d{4}[A-Z]$/;
    const aadhaarPattern = /^\d{4}\s?\d{4}\s?\d{4}$/;

    // First try to detect by document number pattern
    if (fields.DOCUMENT_NUMBER) {
      if (panPattern.test(fields.DOCUMENT_NUMBER.replace(/\s/g, ''))) {
        fields.DOCUMENT_TYPE = "PAN";
      } else if (aadhaarPattern.test(fields.DOCUMENT_NUMBER)) {
        fields.DOCUMENT_TYPE = "AADHAAR";
      }
    }

    // If document type is still empty, try to detect by text content
    if (!fields.DOCUMENT_TYPE || fields.DOCUMENT_TYPE === "") {
      if (allText.includes('PERMANENT ACCOUNT NUMBER') ||
          allText.includes('PAN CARD') ||
          allText.includes('INCOME TAX DEPARTMENT')) {
        fields.DOCUMENT_TYPE = "PAN";
      } else if (allText.includes('AADHAAR') ||
                 allText.includes('UNIQUE IDENTIFICATION') ||
                 allText.includes('GOVERNMENT OF INDIA') ||
                 allText.includes('UIDAI')) {
        fields.DOCUMENT_TYPE = "AADHAAR";
      } else if (allText.includes('PASSPORT') ||
                 allText.includes('REPUBLIC OF INDIA')) {
        fields.DOCUMENT_TYPE = "PASSPORT";
      } else if (allText.includes('DRIVING LICENCE') ||
                 allText.includes('DRIVING LICENSE') ||
                 allText.includes('TRANSPORT DEPARTMENT')) {
        fields.DOCUMENT_TYPE = "DRIVING_LICENSE";
      } else if (allText.includes('VOTER') ||
                 allText.includes('ELECTION COMMISSION')) {
        fields.DOCUMENT_TYPE = "VOTER_ID";
      } else {
        // Default fallback based on document number pattern if available
        if (fields.DOCUMENT_NUMBER) {
          if (/[A-Z]{5}\d{4}[A-Z]/.test(fields.DOCUMENT_NUMBER.replace(/\s/g, ''))) {
            fields.DOCUMENT_TYPE = "PAN";
          } else if (/\d{4}[\s-]?\d{4}[\s-]?\d{4}/.test(fields.DOCUMENT_NUMBER)) {
            fields.DOCUMENT_TYPE = "AADHAAR";
          } else {
            fields.DOCUMENT_TYPE = "UNKNOWN";
          }
        } else {
          fields.DOCUMENT_TYPE = "UNKNOWN";
        }
      }
    }
  }

  return fields;
}

// Process document with AWS Textract
async function processWithTextract(filePath) {
  try {
    console.log(`Starting Textract processing for: ${filePath}`);

    // If Textract is not available, return mock data
    if (!textract || !AnalyzeDocumentCommand) {
      console.log('Textract not available, returning mock data');
      return {
        FIRST_NAME: "Mock",
        MIDDLE_NAME: "Test",
        LAST_NAME: "User",
        DOCUMENT_NUMBER: "MOCK123456",
        DATE_OF_BIRTH: "01/01/1990",
        ADDRESS: "Mock Address",
        GENDER: "MALE",
        DOCUMENT_TYPE: "MOCK"
      };
    }

    // Read the file
    const imageBytes = fs.readFileSync(filePath);

    // Try FORMS feature first (best for structured documents)
    const params = {
      Document: {
        Bytes: imageBytes
      },
      FeatureTypes: ['FORMS']
    };

    try {
      console.log('Trying Textract with FORMS feature...');
      const result = await textract.send(new AnalyzeDocumentCommand(params));
      console.log(`Textract FORMS processing successful. Found ${result.Blocks.length} blocks`);
      return mapToStandardJson(result);
    } catch (formsError) {
      console.log('FORMS feature failed, trying TABLES feature...', formsError.message);

      // Try TABLES feature
      params.FeatureTypes = ['TABLES'];
      try {
        const result = await textract.send(new AnalyzeDocumentCommand(params));
        console.log(`Textract TABLES processing successful. Found ${result.Blocks.length} blocks`);
        return mapToStandardJson(result);
      } catch (tablesError) {
        console.log('TABLES feature failed, trying basic text detection...', tablesError.message);

        // Try basic text detection
        const basicParams = {
          Document: {
            Bytes: imageBytes
          }
        };

        try {
          const result = await textract.send(new DetectDocumentTextCommand(basicParams));
          console.log(`Textract basic text detection successful. Found ${result.Blocks.length} blocks`);
          return mapToStandardJson(result);
        } catch (basicError) {
          console.error('All Textract methods failed:', basicError.message);
          return {
            FIRST_NAME: "",
            MIDDLE_NAME: "",
            LAST_NAME: "",
            DOCUMENT_NUMBER: "",
            DATE_OF_BIRTH: "",
            ADDRESS: "",
            GENDER: "",
            DOCUMENT_TYPE: "",
            ERROR: `Textract failed: ${basicError.message}`
          };
        }
      }
    }
  } catch (error) {
    console.error('Textract processing error:', error);
    return {
      FIRST_NAME: "",
      MIDDLE_NAME: "",
      LAST_NAME: "",
      DOCUMENT_NUMBER: "",
      DATE_OF_BIRTH: "",
      ADDRESS: "",
      GENDER: "",
      DOCUMENT_TYPE: "",
      ERROR: `Processing failed: ${error.message}`
    };
  }
}

// Group documents by person name
function createGroupedJsonByPerson(extractedDocs) {
  const groups = [];
  const used = new Set();

  for (let i = 0; i < extractedDocs.length; i++) {
    if (used.has(i)) continue;

    const doc = extractedDocs[i];
    const group = [doc];
    used.add(i);

    // Find similar documents
    for (let j = i + 1; j < extractedDocs.length; j++) {
      if (used.has(j)) continue;

      const otherDoc = extractedDocs[j];

      // Compare names
      const name1 = `${doc.FIRST_NAME} ${doc.MIDDLE_NAME} ${doc.LAST_NAME}`.trim();
      const name2 = `${otherDoc.FIRST_NAME} ${otherDoc.MIDDLE_NAME} ${otherDoc.LAST_NAME}`.trim();

      if (name1.toLowerCase() === name2.toLowerCase() && name1.length > 0) {
        group.push(otherDoc);
        used.add(j);
      }
    }

    if (group.length > 0) {
      const personName = `${doc.FIRST_NAME} ${doc.MIDDLE_NAME} ${doc.LAST_NAME}`.trim();
      groups.push({
        group_id: groups.length + 1,
        person_name: personName || "Unknown",
        total_documents: group.length,
        documents: group
      });
    }
  }

  return groups;
}

// Group documents by company name
function createGroupedJsonByCompany(extractedDocs) {
  const groups = [];
  const used = new Set();

  for (let i = 0; i < extractedDocs.length; i++) {
    if (used.has(i)) continue;

    const doc = extractedDocs[i];
    const group = [doc];
    used.add(i);

    // Find similar documents
    for (let j = i + 1; j < extractedDocs.length; j++) {
      if (used.has(j)) continue;

      const otherDoc = extractedDocs[j];

      // Compare company names
      const company1 = doc.LEGAL_NAME || doc.TRADE_NAME || "";
      const company2 = otherDoc.LEGAL_NAME || otherDoc.TRADE_NAME || "";

      if (company1.toLowerCase() === company2.toLowerCase() && company1.length > 0) {
        group.push(otherDoc);
        used.add(j);
      }
    }

    if (group.length > 0) {
      const companyName = doc.LEGAL_NAME || doc.TRADE_NAME || "Unknown";
      groups.push({
        group_id: groups.length + 1,
        company_name: companyName,
        total_documents: group.length,
        documents: group
      });
    }
  }

  return groups;
}

// API Routes

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: 'Document Extraction API is running' });
});

// Main upload endpoint for UI compatibility
app.post('/upload', upload.array('documents', 20), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        error: 'No files uploaded',
        success: false
      });
    }

    console.log(`Processing ${req.files.length} files...`);
    const extractedDocs = [];
    const personalDocs = [];
    const gstDocs = [];

    // Process each uploaded file
    for (const file of req.files) {
      try {
        console.log(`Processing file: ${file.originalname}`);

        // Process with Textract
        const extracted = await processWithTextract(file.path);

        extractedDocs.push({
          filename: file.originalname,
          ...extracted
        });

        // Categorize documents
        if (extracted.DOCUMENT_TYPE === "GST_CERTIFICATE") {
          gstDocs.push({
            filename: file.originalname,
            ...extracted
          });
        } else {
          personalDocs.push({
            filename: file.originalname,
            ...extracted
          });
        }

      } catch (fileError) {
        console.error(`Error processing file ${file.originalname}:`, fileError);
        extractedDocs.push({
          filename: file.originalname,
          ERROR: `Failed to process: ${fileError.message}`
        });
      }
    }

    const groupedByPerson = personalDocs.length > 0 ? createGroupedJsonByPerson(personalDocs) : [];
    const groupedByCompany = gstDocs.length > 0 ? createGroupedJsonByCompany(gstDocs) : [];

    // Save extractedDocs to a file for the grouped-by-person endpoint
    const outputPath = path.join(__dirname, 'extracted_results.json');
    const outputData = {
      all_extracted_data: extractedDocs,
      personal_documents: {
        count: personalDocs.length,
        grouped_by_person: groupedByPerson
      },
      gst_documents: {
        count: gstDocs.length,
        grouped_by_company: groupedByCompany
      }
    };
    fs.writeFileSync(outputPath, JSON.stringify(outputData, null, 2), 'utf8');

    res.json({
      success: true,
      total_files: req.files.length,
      processed_files: extractedDocs.length,
      personal_documents: {
        count: personalDocs.length,
        grouped_by_person: groupedByPerson
      },
      gst_documents: {
        count: gstDocs.length,
        grouped_by_company: groupedByCompany
      },
      all_extracted_data: extractedDocs
    });

  } catch (error) {
    console.error('Upload error:', error);
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: `File too large. Maximum size is 10MB per file.` });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({ error: `Too many files. Maximum is 20 files per upload.` });
    }
    if (error.message && error.message.includes('Only images')) {
      return res.status(400).json({ error: `Upload error: ${error.message}` });
    }
    res.status(500).json({ error: error.message });
  }
});

// API endpoint to get grouped documents by person
app.get('/api/grouped-by-person', (req, res) => {
  // Load extracted results
  const dataPath = path.join(__dirname, 'extracted_results.json');
  let fileData = {};
  try {
    fileData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
  } catch (err) {
    return res.status(500).json({ success: false, error: 'Could not read extracted_results.json' });
  }

  res.json({
    success: true,
    total_files: fileData.all_extracted_data ? fileData.all_extracted_data.length : 0,
    processed_files: fileData.all_extracted_data ? fileData.all_extracted_data.length : 0,
    personal_documents: fileData.personal_documents || {
      count: 0,
      grouped_by_person: []
    }
  });
});

// API endpoint for personal documents processing
app.post('/api/process-personal-documents', upload.array('documents', 20), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No files uploaded',
        message: 'Please upload at least one Aadhaar or PAN document'
      });
    }

    const extractedDocs = [];
    const errors = [];

    for (const file of req.files) {
      try {
        const extracted = await processWithTextract(file.path);
        extractedDocs.push({
          filename: file.originalname,
          ...extracted
        });
      } catch (fileError) {
        console.error(`Error processing file ${file.originalname}:`, fileError);
        errors.push({
          filename: file.originalname,
          error: `Failed to process: ${fileError.message}`
        });
      }
    }

    const groupedByPerson = createGroupedJsonByPerson(extractedDocs);

    res.json({
      success: true,
      document_type: 'personal_documents',
      total_files_uploaded: req.files.length,
      successfully_processed: extractedDocs.length,
      errors: errors,
      grouped_by_person: groupedByPerson,
      processing_timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Personal documents processing error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: error.message
    });
  }
});

// Start server
app.listen(PORT, () => {
  console.log(`Document Extraction API running on http://localhost:${PORT}`);
  console.log('Available endpoints:');
  console.log('  POST /upload - Main upload endpoint');
  console.log('  GET /api/grouped-by-person - Get grouped results');
  console.log('  POST /api/process-personal-documents - Process personal documents');
  console.log('  GET /health - Health check');
});
