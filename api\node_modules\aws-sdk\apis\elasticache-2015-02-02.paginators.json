{"pagination": {"DescribeCacheClusters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "CacheClusters"}, "DescribeCacheEngineVersions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "CacheEngineVersions"}, "DescribeCacheParameterGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "CacheParameterGroups"}, "DescribeCacheParameters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Parameters"}, "DescribeCacheSecurityGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "CacheSecurityGroups"}, "DescribeCacheSubnetGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "CacheSubnetGroups"}, "DescribeEngineDefaultParameters": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "EngineDefaults.Marker", "result_key": "EngineDefaults.Parameters"}, "DescribeEvents": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Events"}, "DescribeGlobalReplicationGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "GlobalReplicationGroups"}, "DescribeReplicationGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ReplicationGroups"}, "DescribeReservedCacheNodes": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ReservedCacheNodes"}, "DescribeReservedCacheNodesOfferings": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ReservedCacheNodesOfferings"}, "DescribeServerlessCacheSnapshots": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ServerlessCacheSnapshots"}, "DescribeServerlessCaches": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "ServerlessCaches"}, "DescribeServiceUpdates": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "ServiceUpdates"}, "DescribeSnapshots": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Snapshots"}, "DescribeUpdateActions": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "UpdateActions"}, "DescribeUserGroups": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "UserGroups"}, "DescribeUsers": {"input_token": "<PERSON><PERSON>", "limit_key": "MaxRecords", "output_token": "<PERSON><PERSON>", "result_key": "Users"}}}