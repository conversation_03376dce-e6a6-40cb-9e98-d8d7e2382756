* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    margin-bottom: 30px;
    color: white;
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.header-text {
    text-align: left;
}

.header-text h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header-text p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.tech-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-outline {
    background: transparent;
    color: white;
    border: 2px solid white;
}

.btn-outline:hover {
    background: white;
    color: #667eea;
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .header-text {
        text-align: center;
    }
}

.upload-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 30px;
}

.upload-area {
    border: 3px dashed #667eea;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
    background: #f8f9ff;
}

.upload-area:hover {
    border-color: #764ba2;
    background: #f0f2ff;
}

.upload-area.dragover {
    border-color: #4CAF50;
    background: #e8f5e8;
}

.upload-icon {
    margin-bottom: 15px;
    color: #6c63ff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.upload-icon svg {
    width: 48px;
    height: 48px;
}

.upload-area h3 {
    margin-bottom: 10px;
    color: #333;
}

.upload-area p {
    color: #666;
    margin-bottom: 20px;
}

.browse-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: transform 0.2s ease;
}

.browse-btn:hover {
    transform: translateY(-2px);
}

.file-list {
    margin: 20px 0;
    max-height: 200px;
    overflow-y: auto;
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: #f5f5f5;
    border-radius: 8px;
    margin-bottom: 8px;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.file-icon {
    font-size: 1.2rem;
}

.file-name {
    font-weight: 500;
}

.file-size {
    color: #666;
    font-size: 0.9rem;
}

.remove-file {
    background: #ff4757;
    color: white;
    border: none;
    border-radius: 50%;
    width: 25px;
    height: 25px;
    cursor: pointer;
    font-size: 0.8rem;
}

.upload-controls {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
}

.upload-btn {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
}

.upload-btn:hover:not(:disabled) {
    transform: translateY(-2px);
}

.upload-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.clear-btn {
    background: #ff4757;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.clear-btn:hover {
    transform: translateY(-2px);
}

.loader {
    width: 20px;
    height: 20px;
    border: 2px solid #ffffff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.results-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.results-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
}

.view-toggle {
    display: flex;
    gap: 5px;
    background: #f5f5f5;
    border-radius: 25px;
    padding: 5px;
}

.view-btn {
    background: transparent;
    border: none;
    padding: 10px 20px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    color: #666;
}

.view-btn.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateY(-1px);
}

.view-btn:hover:not(.active) {
    background: #e0e0e0;
    color: #333;
}

.results-container {
    position: relative;
}

.view-content {
    display: none;
}

.view-content.active {
    display: block;
}

.rich-controls, .json-controls {
    margin-bottom: 15px;
    display: flex;
    gap: 10px;
    align-items: center;
}

.copy-btn, .format-btn, .download-btn, .export-btn, .print-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.2s ease;
}

.export-btn {
    background: #4CAF50;
}

.print-btn {
    background: #FF9800;
}

.copy-btn:hover, .format-btn:hover, .download-btn:hover,
.export-btn:hover, .print-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

#jsonOutput {
    background: #1e1e1e;
    color: #d4d4d4;
    padding: 20px;
    border-radius: 10px;
    overflow-x: auto;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
    max-height: 600px;
    overflow-y: auto;
}

.error-section {
    background: #ff4757;
    color: white;
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.error-message {
    font-weight: 600;
    text-align: center;
}

.user-group {
    background: #f8f9ff;
    border: 2px solid #667eea;
    border-radius: 10px;
    margin-bottom: 20px;
    overflow: hidden;
}

.user-group h3 {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 20px;
    margin: 0;
    font-weight: 600;
}

.user-group ul {
    padding: 15px 20px;
    list-style: none;
}

.user-group li {
    background: white;
    padding: 10px 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    border-left: 4px solid #667eea;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
}

/* Rich View Styles */
.rich-output {
    background: #f8f9ff;
    border-radius: 10px;
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
}

.person-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    border-left: 5px solid #667eea;
}

.person-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e0e0e0;
}

.person-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    font-weight: bold;
    margin-right: 15px;
}

.person-info h3 {
    margin: 0;
    color: #333;
    font-size: 1.3rem;
}

.person-info .document-count {
    color: #666;
    font-size: 0.9rem;
    margin-top: 2px;
}

.documents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.document-item {
    background: #f8f9ff;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e0e7ff;
}

.document-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.document-icon {
    font-size: 1.5rem;
    margin-right: 10px;
}

.document-title {
    font-weight: 600;
    color: #333;
    font-size: 1rem;
}

.document-fields {
    display: grid;
    gap: 8px;
}

.field-row {
    display: flex;
    justify-content: space-between;
    padding: 5px 0;
    border-bottom: 1px solid #f0f0f0;
}

.field-label {
    font-weight: 500;
    color: #555;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.field-value {
    color: #333;
    font-weight: 600;
    text-align: right;
    max-width: 60%;
    word-break: break-word;
}

.summary-stats {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 5px;
}

@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .upload-section, .results-section {
        padding: 20px;
    }
    
    .upload-area {
        padding: 30px 20px;
    }
}
