package com.textract.ui;

import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.Vertx;
import io.vertx.core.http.HttpServer;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.handler.BodyHandler;
import io.vertx.ext.web.handler.CorsHandler;
import io.vertx.ext.web.handler.StaticHandler;
import io.vertx.ext.web.client.WebClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

/**
 * UI Verticle for serving the document extraction web interface
 * 
 * This verticle serves static files and acts as a proxy to the API server
 */
public class UIVerticle extends AbstractVerticle {

    private static final Logger logger = LoggerFactory.getLogger(UIVerticle.class);

    /**
     * Main method to run the UI server directly
     */
    public static void main(String[] args) {
        Vertx vertx = Vertx.vertx();
        vertx.deployVerticle(new UIVerticle());
    }
    
    private static final int DEFAULT_PORT = 8081;
    private static final String DEFAULT_HOST = "0.0.0.0";
    private static final String DEFAULT_API_HOST = "localhost";
    private static final int DEFAULT_API_PORT = 9090;
    
    private HttpServer server;
    private WebClient webClient;
    private String apiBaseUrl;

    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        logger.info("Starting Document Extraction UI Server...");
        
        // Initialize web client for API communication
        webClient = WebClient.create(vertx);
        
        // Get configuration
        int port = config().getInteger("http.port", DEFAULT_PORT);
        String host = config().getString("http.host", DEFAULT_HOST);
        String apiHost = config().getString("api.host", DEFAULT_API_HOST);
        int apiPort = config().getInteger("api.port", DEFAULT_API_PORT);
        
        apiBaseUrl = String.format("http://%s:%d", apiHost, apiPort);
        
        // Create HTTP server
        server = vertx.createHttpServer();
        
        // Setup router
        Router router = setupRouter();
        
        // Start server
        server.requestHandler(router)
            .listen(port, host)
            .onSuccess(result -> {
                logger.info("✅ UI Server started successfully on {}:{}", host, port);
                logger.info("🌐 Web interface available at: http://{}:{}", host, port);
                logger.info("🔗 API server configured at: {}", apiBaseUrl);
                startPromise.complete();
            })
            .onFailure(error -> {
                logger.error("❌ Failed to start UI server: {}", error.getMessage());
                startPromise.fail(error);
            });
    }

    @Override
    public void stop(Promise<Void> stopPromise) throws Exception {
        logger.info("Stopping Document Extraction UI Server...");
        
        if (server != null) {
            server.close()
                .onSuccess(result -> {
                    logger.info("✅ UI Server stopped successfully");
                    stopPromise.complete();
                })
                .onFailure(error -> {
                    logger.error("❌ Error stopping UI server: {}", error.getMessage());
                    stopPromise.fail(error);
                });
        } else {
            stopPromise.complete();
        }
    }

    /**
     * Setup router with static file serving and API proxy routes
     */
    private Router setupRouter() {
        Router router = Router.router(vertx);
        
        // CORS handler
        CorsHandler corsHandler = CorsHandler.create()
            .addOrigin("*")
            .allowedMethods(Set.of(
                io.vertx.core.http.HttpMethod.GET,
                io.vertx.core.http.HttpMethod.POST,
                io.vertx.core.http.HttpMethod.PUT,
                io.vertx.core.http.HttpMethod.DELETE,
                io.vertx.core.http.HttpMethod.OPTIONS
            ))
            .allowedHeaders(Set.of(
                "Content-Type",
                "Authorization",
                "X-Requested-With",
                "Accept",
                "Origin"
            ));
        
        router.route().handler(corsHandler);
        
        // Body handler
        router.route().handler(BodyHandler.create());
        
        // API proxy routes
        setupApiProxyRoutes(router);
        
        // Static file handler (must be last)
        router.route("/*").handler(StaticHandler.create("webroot")
            .setIndexPage("index.html")
            .setCachingEnabled(false) // Disable caching for development
            .setMaxAgeSeconds(0));
        
        // Error handler
        router.errorHandler(500, context -> {
            logger.error("Internal server error", context.failure());
            context.response()
                .setStatusCode(500)
                .putHeader("Content-Type", "application/json")
                .end(new JsonObject()
                    .put("success", false)
                    .put("error", "Internal server error")
                    .put("message", context.failure().getMessage())
                    .encode());
        });
        
        return router;
    }

    /**
     * Setup API proxy routes to forward requests to the API server
     */
    private void setupApiProxyRoutes(Router router) {
        // Health check for UI server
        router.get("/ui/health").handler(context -> {
            logger.debug("UI health check requested");
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(new JsonObject()
                    .put("status", "OK")
                    .put("message", "Document Extraction UI is running")
                    .put("timestamp", System.currentTimeMillis())
                    .put("version", "1.0.0")
                    .put("api_url", apiBaseUrl)
                    .encode());
        });
        
        // Proxy upload requests to API server
        router.post("/upload").handler(context -> {
            logger.info("Proxying upload request to API server");

            try {
                // Create multipart form for file uploads
                io.vertx.ext.web.client.HttpRequest<io.vertx.core.buffer.Buffer> request =
                    webClient.post(9090, DEFAULT_API_HOST, "/upload");

                // Create multipart form
                io.vertx.ext.web.multipart.MultipartForm form = io.vertx.ext.web.multipart.MultipartForm.create();

                // Add uploaded files to the form
                for (io.vertx.ext.web.FileUpload upload : context.fileUploads()) {
                    form.binaryFileUpload("files", upload.fileName(), upload.uploadedFileName(), "application/octet-stream");
                }

                // Send the multipart form
                request.sendMultipartForm(form)
                    .onSuccess(response -> {
                        // Forward the response back to the client
                        context.response()
                            .setStatusCode(response.statusCode())
                            .putHeader("Content-Type", "application/json")
                            .end(response.bodyAsString());
                    })
                    .onFailure(error -> {
                        logger.error("Error proxying upload request: {}", error.getMessage());
                        context.response()
                            .setStatusCode(500)
                            .putHeader("Content-Type", "application/json")
                            .end(new JsonObject()
                                .put("success", false)
                                .put("error", "Failed to connect to API server")
                                .put("message", error.getMessage())
                                .encode());
                    });
            } catch (Exception e) {
                logger.error("Error processing upload: {}", e.getMessage());
                context.response()
                    .setStatusCode(500)
                    .putHeader("Content-Type", "application/json")
                    .end(new JsonObject()
                        .put("success", false)
                        .put("error", "Error processing upload")
                        .put("message", e.getMessage())
                        .encode());
            }
        });
        
        // Proxy API requests to API server
        router.get("/api/*").handler(context -> {
            String path = context.request().path();
            logger.debug("Proxying API request: {}", path);
            
            webClient.get(9090, DEFAULT_API_HOST, path)
                .send()
                .onSuccess(response -> {
                    context.response()
                        .setStatusCode(response.statusCode())
                        .putHeader("Content-Type", "application/json")
                        .end(response.bodyAsString());
                })
                .onFailure(error -> {
                    logger.error("Error proxying API request {}: {}", path, error.getMessage());
                    context.response()
                        .setStatusCode(500)
                        .putHeader("Content-Type", "application/json")
                        .end(new JsonObject()
                            .put("success", false)
                            .put("error", "Failed to connect to API server")
                            .put("message", error.getMessage())
                            .encode());
                });
        });
        
        // Proxy health check to API server
        router.get("/health").handler(context -> {
            logger.debug("Proxying health check to API server");
            
            webClient.get(9090, DEFAULT_API_HOST, "/health")
                .send()
                .onSuccess(response -> {
                    context.response()
                        .setStatusCode(response.statusCode())
                        .putHeader("Content-Type", "application/json")
                        .end(response.bodyAsString());
                })
                .onFailure(error -> {
                    logger.error("Error proxying health check: {}", error.getMessage());
                    context.response()
                        .setStatusCode(503)
                        .putHeader("Content-Type", "application/json")
                        .end(new JsonObject()
                            .put("status", "ERROR")
                            .put("message", "API server unavailable")
                            .put("error", error.getMessage())
                            .encode());
                });
        });
        
        // UI configuration endpoint
        router.get("/ui/config").handler(context -> {
            JsonObject config = new JsonObject()
                .put("apiUrl", apiBaseUrl)
                .put("version", "1.0.0")
                .put("features", new JsonObject()
                    .put("fileUpload", true)
                    .put("documentGrouping", true)
                    .put("richView", true)
                    .put("jsonView", true))
                .put("supportedFormats", new String[]{"JPG", "JPEG", "PNG", "PDF"})
                .put("maxFileSize", "50MB");
            
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(config.encode());
        });
    }
}
