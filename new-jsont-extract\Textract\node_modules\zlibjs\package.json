{"author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "name": "zlibjs", "description": "zlib, gzip and zip implementation in JavaScript", "version": "0.3.1", "license": "MIT", "main": "./bin/node-zlib.js", "homepage": "https://github.com/imaya/zlib.js", "repository": {"type": "git", "url": "git://github.com/imaya/zlib.js.git"}, "dependencies": {}, "devDependencies": {"babel-preset-env": "^1.5.1", "babel-preset-power-assert": "^1.0.0", "babel-register": "^6.24.1", "grunt": "^1.0.1", "grunt-closure-tools": "^0.9.9", "grunt-contrib-concat": "^1.0.1", "karma": "^1.3.0", "karma-chrome-launcher": "^2.2.0", "karma-espower-preprocessor": "^1.1.0", "karma-firefox-launcher": "^1.0.1", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.3", "karma-power-assert": "^1.0.0", "karma-sinon": "^1.0.5", "mocha": "^3.1.0", "power-assert": "^1.4.4", "sinon": "^2.3.2"}, "files": ["README.md", "README.en.md", "ChangeLog.md", "LICENSE", "bin", "package.json"], "optionalDependencies": {}, "engines": {"node": "*"}, "scripts": {"test": "npm run test-mocha && npm run test-karma", "test-mocha": "mocha --compilers js:babel-register test/nodejs/node-test.js", "test-karma": "karma start --single-run"}}