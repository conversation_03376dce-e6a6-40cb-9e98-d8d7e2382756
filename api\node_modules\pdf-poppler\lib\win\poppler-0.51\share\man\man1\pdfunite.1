.\" Copyright 2011 The Poppler Developers - http://poppler.freedesktop.org
.TH pdfunite 1 "15 September 2011"
.SH NAME
pdfunite \- Portable Document Format (PDF) page merger
.SH SYNOPSIS
.B pdfunite
[options]
.I PDF-sourcefile1..PDF-sourcefilen PDF-destfile
.SH DESCRIPTION
.B pdfunite
merges several PDF (Portable Document Format) files in order of their occurrence on command line to one PDF result file.
.TP
Neither of the PDF-sourcefile1 to PDF-sourcefilen should be encrypted.
.SH OPTIONS
.TP
.B \-v
Print copyright and version information.
.TP
.B \-h
Print usage information.
.RB ( \-help
and
.B \-\-help
are equivalent.)
.SH EXAMPLE
pdfunite sample1.pdf sample2.pdf sample.pdf
.TP
merges all pages from sample1.pdf and sample2.pdf (in that order) and creates sample.pdf
.SH AUTHOR
The pdfunite software and documentation are copyright 1996-2004 Glyph & Cog, LLC
and copyright 2005-2011 The Poppler Developers - http://poppler.freedesktop.org
.SH "SEE ALSO"
.BR pdfdetach (1),
.BR pdffonts (1),
.BR pdfimages (1),
.BR pdfinfo (1),
.BR pdftocairo (1),
.BR pdftohtml (1),
.BR pdftoppm (1),
.BR pdftops (1),
.BR pdftotext (1)
.BR pdfseparate (1),
.BR pdfsig (1)
