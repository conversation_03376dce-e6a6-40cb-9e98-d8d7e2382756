const fs = require('fs');
const path = require('path');

// Try to require AWS SDK modules, fallback if not available
let TextractClient, AnalyzeDocumentCommand, DetectDocumentTextCommand;
try {
  const textractModule = require("@aws-sdk/client-textract");
  TextractClient = textractModule.TextractClient;
  AnalyzeDocumentCommand = textractModule.AnalyzeDocumentCommand;
  DetectDocumentTextCommand = textractModule.DetectDocumentTextCommand;
} catch (err) {
  console.log('AWS Textract SDK not available, using mock responses');
}

// Configure Textract client
let textract;
if (TextractClient) {
  textract = new TextractClient({
    region: "ap-south-1",
    credentials: {
      accessKeyId: "********************",
      secretAccessKey: "kzLpPpgc9nX0nOCGFf5xcBxlBz+PH/66Hyd0BmU8"
    }
  });
}

async function testExtraction() {
  // Check if there are any uploaded files
  const uploadsDir = path.join(__dirname, 'uploads');
  
  if (!fs.existsSync(uploadsDir)) {
    console.log('No uploads directory found');
    return;
  }
  
  const files = fs.readdirSync(uploadsDir);
  if (files.length === 0) {
    console.log('No uploaded files found');
    return;
  }
  
  const testFile = path.join(uploadsDir, files[0]);
  console.log(`Testing extraction on: ${files[0]}`);
  
  if (!textract) {
    console.log('Textract not available, cannot test');
    return;
  }
  
  try {
    const imageBytes = fs.readFileSync(testFile);
    
    // Try basic text detection first
    const params = {
      Document: {
        Bytes: imageBytes
      }
    };
    
    console.log('Attempting basic text detection...');
    const result = await textract.send(new DetectDocumentTextCommand(params));
    
    console.log(`Found ${result.Blocks.length} blocks`);
    
    // Extract all text
    const allText = result.Blocks
      .filter(block => block.BlockType === 'LINE')
      .map(block => block.Text)
      .join('\n');
    
    console.log('Extracted text:');
    console.log('================');
    console.log(allText);
    console.log('================');
    
    // Check for key-value pairs
    const keyValueBlocks = result.Blocks.filter(block => block.BlockType === 'KEY_VALUE_SET');
    console.log(`Found ${keyValueBlocks.length} key-value blocks`);
    
    if (keyValueBlocks.length > 0) {
      console.log('Key-value blocks found - structured extraction possible');
    } else {
      console.log('No key-value blocks - will use pattern-based extraction');
    }
    
  } catch (error) {
    console.error('Extraction failed:', error.message);
  }
}

testExtraction().catch(console.error);
