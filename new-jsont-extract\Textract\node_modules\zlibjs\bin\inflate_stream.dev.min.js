/** @license zlib.js 2012 - imaya [ https://github.com/imaya/zlib.js ] The MIT License */(function() {'use strict';var k=void 0,l=!0,aa=this;function s(a,d){var b=a.split("."),c=aa;!(b[0]in c)&&c.execScript&&c.execScript("var "+b[0]);for(var e;b.length&&(e=b.shift());)!b.length&&d!==k?c[e]=d:c=c[e]?c[e]:c[e]={}};var x="undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint16Array&&"undefined"!==typeof Uint32Array&&"undefined"!==typeof DataView;function y(a){var d=a.length,b=0,c=Number.POSITIVE_INFINITY,e,g,f,p,h,q,m,n,r,B;for(n=0;n<d;++n)a[n]>b&&(b=a[n]),a[n]<c&&(c=a[n]);e=1<<b;g=new (x?Uint32Array:Array)(e);f=1;p=0;for(h=2;f<=b;){for(n=0;n<d;++n)if(a[n]===f){q=0;m=p;for(r=0;r<f;++r)q=q<<1|m&1,m>>=1;B=f<<16|n;for(r=q;r<e;r+=h)g[r]=B;++p}++f;p<<=1;h<<=1}return[g,b,c]};function z(a,d,b){this.u=[];this.i=b?b:32768;this.v=0;this.a=d===k?0:d;this.d=this.e=0;this.input=x?new Uint8Array(a):a;this.b=new (x?Uint8Array:Array)(this.i);this.c=0;this.t=this.l=!1;this.f=0;this.status=A}var A=0;
z.prototype.j=function(a,d){var b=!1;a!==k&&(this.input=a);d!==k&&(this.a=d);for(;!b;)switch(this.status){case A:case 1:var c;var e=k;this.status=1;H(this);if(0>(e=I(this,3)))J(this),c=-1;else{e&1&&(this.l=l);e>>>=1;switch(e){case 0:this.h=0;break;case 1:this.h=1;break;case 2:this.h=2;break;default:throw Error("unknown BTYPE: "+e);}this.status=2;c=k}0>c&&(b=l);break;case 2:case 3:switch(this.h){case 0:var g;var f=k,p=k,h=this.input,q=this.a;this.status=3;if(q+4>=h.length)g=-1;else{f=h[q++]|h[q++]<<
8;p=h[q++]|h[q++]<<8;if(f===~p)throw Error("invalid uncompressed block header: length verify");this.d=this.e=0;this.a=q;this.m=f;this.status=4;g=k}0>g&&(b=l);break;case 1:this.status=3;this.k=ba;this.n=ca;this.status=4;break;case 2:var m;a:{var n=k,r=k,B=k,V=new (x?Uint8Array:Array)(K.length),W=k;this.status=3;H(this);n=I(this,5)+257;r=I(this,5)+1;B=I(this,4)+4;if(0>n||0>r||0>B)J(this),m=-1;else{try{for(var w=k,D=k,E=0,C=k,u=k,t=k,X=k,t=0;t<B;++t){if(0>(w=I(this,3)))throw Error("not enough input");
V[K[t]]=w}W=y(V);u=new (x?Uint8Array:Array)(n+r);t=0;for(X=n+r;t<X;){D=O(this,W);if(0>D)throw Error("not enough input");switch(D){case 16:if(0>(w=I(this,2)))throw Error("not enough input");for(C=3+w;C--;)u[t++]=E;break;case 17:if(0>(w=I(this,3)))throw Error("not enough input");for(C=3+w;C--;)u[t++]=0;E=0;break;case 18:if(0>(w=I(this,7)))throw Error("not enough input");for(C=11+w;C--;)u[t++]=0;E=0;break;default:E=u[t++]=D}}new (x?Uint8Array:Array)(n);new (x?Uint8Array:Array)(r);this.k=x?y(u.subarray(0,
n)):y(u.slice(0,n));this.n=x?y(u.subarray(n)):y(u.slice(n))}catch(pa){J(this);m=-1;break a}this.status=4;m=0}}0>m&&(b=l)}break;case 4:case 5:switch(this.h){case 0:var L;a:{var Y=this.input,F=this.a,M=this.b,G=this.c,N=this.m;for(this.status=5;N--;){G===M.length&&(M=P(this,{o:2}));if(F>=Y.length){this.a=F;this.c=G;this.m=N+1;L=-1;break a}M[G++]=Y[F++]}0>N&&(this.status=6);this.a=F;this.c=G;L=0}0>L&&(b=l);break;case 1:case 2:0>da(this)&&(b=l)}break;case 6:this.l?b=l:this.status=A}var Z,v=this.c,$;Z=
this.t?x?new Uint8Array(this.b.subarray(this.f,v)):this.b.slice(this.f,v):x?this.b.subarray(this.f,v):this.b.slice(this.f,v);this.f=v;v>32768+this.i&&(this.c=this.f=32768,x?($=this.b,this.b=new Uint8Array(this.i+32768),this.b.set($.subarray(v-32768,v))):this.b=this.b.slice(v-32768));return Z};
var ea=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],K=x?new Uint16Array(ea):ea,fa=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258],ga=x?new Uint16Array(fa):fa,ha=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],ia=x?new Uint8Array(ha):ha,ja=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],ka=x?new Uint16Array(ja):ja,la=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,
11,11,12,12,13,13],ma=x?new Uint8Array(la):la,Q=new (x?Uint8Array:Array)(288),R,na;R=0;for(na=Q.length;R<na;++R)Q[R]=143>=R?8:255>=R?9:279>=R?7:8;var ba=y(Q),S=new (x?Uint8Array:Array)(30),T,oa;T=0;for(oa=S.length;T<oa;++T)S[T]=5;var ca=y(S);function I(a,d){for(var b=a.e,c=a.d,e=a.input,g=a.a,f;c<d;){if(e.length<=g)return-1;f=e[g++];b|=f<<c;c+=8}f=b&(1<<d)-1;a.e=b>>>d;a.d=c-d;a.a=g;return f}
function O(a,d){for(var b=a.e,c=a.d,e=a.input,g=a.a,f=d[0],p=d[1],h,q,m;c<p;){if(e.length<=g)return-1;h=e[g++];b|=h<<c;c+=8}q=f[b&(1<<p)-1];m=q>>>16;if(m>c)throw Error("invalid code length: "+m);a.e=b>>m;a.d=c-m;a.a=g;return q&65535}function H(a){a.s=a.a;a.r=a.d;a.q=a.e}function J(a){a.a=a.s;a.d=a.r;a.e=a.q}
function da(a){var d=a.b,b=a.c,c,e,g,f,p=a.k,h=a.n,q=d.length,m;for(a.status=5;;){H(a);c=O(a,p);if(0>c)return a.c=b,J(a),-1;if(256===c)break;if(256>c)b===q&&(d=P(a),q=d.length),d[b++]=c;else{e=c-257;f=ga[e];if(0<ia[e]){m=I(a,ia[e]);if(0>m)return a.c=b,J(a),-1;f+=m}c=O(a,h);if(0>c)return a.c=b,J(a),-1;g=ka[c];if(0<ma[c]){m=I(a,ma[c]);if(0>m)return a.c=b,J(a),-1;g+=m}b+f>=q&&(d=P(a),q=d.length);for(;f--;)d[b]=d[b++-g];if(a.a===a.input.length)return a.c=b,-1}}for(;8<=a.d;)a.d-=8,a.a--;a.c=b;a.status=
6}function P(a,d){var b,c=a.input.length/a.a+1|0,e,g,f,p=a.input,h=a.b;d&&("number"===typeof d.o&&(c=d.o),"number"===typeof d.p&&(c+=d.p));2>c?(e=(p.length-a.a)/a.k[2],f=258*(e/2)|0,g=f<h.length?h.length+f:h.length<<1):g=h.length*c;x?(b=new Uint8Array(g),b.set(h)):b=h;a.b=b;return a.b};function U(a){this.input=a===k?new (x?Uint8Array:Array):a;this.a=0;this.g=new z(this.input,this.a);this.b=this.g.b}
U.prototype.j=function(a){var d;if(a!==k)if(x){var b=new Uint8Array(this.input.length+a.length);b.set(this.input,0);b.set(a,this.input.length);this.input=b}else this.input=this.input.concat(a);var c;if(c=this.method===k){var e;var g=this.a,f=this.input,p=f[g++],h=f[g++];if(p===k||h===k)e=-1;else{switch(p&15){case 8:this.method=8;break;default:throw Error("unsupported compression method");}if(0!==((p<<8)+h)%31)throw Error("invalid fcheck flag:"+((p<<8)+h)%31);if(h&32)throw Error("fdict flag is not supported");
this.a=g;e=k}c=0>e}if(c)return new (x?Uint8Array:Array);d=this.g.j(this.input,this.a);0!==this.g.a&&(this.input=x?this.input.subarray(this.g.a):this.input.slice(this.g.a),this.a=0);return d};s("Zlib.InflateStream",U);s("Zlib.InflateStream.prototype.decompress",U.prototype.j);}).call(this);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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