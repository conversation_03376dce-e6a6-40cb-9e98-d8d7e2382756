/** @license zlib.js 2012 - imaya [ https://github.com/imaya/zlib.js ] The MIT License */(function() {'use strict';var n=void 0,u=!0,aa=this;function ba(e,d){var c=e.split("."),f=aa;!(c[0]in f)&&f.execScript&&f.execScript("var "+c[0]);for(var a;c.length&&(a=c.shift());)!c.length&&d!==n?f[a]=d:f=f[a]?f[a]:f[a]={}};var C="undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint16Array&&"undefined"!==typeof Uint32Array&&"undefined"!==typeof DataView;function K(e,d){this.index="number"===typeof d?d:0;this.d=0;this.buffer=e instanceof(C?Uint8Array:Array)?e:new (C?Uint8Array:Array)(32768);if(2*this.buffer.length<=this.index)throw Error("invalid index");this.buffer.length<=this.index&&ca(this)}function ca(e){var d=e.buffer,c,f=d.length,a=new (C?Uint8Array:Array)(f<<1);if(C)a.set(d);else for(c=0;c<f;++c)a[c]=d[c];return e.buffer=a}
K.prototype.a=function(e,d,c){var f=this.buffer,a=this.index,b=this.d,k=f[a],m;c&&1<d&&(e=8<d?(L[e&255]<<24|L[e>>>8&255]<<16|L[e>>>16&255]<<8|L[e>>>24&255])>>32-d:L[e]>>8-d);if(8>d+b)k=k<<d|e,b+=d;else for(m=0;m<d;++m)k=k<<1|e>>d-m-1&1,8===++b&&(b=0,f[a++]=L[k],k=0,a===f.length&&(f=ca(this)));f[a]=k;this.buffer=f;this.d=b;this.index=a};K.prototype.finish=function(){var e=this.buffer,d=this.index,c;0<this.d&&(e[d]<<=8-this.d,e[d]=L[e[d]],d++);C?c=e.subarray(0,d):(e.length=d,c=e);return c};
var ga=new (C?Uint8Array:Array)(256),M;for(M=0;256>M;++M){for(var R=M,S=R,ha=7,R=R>>>1;R;R>>>=1)S<<=1,S|=R&1,--ha;ga[M]=(S<<ha&255)>>>0}var L=ga;function ja(e){this.buffer=new (C?Uint16Array:Array)(2*e);this.length=0}ja.prototype.getParent=function(e){return 2*((e-2)/4|0)};ja.prototype.push=function(e,d){var c,f,a=this.buffer,b;c=this.length;a[this.length++]=d;for(a[this.length++]=e;0<c;)if(f=this.getParent(c),a[c]>a[f])b=a[c],a[c]=a[f],a[f]=b,b=a[c+1],a[c+1]=a[f+1],a[f+1]=b,c=f;else break;return this.length};
ja.prototype.pop=function(){var e,d,c=this.buffer,f,a,b;d=c[0];e=c[1];this.length-=2;c[0]=c[this.length];c[1]=c[this.length+1];for(b=0;;){a=2*b+2;if(a>=this.length)break;a+2<this.length&&c[a+2]>c[a]&&(a+=2);if(c[a]>c[b])f=c[b],c[b]=c[a],c[a]=f,f=c[b+1],c[b+1]=c[a+1],c[a+1]=f;else break;b=a}return{index:e,value:d,length:this.length}};function ka(e,d){this.e=ma;this.f=0;this.input=C&&e instanceof Array?new Uint8Array(e):e;this.c=0;d&&(d.lazy&&(this.f=d.lazy),"number"===typeof d.compressionType&&(this.e=d.compressionType),d.outputBuffer&&(this.b=C&&d.outputBuffer instanceof Array?new Uint8Array(d.outputBuffer):d.outputBuffer),"number"===typeof d.outputIndex&&(this.c=d.outputIndex));this.b||(this.b=new (C?Uint8Array:Array)(32768))}var ma=2,T=[],U;
for(U=0;288>U;U++)switch(u){case 143>=U:T.push([U+48,8]);break;case 255>=U:T.push([U-144+400,9]);break;case 279>=U:T.push([U-256+0,7]);break;case 287>=U:T.push([U-280+192,8]);break;default:throw"invalid literal: "+U;}
ka.prototype.h=function(){var e,d,c,f,a=this.input;switch(this.e){case 0:c=0;for(f=a.length;c<f;){d=C?a.subarray(c,c+65535):a.slice(c,c+65535);c+=d.length;var b=d,k=c===f,m=n,g=n,p=n,v=n,x=n,l=this.b,h=this.c;if(C){for(l=new Uint8Array(this.b.buffer);l.length<=h+b.length+5;)l=new Uint8Array(l.length<<1);l.set(this.b)}m=k?1:0;l[h++]=m|0;g=b.length;p=~g+65536&65535;l[h++]=g&255;l[h++]=g>>>8&255;l[h++]=p&255;l[h++]=p>>>8&255;if(C)l.set(b,h),h+=b.length,l=l.subarray(0,h);else{v=0;for(x=b.length;v<x;++v)l[h++]=
b[v];l.length=h}this.c=h;this.b=l}break;case 1:var q=new K(C?new Uint8Array(this.b.buffer):this.b,this.c);q.a(1,1,u);q.a(1,2,u);var t=na(this,a),w,da,z;w=0;for(da=t.length;w<da;w++)if(z=t[w],K.prototype.a.apply(q,T[z]),256<z)q.a(t[++w],t[++w],u),q.a(t[++w],5),q.a(t[++w],t[++w],u);else if(256===z)break;this.b=q.finish();this.c=this.b.length;break;case ma:var B=new K(C?new Uint8Array(this.b.buffer):this.b,this.c),ra,J,N,O,P,Ia=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],W,sa,X,ta,ea,ia=Array(19),
ua,Q,fa,y,va;ra=ma;B.a(1,1,u);B.a(ra,2,u);J=na(this,a);W=oa(this.j,15);sa=pa(W);X=oa(this.i,7);ta=pa(X);for(N=286;257<N&&0===W[N-1];N--);for(O=30;1<O&&0===X[O-1];O--);var wa=N,xa=O,F=new (C?Uint32Array:Array)(wa+xa),r,G,s,Y,E=new (C?Uint32Array:Array)(316),D,A,H=new (C?Uint8Array:Array)(19);for(r=G=0;r<wa;r++)F[G++]=W[r];for(r=0;r<xa;r++)F[G++]=X[r];if(!C){r=0;for(Y=H.length;r<Y;++r)H[r]=0}r=D=0;for(Y=F.length;r<Y;r+=G){for(G=1;r+G<Y&&F[r+G]===F[r];++G);s=G;if(0===F[r])if(3>s)for(;0<s--;)E[D++]=0,
H[0]++;else for(;0<s;)A=138>s?s:138,A>s-3&&A<s&&(A=s-3),10>=A?(E[D++]=17,E[D++]=A-3,H[17]++):(E[D++]=18,E[D++]=A-11,H[18]++),s-=A;else if(E[D++]=F[r],H[F[r]]++,s--,3>s)for(;0<s--;)E[D++]=F[r],H[F[r]]++;else for(;0<s;)A=6>s?s:6,A>s-3&&A<s&&(A=s-3),E[D++]=16,E[D++]=A-3,H[16]++,s-=A}e=C?E.subarray(0,D):E.slice(0,D);ea=oa(H,7);for(y=0;19>y;y++)ia[y]=ea[Ia[y]];for(P=19;4<P&&0===ia[P-1];P--);ua=pa(ea);B.a(N-257,5,u);B.a(O-1,5,u);B.a(P-4,4,u);for(y=0;y<P;y++)B.a(ia[y],3,u);y=0;for(va=e.length;y<va;y++)if(Q=
e[y],B.a(ua[Q],ea[Q],u),16<=Q){y++;switch(Q){case 16:fa=2;break;case 17:fa=3;break;case 18:fa=7;break;default:throw"invalid code: "+Q;}B.a(e[y],fa,u)}var ya=[sa,W],za=[ta,X],I,Aa,Z,la,Ba,Ca,Da,Ea;Ba=ya[0];Ca=ya[1];Da=za[0];Ea=za[1];I=0;for(Aa=J.length;I<Aa;++I)if(Z=J[I],B.a(Ba[Z],Ca[Z],u),256<Z)B.a(J[++I],J[++I],u),la=J[++I],B.a(Da[la],Ea[la],u),B.a(J[++I],J[++I],u);else if(256===Z)break;this.b=B.finish();this.c=this.b.length;break;default:throw"invalid compression type";}return this.b};
function qa(e,d){this.length=e;this.g=d}
var Fa=function(){function e(a){switch(u){case 3===a:return[257,a-3,0];case 4===a:return[258,a-4,0];case 5===a:return[259,a-5,0];case 6===a:return[260,a-6,0];case 7===a:return[261,a-7,0];case 8===a:return[262,a-8,0];case 9===a:return[263,a-9,0];case 10===a:return[264,a-10,0];case 12>=a:return[265,a-11,1];case 14>=a:return[266,a-13,1];case 16>=a:return[267,a-15,1];case 18>=a:return[268,a-17,1];case 22>=a:return[269,a-19,2];case 26>=a:return[270,a-23,2];case 30>=a:return[271,a-27,2];case 34>=a:return[272,
a-31,2];case 42>=a:return[273,a-35,3];case 50>=a:return[274,a-43,3];case 58>=a:return[275,a-51,3];case 66>=a:return[276,a-59,3];case 82>=a:return[277,a-67,4];case 98>=a:return[278,a-83,4];case 114>=a:return[279,a-99,4];case 130>=a:return[280,a-115,4];case 162>=a:return[281,a-131,5];case 194>=a:return[282,a-163,5];case 226>=a:return[283,a-195,5];case 257>=a:return[284,a-227,5];case 258===a:return[285,a-258,0];default:throw"invalid length: "+a;}}var d=[],c,f;for(c=3;258>=c;c++)f=e(c),d[c]=f[2]<<24|
f[1]<<16|f[0];return d}(),Ga=C?new Uint32Array(Fa):Fa;
function na(e,d){function c(a,c){var b=a.g,d=[],f=0,e;e=Ga[a.length];d[f++]=e&65535;d[f++]=e>>16&255;d[f++]=e>>24;var g;switch(u){case 1===b:g=[0,b-1,0];break;case 2===b:g=[1,b-2,0];break;case 3===b:g=[2,b-3,0];break;case 4===b:g=[3,b-4,0];break;case 6>=b:g=[4,b-5,1];break;case 8>=b:g=[5,b-7,1];break;case 12>=b:g=[6,b-9,2];break;case 16>=b:g=[7,b-13,2];break;case 24>=b:g=[8,b-17,3];break;case 32>=b:g=[9,b-25,3];break;case 48>=b:g=[10,b-33,4];break;case 64>=b:g=[11,b-49,4];break;case 96>=b:g=[12,b-
65,5];break;case 128>=b:g=[13,b-97,5];break;case 192>=b:g=[14,b-129,6];break;case 256>=b:g=[15,b-193,6];break;case 384>=b:g=[16,b-257,7];break;case 512>=b:g=[17,b-385,7];break;case 768>=b:g=[18,b-513,8];break;case 1024>=b:g=[19,b-769,8];break;case 1536>=b:g=[20,b-1025,9];break;case 2048>=b:g=[21,b-1537,9];break;case 3072>=b:g=[22,b-2049,10];break;case 4096>=b:g=[23,b-3073,10];break;case 6144>=b:g=[24,b-4097,11];break;case 8192>=b:g=[25,b-6145,11];break;case 12288>=b:g=[26,b-8193,12];break;case 16384>=
b:g=[27,b-12289,12];break;case 24576>=b:g=[28,b-16385,13];break;case 32768>=b:g=[29,b-24577,13];break;default:throw"invalid distance";}e=g;d[f++]=e[0];d[f++]=e[1];d[f++]=e[2];var k,m;k=0;for(m=d.length;k<m;++k)l[h++]=d[k];t[d[0]]++;w[d[3]]++;q=a.length+c-1;x=null}var f,a,b,k,m,g={},p,v,x,l=C?new Uint16Array(2*d.length):[],h=0,q=0,t=new (C?Uint32Array:Array)(286),w=new (C?Uint32Array:Array)(30),da=e.f,z;if(!C){for(b=0;285>=b;)t[b++]=0;for(b=0;29>=b;)w[b++]=0}t[256]=1;f=0;for(a=d.length;f<a;++f){b=
m=0;for(k=3;b<k&&f+b!==a;++b)m=m<<8|d[f+b];g[m]===n&&(g[m]=[]);p=g[m];if(!(0<q--)){for(;0<p.length&&32768<f-p[0];)p.shift();if(f+3>=a){x&&c(x,-1);b=0;for(k=a-f;b<k;++b)z=d[f+b],l[h++]=z,++t[z];break}0<p.length?(v=Ha(d,f,p),x?x.length<v.length?(z=d[f-1],l[h++]=z,++t[z],c(v,0)):c(x,-1):v.length<da?x=v:c(v,0)):x?c(x,-1):(z=d[f],l[h++]=z,++t[z])}p.push(f)}l[h++]=256;t[256]++;e.j=t;e.i=w;return C?l.subarray(0,h):l}
function Ha(e,d,c){var f,a,b=0,k,m,g,p,v=e.length;m=0;p=c.length;a:for(;m<p;m++){f=c[p-m-1];k=3;if(3<b){for(g=b;3<g;g--)if(e[f+g-1]!==e[d+g-1])continue a;k=b}for(;258>k&&d+k<v&&e[f+k]===e[d+k];)++k;k>b&&(a=f,b=k);if(258===k)break}return new qa(b,d-a)}
function oa(e,d){var c=e.length,f=new ja(572),a=new (C?Uint8Array:Array)(c),b,k,m,g,p;if(!C)for(g=0;g<c;g++)a[g]=0;for(g=0;g<c;++g)0<e[g]&&f.push(g,e[g]);b=Array(f.length/2);k=new (C?Uint32Array:Array)(f.length/2);if(1===b.length)return a[f.pop().index]=1,a;g=0;for(p=f.length/2;g<p;++g)b[g]=f.pop(),k[g]=b[g].value;m=Ja(k,k.length,d);g=0;for(p=b.length;g<p;++g)a[b[g].index]=m[g];return a}
function Ja(e,d,c){function f(a){var b=g[a][p[a]];b===d?(f(a+1),f(a+1)):--k[b];++p[a]}var a=new (C?Uint16Array:Array)(c),b=new (C?Uint8Array:Array)(c),k=new (C?Uint8Array:Array)(d),m=Array(c),g=Array(c),p=Array(c),v=(1<<c)-d,x=1<<c-1,l,h,q,t,w;a[c-1]=d;for(h=0;h<c;++h)v<x?b[h]=0:(b[h]=1,v-=x),v<<=1,a[c-2-h]=(a[c-1-h]/2|0)+d;a[0]=b[0];m[0]=Array(a[0]);g[0]=Array(a[0]);for(h=1;h<c;++h)a[h]>2*a[h-1]+b[h]&&(a[h]=2*a[h-1]+b[h]),m[h]=Array(a[h]),g[h]=Array(a[h]);for(l=0;l<d;++l)k[l]=c;for(q=0;q<a[c-1];++q)m[c-
1][q]=e[q],g[c-1][q]=q;for(l=0;l<c;++l)p[l]=0;1===b[c-1]&&(--k[0],++p[c-1]);for(h=c-2;0<=h;--h){t=l=0;w=p[h+1];for(q=0;q<a[h];q++)t=m[h+1][w]+m[h+1][w+1],t>e[l]?(m[h][q]=t,g[h][q]=d,w+=2):(m[h][q]=e[l],g[h][q]=l,++l);p[h]=0;1===b[h]&&f(h)}return k}
function pa(e){var d=new (C?Uint16Array:Array)(e.length),c=[],f=[],a=0,b,k,m,g;b=0;for(k=e.length;b<k;b++)c[e[b]]=(c[e[b]]|0)+1;b=1;for(k=16;b<=k;b++)f[b]=a,a+=c[b]|0,a<<=1;b=0;for(k=e.length;b<k;b++){a=f[e[b]];f[e[b]]+=1;m=d[b]=0;for(g=e[b];m<g;m++)d[b]=d[b]<<1|a&1,a>>>=1}return d};ba("Zlib.RawDeflate",ka);ba("Zlib.RawDeflate.prototype.compress",ka.prototype.h);var Ka={NONE:0,FIXED:1,DYNAMIC:ma},V,La,$,Ma;if(Object.keys)V=Object.keys(Ka);else for(La in V=[],$=0,Ka)V[$++]=La;$=0;for(Ma=V.length;$<Ma;++$)La=V[$],ba("Zlib.RawDeflate.CompressionType."+La,Ka[La]);}).call(this);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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