{"pagination": {"ListAggregatedUtterances": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListBotAliasReplicas": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListBotAliases": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListBotLocales": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListBotRecommendations": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListBotResourceGenerations": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListBotVersionReplicas": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListBotVersions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListBots": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListBuiltInIntents": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListBuiltInSlotTypes": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListCustomVocabularyItems": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListExports": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListImports": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListIntentMetrics": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListIntentStageMetrics": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListIntents": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListRecommendedIntents": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListSessionAnalyticsData": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListSessionMetrics": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListSlotTypes": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListSlots": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListTestExecutionResultItems": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListTestExecutions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListTestSetRecords": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListTestSets": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListUtteranceAnalyticsData": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListUtteranceMetrics": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}}}