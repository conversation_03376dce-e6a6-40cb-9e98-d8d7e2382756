{"name": "pdf-poppler", "version": "0.2.1", "description": "Convert PDF files into images using Poppler with promises. It achieves 10x faster performance compared to other PDF converters. \nPoppler library attached inside statically, so it has not require installation of poppler.", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/kb47/pdf-poppler.git"}, "keywords": ["pdf", "poppler", "jpeg", "jpg", "png", "tiff", "tif", "parse", "info", "image", "pdf2image", "pdf2img", "electron"], "author": "Khishigbaatar N. <<EMAIL>>", "license": "ISC"}