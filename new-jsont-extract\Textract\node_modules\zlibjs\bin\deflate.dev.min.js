/** @license zlib.js 2012 - imaya [ https://github.com/imaya/zlib.js ] The MIT License */(function() {'use strict';var n=void 0,w=!0,aa=this;function ba(f,d){var c=f.split("."),e=aa;!(c[0]in e)&&e.execScript&&e.execScript("var "+c[0]);for(var b;c.length&&(b=c.shift());)!c.length&&d!==n?e[b]=d:e=e[b]?e[b]:e[b]={}};var C="undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint16Array&&"undefined"!==typeof Uint32Array&&"undefined"!==typeof DataView;function K(f,d){this.index="number"===typeof d?d:0;this.e=0;this.buffer=f instanceof(C?Uint8Array:Array)?f:new (C?Uint8Array:Array)(32768);if(2*this.buffer.length<=this.index)throw Error("invalid index");this.buffer.length<=this.index&&ca(this)}function ca(f){var d=f.buffer,c,e=d.length,b=new (C?Uint8Array:Array)(e<<1);if(C)b.set(d);else for(c=0;c<e;++c)b[c]=d[c];return f.buffer=b}
K.prototype.b=function(f,d,c){var e=this.buffer,b=this.index,a=this.e,g=e[b],m;c&&1<d&&(f=8<d?(L[f&255]<<24|L[f>>>8&255]<<16|L[f>>>16&255]<<8|L[f>>>24&255])>>32-d:L[f]>>8-d);if(8>d+a)g=g<<d|f,a+=d;else for(m=0;m<d;++m)g=g<<1|f>>d-m-1&1,8===++a&&(a=0,e[b++]=L[g],g=0,b===e.length&&(e=ca(this)));e[b]=g;this.buffer=e;this.e=a;this.index=b};K.prototype.finish=function(){var f=this.buffer,d=this.index,c;0<this.e&&(f[d]<<=8-this.e,f[d]=L[f[d]],d++);C?c=f.subarray(0,d):(f.length=d,c=f);return c};
var da=new (C?Uint8Array:Array)(256),M;for(M=0;256>M;++M){for(var N=M,S=N,ea=7,N=N>>>1;N;N>>>=1)S<<=1,S|=N&1,--ea;da[M]=(S<<ea&255)>>>0}var L=da;function ia(f){this.buffer=new (C?Uint16Array:Array)(2*f);this.length=0}ia.prototype.getParent=function(f){return 2*((f-2)/4|0)};ia.prototype.push=function(f,d){var c,e,b=this.buffer,a;c=this.length;b[this.length++]=d;for(b[this.length++]=f;0<c;)if(e=this.getParent(c),b[c]>b[e])a=b[c],b[c]=b[e],b[e]=a,a=b[c+1],b[c+1]=b[e+1],b[e+1]=a,c=e;else break;return this.length};
ia.prototype.pop=function(){var f,d,c=this.buffer,e,b,a;d=c[0];f=c[1];this.length-=2;c[0]=c[this.length];c[1]=c[this.length+1];for(a=0;;){b=2*a+2;if(b>=this.length)break;b+2<this.length&&c[b+2]>c[b]&&(b+=2);if(c[b]>c[a])e=c[a],c[a]=c[b],c[b]=e,e=c[a+1],c[a+1]=c[b+1],c[b+1]=e;else break;a=b}return{index:f,value:d,length:this.length}};function ka(f,d){this.d=la;this.i=0;this.input=C&&f instanceof Array?new Uint8Array(f):f;this.c=0;d&&(d.lazy&&(this.i=d.lazy),"number"===typeof d.compressionType&&(this.d=d.compressionType),d.outputBuffer&&(this.a=C&&d.outputBuffer instanceof Array?new Uint8Array(d.outputBuffer):d.outputBuffer),"number"===typeof d.outputIndex&&(this.c=d.outputIndex));this.a||(this.a=new (C?Uint8Array:Array)(32768))}var la=2,na={NONE:0,h:1,g:la,n:3},T=[],U;
for(U=0;288>U;U++)switch(w){case 143>=U:T.push([U+48,8]);break;case 255>=U:T.push([U-144+400,9]);break;case 279>=U:T.push([U-256+0,7]);break;case 287>=U:T.push([U-280+192,8]);break;default:throw"invalid literal: "+U;}
ka.prototype.f=function(){var f,d,c,e,b=this.input;switch(this.d){case 0:c=0;for(e=b.length;c<e;){d=C?b.subarray(c,c+65535):b.slice(c,c+65535);c+=d.length;var a=d,g=c===e,m=n,k=n,p=n,t=n,u=n,l=this.a,h=this.c;if(C){for(l=new Uint8Array(this.a.buffer);l.length<=h+a.length+5;)l=new Uint8Array(l.length<<1);l.set(this.a)}m=g?1:0;l[h++]=m|0;k=a.length;p=~k+65536&65535;l[h++]=k&255;l[h++]=k>>>8&255;l[h++]=p&255;l[h++]=p>>>8&255;if(C)l.set(a,h),h+=a.length,l=l.subarray(0,h);else{t=0;for(u=a.length;t<u;++t)l[h++]=
a[t];l.length=h}this.c=h;this.a=l}break;case 1:var q=new K(C?new Uint8Array(this.a.buffer):this.a,this.c);q.b(1,1,w);q.b(1,2,w);var s=oa(this,b),x,fa,z;x=0;for(fa=s.length;x<fa;x++)if(z=s[x],K.prototype.b.apply(q,T[z]),256<z)q.b(s[++x],s[++x],w),q.b(s[++x],5),q.b(s[++x],s[++x],w);else if(256===z)break;this.a=q.finish();this.c=this.a.length;break;case la:var B=new K(C?new Uint8Array(this.a.buffer):this.a,this.c),ta,J,O,P,Q,La=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],X,ua,Y,va,ga,ja=Array(19),
wa,R,ha,y,xa;ta=la;B.b(1,1,w);B.b(ta,2,w);J=oa(this,b);X=pa(this.m,15);ua=qa(X);Y=pa(this.l,7);va=qa(Y);for(O=286;257<O&&0===X[O-1];O--);for(P=30;1<P&&0===Y[P-1];P--);var ya=O,za=P,F=new (C?Uint32Array:Array)(ya+za),r,G,v,Z,E=new (C?Uint32Array:Array)(316),D,A,H=new (C?Uint8Array:Array)(19);for(r=G=0;r<ya;r++)F[G++]=X[r];for(r=0;r<za;r++)F[G++]=Y[r];if(!C){r=0;for(Z=H.length;r<Z;++r)H[r]=0}r=D=0;for(Z=F.length;r<Z;r+=G){for(G=1;r+G<Z&&F[r+G]===F[r];++G);v=G;if(0===F[r])if(3>v)for(;0<v--;)E[D++]=0,
H[0]++;else for(;0<v;)A=138>v?v:138,A>v-3&&A<v&&(A=v-3),10>=A?(E[D++]=17,E[D++]=A-3,H[17]++):(E[D++]=18,E[D++]=A-11,H[18]++),v-=A;else if(E[D++]=F[r],H[F[r]]++,v--,3>v)for(;0<v--;)E[D++]=F[r],H[F[r]]++;else for(;0<v;)A=6>v?v:6,A>v-3&&A<v&&(A=v-3),E[D++]=16,E[D++]=A-3,H[16]++,v-=A}f=C?E.subarray(0,D):E.slice(0,D);ga=pa(H,7);for(y=0;19>y;y++)ja[y]=ga[La[y]];for(Q=19;4<Q&&0===ja[Q-1];Q--);wa=qa(ga);B.b(O-257,5,w);B.b(P-1,5,w);B.b(Q-4,4,w);for(y=0;y<Q;y++)B.b(ja[y],3,w);y=0;for(xa=f.length;y<xa;y++)if(R=
f[y],B.b(wa[R],ga[R],w),16<=R){y++;switch(R){case 16:ha=2;break;case 17:ha=3;break;case 18:ha=7;break;default:throw"invalid code: "+R;}B.b(f[y],ha,w)}var Aa=[ua,X],Ba=[va,Y],I,Ca,$,ma,Da,Ea,Fa,Ga;Da=Aa[0];Ea=Aa[1];Fa=Ba[0];Ga=Ba[1];I=0;for(Ca=J.length;I<Ca;++I)if($=J[I],B.b(Da[$],Ea[$],w),256<$)B.b(J[++I],J[++I],w),ma=J[++I],B.b(Fa[ma],Ga[ma],w),B.b(J[++I],J[++I],w);else if(256===$)break;this.a=B.finish();this.c=this.a.length;break;default:throw"invalid compression type";}return this.a};
function ra(f,d){this.length=f;this.k=d}
var sa=function(){function f(b){switch(w){case 3===b:return[257,b-3,0];case 4===b:return[258,b-4,0];case 5===b:return[259,b-5,0];case 6===b:return[260,b-6,0];case 7===b:return[261,b-7,0];case 8===b:return[262,b-8,0];case 9===b:return[263,b-9,0];case 10===b:return[264,b-10,0];case 12>=b:return[265,b-11,1];case 14>=b:return[266,b-13,1];case 16>=b:return[267,b-15,1];case 18>=b:return[268,b-17,1];case 22>=b:return[269,b-19,2];case 26>=b:return[270,b-23,2];case 30>=b:return[271,b-27,2];case 34>=b:return[272,
b-31,2];case 42>=b:return[273,b-35,3];case 50>=b:return[274,b-43,3];case 58>=b:return[275,b-51,3];case 66>=b:return[276,b-59,3];case 82>=b:return[277,b-67,4];case 98>=b:return[278,b-83,4];case 114>=b:return[279,b-99,4];case 130>=b:return[280,b-115,4];case 162>=b:return[281,b-131,5];case 194>=b:return[282,b-163,5];case 226>=b:return[283,b-195,5];case 257>=b:return[284,b-227,5];case 258===b:return[285,b-258,0];default:throw"invalid length: "+b;}}var d=[],c,e;for(c=3;258>=c;c++)e=f(c),d[c]=e[2]<<24|
e[1]<<16|e[0];return d}(),Ha=C?new Uint32Array(sa):sa;
function oa(f,d){function c(b,c){var a=b.k,d=[],e=0,f;f=Ha[b.length];d[e++]=f&65535;d[e++]=f>>16&255;d[e++]=f>>24;var g;switch(w){case 1===a:g=[0,a-1,0];break;case 2===a:g=[1,a-2,0];break;case 3===a:g=[2,a-3,0];break;case 4===a:g=[3,a-4,0];break;case 6>=a:g=[4,a-5,1];break;case 8>=a:g=[5,a-7,1];break;case 12>=a:g=[6,a-9,2];break;case 16>=a:g=[7,a-13,2];break;case 24>=a:g=[8,a-17,3];break;case 32>=a:g=[9,a-25,3];break;case 48>=a:g=[10,a-33,4];break;case 64>=a:g=[11,a-49,4];break;case 96>=a:g=[12,a-
65,5];break;case 128>=a:g=[13,a-97,5];break;case 192>=a:g=[14,a-129,6];break;case 256>=a:g=[15,a-193,6];break;case 384>=a:g=[16,a-257,7];break;case 512>=a:g=[17,a-385,7];break;case 768>=a:g=[18,a-513,8];break;case 1024>=a:g=[19,a-769,8];break;case 1536>=a:g=[20,a-1025,9];break;case 2048>=a:g=[21,a-1537,9];break;case 3072>=a:g=[22,a-2049,10];break;case 4096>=a:g=[23,a-3073,10];break;case 6144>=a:g=[24,a-4097,11];break;case 8192>=a:g=[25,a-6145,11];break;case 12288>=a:g=[26,a-8193,12];break;case 16384>=
a:g=[27,a-12289,12];break;case 24576>=a:g=[28,a-16385,13];break;case 32768>=a:g=[29,a-24577,13];break;default:throw"invalid distance";}f=g;d[e++]=f[0];d[e++]=f[1];d[e++]=f[2];var k,m;k=0;for(m=d.length;k<m;++k)l[h++]=d[k];s[d[0]]++;x[d[3]]++;q=b.length+c-1;u=null}var e,b,a,g,m,k={},p,t,u,l=C?new Uint16Array(2*d.length):[],h=0,q=0,s=new (C?Uint32Array:Array)(286),x=new (C?Uint32Array:Array)(30),fa=f.i,z;if(!C){for(a=0;285>=a;)s[a++]=0;for(a=0;29>=a;)x[a++]=0}s[256]=1;e=0;for(b=d.length;e<b;++e){a=
m=0;for(g=3;a<g&&e+a!==b;++a)m=m<<8|d[e+a];k[m]===n&&(k[m]=[]);p=k[m];if(!(0<q--)){for(;0<p.length&&32768<e-p[0];)p.shift();if(e+3>=b){u&&c(u,-1);a=0;for(g=b-e;a<g;++a)z=d[e+a],l[h++]=z,++s[z];break}0<p.length?(t=Ia(d,e,p),u?u.length<t.length?(z=d[e-1],l[h++]=z,++s[z],c(t,0)):c(u,-1):t.length<fa?u=t:c(t,0)):u?c(u,-1):(z=d[e],l[h++]=z,++s[z])}p.push(e)}l[h++]=256;s[256]++;f.m=s;f.l=x;return C?l.subarray(0,h):l}
function Ia(f,d,c){var e,b,a=0,g,m,k,p,t=f.length;m=0;p=c.length;a:for(;m<p;m++){e=c[p-m-1];g=3;if(3<a){for(k=a;3<k;k--)if(f[e+k-1]!==f[d+k-1])continue a;g=a}for(;258>g&&d+g<t&&f[e+g]===f[d+g];)++g;g>a&&(b=e,a=g);if(258===g)break}return new ra(a,d-b)}
function pa(f,d){var c=f.length,e=new ia(572),b=new (C?Uint8Array:Array)(c),a,g,m,k,p;if(!C)for(k=0;k<c;k++)b[k]=0;for(k=0;k<c;++k)0<f[k]&&e.push(k,f[k]);a=Array(e.length/2);g=new (C?Uint32Array:Array)(e.length/2);if(1===a.length)return b[e.pop().index]=1,b;k=0;for(p=e.length/2;k<p;++k)a[k]=e.pop(),g[k]=a[k].value;m=Ja(g,g.length,d);k=0;for(p=a.length;k<p;++k)b[a[k].index]=m[k];return b}
function Ja(f,d,c){function e(a){var b=k[a][p[a]];b===d?(e(a+1),e(a+1)):--g[b];++p[a]}var b=new (C?Uint16Array:Array)(c),a=new (C?Uint8Array:Array)(c),g=new (C?Uint8Array:Array)(d),m=Array(c),k=Array(c),p=Array(c),t=(1<<c)-d,u=1<<c-1,l,h,q,s,x;b[c-1]=d;for(h=0;h<c;++h)t<u?a[h]=0:(a[h]=1,t-=u),t<<=1,b[c-2-h]=(b[c-1-h]/2|0)+d;b[0]=a[0];m[0]=Array(b[0]);k[0]=Array(b[0]);for(h=1;h<c;++h)b[h]>2*b[h-1]+a[h]&&(b[h]=2*b[h-1]+a[h]),m[h]=Array(b[h]),k[h]=Array(b[h]);for(l=0;l<d;++l)g[l]=c;for(q=0;q<b[c-1];++q)m[c-
1][q]=f[q],k[c-1][q]=q;for(l=0;l<c;++l)p[l]=0;1===a[c-1]&&(--g[0],++p[c-1]);for(h=c-2;0<=h;--h){s=l=0;x=p[h+1];for(q=0;q<b[h];q++)s=m[h+1][x]+m[h+1][x+1],s>f[l]?(m[h][q]=s,k[h][q]=d,x+=2):(m[h][q]=f[l],k[h][q]=l,++l);p[h]=0;1===a[h]&&e(h)}return g}
function qa(f){var d=new (C?Uint16Array:Array)(f.length),c=[],e=[],b=0,a,g,m,k;a=0;for(g=f.length;a<g;a++)c[f[a]]=(c[f[a]]|0)+1;a=1;for(g=16;a<=g;a++)e[a]=b,b+=c[a]|0,b<<=1;a=0;for(g=f.length;a<g;a++){b=e[f[a]];e[f[a]]+=1;m=d[a]=0;for(k=f[a];m<k;m++)d[a]=d[a]<<1|b&1,b>>>=1}return d};function Ka(f,d){this.input=f;this.a=new (C?Uint8Array:Array)(32768);this.d=V.g;var c={},e;if((d||!(d={}))&&"number"===typeof d.compressionType)this.d=d.compressionType;for(e in d)c[e]=d[e];c.outputBuffer=this.a;this.j=new ka(this.input,c)}var V=na;
Ka.prototype.f=function(){var f,d,c,e,b,a,g=0;a=this.a;switch(8){case 8:f=Math.LOG2E*Math.log(32768)-8;break;default:throw Error("invalid compression method");}d=f<<4|8;a[g++]=d;switch(8){case 8:switch(this.d){case V.NONE:e=0;break;case V.h:e=1;break;case V.g:e=2;break;default:throw Error("unsupported compression type");}break;default:throw Error("invalid compression method");}c=e<<6|0;a[g++]=c|31-(256*d+c)%31;var m=this.input;if("string"===typeof m){var k=m.split(""),p,t;p=0;for(t=k.length;p<t;p++)k[p]=
(k[p].charCodeAt(0)&255)>>>0;m=k}for(var u=1,l=0,h=m.length,q,s=0;0<h;){q=1024<h?1024:h;h-=q;do u+=m[s++],l+=u;while(--q);u%=65521;l%=65521}b=(l<<16|u)>>>0;this.j.c=g;a=this.j.f();g=a.length;C&&(a=new Uint8Array(a.buffer),a.length<=g+4&&(this.a=new Uint8Array(a.length+4),this.a.set(a),a=this.a),a=a.subarray(0,g+4));a[g++]=b>>24&255;a[g++]=b>>16&255;a[g++]=b>>8&255;a[g++]=b&255;return a};ba("Zlib.Deflate",Ka);ba("Zlib.Deflate.compress",function(f,d){return(new Ka(f,d)).f()});ba("Zlib.Deflate.prototype.compress",Ka.prototype.f);var Ma={NONE:V.NONE,FIXED:V.h,DYNAMIC:V.g},Na,Oa,W,Pa;if(Object.keys)Na=Object.keys(Ma);else for(Oa in Na=[],W=0,Ma)Na[W++]=Oa;W=0;for(Pa=Na.length;W<Pa;++W)Oa=Na[W],ba("Zlib.Deflate.CompressionType."+Oa,Ma[Oa]);}).call(this);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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