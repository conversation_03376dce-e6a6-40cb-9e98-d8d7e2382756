{"name": "textract", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "Document extraction service with multiple file upload UI", "dependencies": {"@aws-sdk/client-s3": "^3.882.0", "@aws-sdk/client-textract": "^3.882.0", "aws-sdk": "^2.1692.0", "cors": "^2.8.5", "dotenv": "^17.2.2", "express": "^4.18.2", "multer": "^1.4.5-lts.1", "pdf-parse": "^1.1.1", "pdf-poppler": "^0.2.1", "tesseract.js": "^6.0.1"}}