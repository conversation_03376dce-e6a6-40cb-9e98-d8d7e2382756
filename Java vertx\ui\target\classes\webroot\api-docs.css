/* API Documentation Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f8f9fa;
}

.container {
    display: grid;
    grid-template-columns: 250px 1fr;
    grid-template-rows: auto 1fr;
    grid-template-areas: 
        "header header"
        "sidebar content";
    min-height: 100vh;
}

/* Header */
.header {
    grid-area: header;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.api-status {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
}

.api-status.online {
    background-color: #28a745;
    color: white;
}

.api-status.offline {
    background-color: #dc3545;
    color: white;
}

.api-status.checking {
    background-color: #ffc107;
    color: #333;
}

/* Sidebar Navigation */
.sidebar {
    grid-area: sidebar;
    background: white;
    border-right: 1px solid #e9ecef;
    padding: 2rem 0;
    position: sticky;
    top: 0;
    height: 100vh;
    overflow-y: auto;
}

.nav-menu {
    list-style: none;
}

.nav-menu li {
    margin-bottom: 0.5rem;
}

.nav-menu a {
    display: block;
    padding: 0.75rem 1.5rem;
    color: #666;
    text-decoration: none;
    border-left: 3px solid transparent;
    transition: all 0.3s ease;
}

.nav-menu a:hover,
.nav-menu a.active {
    color: #667eea;
    background-color: #f8f9ff;
    border-left-color: #667eea;
}

/* Main Content */
.content {
    grid-area: content;
    padding: 2rem;
    max-width: 1000px;
    margin: 0 auto;
}

.section {
    margin-bottom: 3rem;
    scroll-margin-top: 2rem;
}

.section h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
    color: #333;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

/* Cards and Boxes */
.info-card {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
}

.warning-box {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
    color: #856404;
}

/* Feature Grid */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.feature-item {
    background: #f8f9ff;
    padding: 1.5rem;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.feature-item h4 {
    color: #667eea;
    margin-bottom: 0.5rem;
}

/* Endpoint Cards */
.endpoint-summary {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.endpoint-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.endpoint-detail {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.endpoint-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

/* HTTP Methods */
.method {
    padding: 0.25rem 0.75rem;
    border-radius: 4px;
    font-weight: bold;
    font-size: 0.8rem;
    text-transform: uppercase;
}

.method.get {
    background-color: #28a745;
    color: white;
}

.method.post {
    background-color: #007bff;
    color: white;
}

.method.put {
    background-color: #ffc107;
    color: #333;
}

.method.delete {
    background-color: #dc3545;
    color: white;
}

.path {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #333;
}

.description {
    color: #666;
    flex: 1;
}

/* Code Blocks */
.code-block {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    margin: 1rem 0;
    overflow-x: auto;
}

.code-block pre {
    margin: 0;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Tables */
.params-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1rem 0;
    background: white;
    border-radius: 6px;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.params-table th,
.params-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e9ecef;
}

.params-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.params-table tr:hover {
    background-color: #f8f9ff;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #667eea;
    color: white;
}

.btn-primary:hover {
    background-color: #5a6fd8;
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

/* Test Results */
.test-button-container {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.test-result {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    display: none;
}

.test-result.success {
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    display: block;
}

.test-result.error {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        grid-template-columns: 1fr;
        grid-template-areas: 
            "header"
            "content";
    }
    
    .sidebar {
        display: none;
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }
    
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .endpoint-card {
        flex-direction: column;
        align-items: flex-start;
    }
}
