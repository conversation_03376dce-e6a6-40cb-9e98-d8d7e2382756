%!PS-Adobe-3.0 Resource-CMap
%%DocumentNeededResources: ProcSet (CIDInit)
%%IncludeResource: ProcSet (CIDInit)
%%BeginResource: CMap (Adobe-CNS1-H-CID)
%%Title: (Adobe-CNS1-H-CID Adobe CNS1 0)
%%Version: 2.002
%%Copyright: -----------------------------------------------------------
%%Copyright: Copyright 1990-2009 Adobe Systems Incorporated.
%%Copyright: All rights reserved.
%%Copyright:
%%Copyright: Redistribution and use in source and binary forms, with or
%%Copyright: without modification, are permitted provided that the
%%Copyright: following conditions are met:
%%Copyright:
%%Copyright: Redistributions of source code must retain the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer.
%%Copyright:
%%Copyright: Redistributions in binary form must reproduce the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer in the documentation and/or other materials
%%Copyright: provided with the distribution. 
%%Copyright:
%%Copyright: Neither the name of Adobe Systems Incorporated nor the names
%%Copyright: of its contributors may be used to endorse or promote
%%Copyright: products derived from this software without specific prior
%%Copyright: written permission. 
%%Copyright:
%%Copyright: THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND
%%Copyright: CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
%%Copyright: INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
%%Copyright: MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
%%Copyright: DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
%%Copyright: CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
%%Copyright: SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
%%Copyright: NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
%%Copyright: LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
%%Copyright: HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
%%Copyright: CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
%%Copyright: OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
%%Copyright: SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
%%Copyright: -----------------------------------------------------------
%%EndComments

/CIDInit /ProcSet findresource begin

12 dict begin

begincmap

/CIDSystemInfo 3 dict dup begin
  /Registry (Adobe) def
  /Ordering (Adobe_CNS1_H_CID) def
  /Supplement 0 def
end def

/CMapName /Adobe-CNS1-H-CID def
/CMapVersion 2.002 def
/CMapType 1 def

/UIDOffset 0 def

/WMode 0 def

1 begincodespacerange
  <0000>   <FFFF>
endcodespacerange

% Map CID to full width space and then map all CIDs to font 0 - for Supplement 0
0 usefont
2 begincidrange
<0000> <FFFF> 0
<0000> <0000> 99
endcidrange

% Map CIDs 1 thru 95 to Ascii codes 0x20 - 0x7F in fontID 1.
1 usefont
1 beginbfrange
<0001> <005F> <20>
endbfrange

% Map CIDs 14099 - 17407 to fontID 2 for Supplement 1.
2 usefont
2 begincidrange
<3713> <37FF> 14099
<3800> <43FF> 14336
endcidrange

% Map CIDs 17408 to 17600 to fontID 3 for Supplement 2.
3 usefont
1 begincidrange
<4400> <44C0> 17408
endcidrange

% Map CIDs 17601 to fontID 4 for Supplement 3.
% Also map CIDs upto 65535 to this fontID.
4 usefont
2 begincidrange
<44C1> <44FF> 17601
<4500> <FFFF> 17664
endcidrange


endcmap
CMapName currentdict /CMap defineresource pop
end
end

%%EndResource
%%EOF
