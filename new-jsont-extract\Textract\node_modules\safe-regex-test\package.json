{"name": "safe-regex-test", "version": "1.1.0", "description": "Give a regex, get a robust predicate function that tests it against a string.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "tests-only": "nyc tape test", "test": "npm run tests-only", "posttest": "npx npm@'>= 10.2' audit --production"}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/safe-regex-test.git"}, "keywords": ["regex", "regexp", "test", "tester", "safe", "robust", "exec"], "author": "<PERSON> <<EMAIL>>", "funding": {"url": "https://github.com/sponsors/ljharb"}, "license": "MIT", "bugs": {"url": "https://github.com/ljharb/safe-regex-test/issues"}, "homepage": "https://github.com/ljharb/safe-regex-test#readme", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}}