
var TesseractCore = (() => {
  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;
  if (typeof __filename !== 'undefined') _scriptDir = _scriptDir || __filename;
  return (
function(TesseractCore = {})  {

var b;b||(b=typeof TesseractCore !== 'undefined' ? TesseractCore : {});var aa,ba;b.ready=new Promise((a,c)=>{aa=a;ba=c});var ca=Object.assign({},b),da="./this.program",ea=(a,c)=>{throw c;},fa="object"==typeof window,ha="function"==typeof importScripts,ia="object"==typeof process&&"object"==typeof process.versions&&"string"==typeof process.versions.node,f="",ja,ka,la;
if(ia){var fs=require("fs"),ma=require("path");f=ha?ma.dirname(f)+"/":__dirname+"/";ja=(a,c)=>{a=a.startsWith("file://")?new URL(a):ma.normalize(a);return fs.readFileSync(a,c?void 0:"utf8")};la=a=>{a=ja(a,!0);a.buffer||(a=new Uint8Array(a));return a};ka=(a,c,d,e=!0)=>{a=a.startsWith("file://")?new URL(a):ma.normalize(a);fs.readFile(a,e?void 0:"utf8",(g,h)=>{g?d(g):c(e?h.buffer:h)})};!b.thisProgram&&1<process.argv.length&&(da=process.argv[1].replace(/\\/g,"/"));process.argv.slice(2);ea=(a,c)=>{process.exitCode=
a;throw c;};b.inspect=()=>"[Emscripten Module object]"}else if(fa||ha)ha?f=self.location.href:"undefined"!=typeof document&&document.currentScript&&(f=document.currentScript.src),_scriptDir&&(f=_scriptDir),0!==f.indexOf("blob:")?f=f.substr(0,f.replace(/[?#].*/,"").lastIndexOf("/")+1):f="",ja=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.send(null);return c.responseText},ha&&(la=a=>{var c=new XMLHttpRequest;c.open("GET",a,!1);c.responseType="arraybuffer";c.send(null);return new Uint8Array(c.response)}),
ka=(a,c,d)=>{var e=new XMLHttpRequest;e.open("GET",a,!0);e.responseType="arraybuffer";e.onload=()=>{200==e.status||0==e.status&&e.response?c(e.response):d()};e.onerror=d;e.send(null)};var na=b.print||console.log.bind(console),n=b.printErr||console.warn.bind(console);Object.assign(b,ca);ca=null;b.thisProgram&&(da=b.thisProgram);b.quit&&(ea=b.quit);var oa;b.wasmBinary&&(oa=b.wasmBinary);var noExitRuntime=b.noExitRuntime||!0;"object"!=typeof WebAssembly&&p("no native wasm support detected");
var pa,ra=!1,r,sa,ta,u,x,ua,va;function wa(){var a=pa.buffer;b.HEAP8=r=new Int8Array(a);b.HEAP16=ta=new Int16Array(a);b.HEAP32=u=new Int32Array(a);b.HEAPU8=sa=new Uint8Array(a);b.HEAPU16=new Uint16Array(a);b.HEAPU32=x=new Uint32Array(a);b.HEAPF32=ua=new Float32Array(a);b.HEAPF64=va=new Float64Array(a)}var xa,ya=[],za=[],Aa=[],Ba=!1;function Ca(){var a=b.preRun.shift();ya.unshift(a)}var Da=0,Ea=null,Fa=null;function Ga(){Da++;b.monitorRunDependencies&&b.monitorRunDependencies(Da)}
function Ha(){Da--;b.monitorRunDependencies&&b.monitorRunDependencies(Da);if(0==Da&&(null!==Ea&&(clearInterval(Ea),Ea=null),Fa)){var a=Fa;Fa=null;a()}}function p(a){if(b.onAbort)b.onAbort(a);a="Aborted("+a+")";n(a);ra=!0;a=new WebAssembly.RuntimeError(a+". Build with -sASSERTIONS for more info.");ba(a);throw a;}function Ia(a){return a.startsWith("data:application/octet-stream;base64,")}var Ja;Ja="tesseract-core-lstm.wasm";if(!Ia(Ja)){var Ka=Ja;Ja=b.locateFile?b.locateFile(Ka,f):f+Ka}
function La(a){try{if(a==Ja&&oa)return new Uint8Array(oa);if(la)return la(a);throw"both async and sync fetching of the wasm failed";}catch(c){p(c)}}function Ma(a){if(!oa&&(fa||ha)){if("function"==typeof fetch&&!a.startsWith("file://"))return fetch(a,{credentials:"same-origin"}).then(c=>{if(!c.ok)throw"failed to load wasm binary file at '"+a+"'";return c.arrayBuffer()}).catch(()=>La(a));if(ka)return new Promise((c,d)=>{ka(a,e=>c(new Uint8Array(e)),d)})}return Promise.resolve().then(()=>La(a))}
function Na(a,c,d){return Ma(a).then(e=>WebAssembly.instantiate(e,c)).then(e=>e).then(d,e=>{n("failed to asynchronously prepare wasm: "+e);p(e)})}
function Oa(a,c){var d=Ja;return oa||"function"!=typeof WebAssembly.instantiateStreaming||Ia(d)||d.startsWith("file://")||ia||"function"!=typeof fetch?Na(d,a,c):fetch(d,{credentials:"same-origin"}).then(e=>WebAssembly.instantiateStreaming(e,a).then(c,function(g){n("wasm streaming compile failed: "+g);n("falling back to ArrayBuffer instantiation");return Na(d,a,c)}))}var y,z,Pa={419340:a=>{b.TesseractProgress&&b.TesseractProgress(a)},419409:a=>{b.TesseractProgress&&b.TesseractProgress(a)}};
function Qa(a){this.name="ExitStatus";this.message="Program terminated with exit("+a+")";this.status=a}function Ra(a){for(;0<a.length;)a.shift()(b)}function Sa(a){for(var c=0,d=0;d<a.length;++d){var e=a.charCodeAt(d);127>=e?c++:2047>=e?c+=2:55296<=e&&57343>=e?(c+=4,++d):c+=3}return c}
function Ta(a,c,d,e){if(!(0<e))return 0;var g=d;e=d+e-1;for(var h=0;h<a.length;++h){var k=a.charCodeAt(h);if(55296<=k&&57343>=k){var m=a.charCodeAt(++h);k=65536+((k&1023)<<10)|m&1023}if(127>=k){if(d>=e)break;c[d++]=k}else{if(2047>=k){if(d+1>=e)break;c[d++]=192|k>>6}else{if(65535>=k){if(d+2>=e)break;c[d++]=224|k>>12}else{if(d+3>=e)break;c[d++]=240|k>>18;c[d++]=128|k>>12&63}c[d++]=128|k>>6&63}c[d++]=128|k&63}}c[d]=0;return d-g}var Ua="undefined"!=typeof TextDecoder?new TextDecoder("utf8"):void 0;
function Va(a,c){for(var d=c+NaN,e=c;a[e]&&!(e>=d);)++e;if(16<e-c&&a.buffer&&Ua)return Ua.decode(a.subarray(c,e));for(d="";c<e;){var g=a[c++];if(g&128){var h=a[c++]&63;if(192==(g&224))d+=String.fromCharCode((g&31)<<6|h);else{var k=a[c++]&63;g=224==(g&240)?(g&15)<<12|h<<6|k:(g&7)<<18|h<<12|k<<6|a[c++]&63;65536>g?d+=String.fromCharCode(g):(g-=65536,d+=String.fromCharCode(55296|g>>10,56320|g&1023))}}else d+=String.fromCharCode(g)}return d}function A(a){return a?Va(sa,a):""}
function Wa(a,c="i8"){c.endsWith("*")&&(c="*");switch(c){case "i1":return r[a>>0];case "i8":return r[a>>0];case "i16":return ta[a>>1];case "i32":return u[a>>2];case "i64":return u[a>>2];case "float":return ua[a>>2];case "double":return va[a>>3];case "*":return x[a>>2];default:p("invalid type for getValue: "+c)}}
function Xa(a,c,d="i8"){d.endsWith("*")&&(d="*");switch(d){case "i1":r[a>>0]=c;break;case "i8":r[a>>0]=c;break;case "i16":ta[a>>1]=c;break;case "i32":u[a>>2]=c;break;case "i64":z=[c>>>0,(y=c,1<=+Math.abs(y)?0<y?+Math.floor(y/4294967296)>>>0:~~+Math.ceil((y-+(~~y>>>0))/4294967296)>>>0:0)];u[a>>2]=z[0];u[a+4>>2]=z[1];break;case "float":ua[a>>2]=c;break;case "double":va[a>>3]=c;break;case "*":x[a>>2]=c;break;default:p("invalid type for setValue: "+d)}}
function Ya(a){this.Hf=a-24;this.xh=function(c){x[this.Hf+4>>2]=c};this.Hg=function(c){x[this.Hf+8>>2]=c};this.hg=function(c,d){this.Uf();this.xh(c);this.Hg(d)};this.Uf=function(){x[this.Hf+16>>2]=0}}
var Za=0,$a=0,ab=(a,c)=>{for(var d=0,e=a.length-1;0<=e;e--){var g=a[e];"."===g?a.splice(e,1):".."===g?(a.splice(e,1),d++):d&&(a.splice(e,1),d--)}if(c)for(;d;d--)a.unshift("..");return a},bb=a=>{var c="/"===a.charAt(0),d="/"===a.substr(-1);(a=ab(a.split("/").filter(e=>!!e),!c).join("/"))||c||(a=".");a&&d&&(a+="/");return(c?"/":"")+a},cb=a=>{var c=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/.exec(a).slice(1);a=c[0];c=c[1];if(!a&&!c)return".";c&&(c=c.substr(0,c.length-1));return a+
c},db=a=>{if("/"===a)return"/";a=bb(a);a=a.replace(/\/$/,"");var c=a.lastIndexOf("/");return-1===c?a:a.substr(c+1)},eb=(a,c)=>bb(a+"/"+c);function fb(){if("object"==typeof crypto&&"function"==typeof crypto.getRandomValues)return d=>crypto.getRandomValues(d);if(ia)try{var a=require("crypto");if(a.randomFillSync)return d=>a.randomFillSync(d);var c=a.randomBytes;return d=>(d.set(c(d.byteLength)),d)}catch(d){}p("initRandomDevice")}function gb(a){return(gb=fb())(a)}
function hb(){for(var a="",c=!1,d=arguments.length-1;-1<=d&&!c;d--){c=0<=d?arguments[d]:B.cwd();if("string"!=typeof c)throw new TypeError("Arguments to path.resolve must be strings");if(!c)return"";a=c+"/"+a;c="/"===c.charAt(0)}a=ab(a.split("/").filter(e=>!!e),!c).join("/");return(c?"/":"")+a||"."}
var ib=(a,c)=>{function d(k){for(var m=0;m<k.length&&""===k[m];m++);for(var v=k.length-1;0<=v&&""===k[v];v--);return m>v?[]:k.slice(m,v-m+1)}a=hb(a).substr(1);c=hb(c).substr(1);a=d(a.split("/"));c=d(c.split("/"));for(var e=Math.min(a.length,c.length),g=e,h=0;h<e;h++)if(a[h]!==c[h]){g=h;break}e=[];for(h=g;h<a.length;h++)e.push("..");e=e.concat(c.slice(g));return e.join("/")};function jb(a,c){var d=Array(Sa(a)+1);a=Ta(a,d,0,d.length);c&&(d.length=a);return d}var kb=[];
function lb(a,c){kb[a]={input:[],output:[],rg:c};B.bh(a,mb)}
var mb={open:function(a){var c=kb[a.node.rdev];if(!c)throw new B.If(43);a.tty=c;a.seekable=!1},close:function(a){a.tty.rg.fsync(a.tty)},fsync:function(a){a.tty.rg.fsync(a.tty)},read:function(a,c,d,e){if(!a.tty||!a.tty.rg.ph)throw new B.If(60);for(var g=0,h=0;h<e;h++){try{var k=a.tty.rg.ph(a.tty)}catch(m){throw new B.If(29);}if(void 0===k&&0===g)throw new B.If(6);if(null===k||void 0===k)break;g++;c[d+h]=k}g&&(a.node.timestamp=Date.now());return g},write:function(a,c,d,e){if(!a.tty||!a.tty.rg.Zg)throw new B.If(60);
try{for(var g=0;g<e;g++)a.tty.rg.Zg(a.tty,c[d+g])}catch(h){throw new B.If(29);}e&&(a.node.timestamp=Date.now());return g}},nb={ph:function(a){if(!a.input.length){var c=null;if(ia){var d=Buffer.alloc(256),e=0;try{e=fs.readSync(process.stdin.fd,d,0,256,-1)}catch(g){if(g.toString().includes("EOF"))e=0;else throw g;}0<e?c=d.slice(0,e).toString("utf-8"):c=null}else"undefined"!=typeof window&&"function"==typeof window.prompt?(c=window.prompt("Input: "),null!==c&&(c+="\n")):"function"==typeof readline&&
(c=readline(),null!==c&&(c+="\n"));if(!c)return null;a.input=jb(c,!0)}return a.input.shift()},Zg:function(a,c){null===c||10===c?(na(Va(a.output,0)),a.output=[]):0!=c&&a.output.push(c)},fsync:function(a){a.output&&0<a.output.length&&(na(Va(a.output,0)),a.output=[])}},ob={Zg:function(a,c){null===c||10===c?(n(Va(a.output,0)),a.output=[]):0!=c&&a.output.push(c)},fsync:function(a){a.output&&0<a.output.length&&(n(Va(a.output,0)),a.output=[])}},C={$f:null,Rf:function(){return C.createNode(null,"/",16895,
0)},createNode:function(a,c,d,e){if(B.ji(d)||B.isFIFO(d))throw new B.If(63);C.$f||(C.$f={dir:{node:{Xf:C.Jf.Xf,Tf:C.Jf.Tf,lookup:C.Jf.lookup,dg:C.Jf.dg,rename:C.Jf.rename,unlink:C.Jf.unlink,rmdir:C.Jf.rmdir,readdir:C.Jf.readdir,symlink:C.Jf.symlink},stream:{Yf:C.Lf.Yf}},file:{node:{Xf:C.Jf.Xf,Tf:C.Jf.Tf},stream:{Yf:C.Lf.Yf,read:C.Lf.read,write:C.Lf.write,sg:C.Lf.sg,kg:C.Lf.kg,qg:C.Lf.qg}},link:{node:{Xf:C.Jf.Xf,Tf:C.Jf.Tf,readlink:C.Jf.readlink},stream:{}},fh:{node:{Xf:C.Jf.Xf,Tf:C.Jf.Tf},stream:B.Fh}});
d=B.createNode(a,c,d,e);B.Sf(d.mode)?(d.Jf=C.$f.dir.node,d.Lf=C.$f.dir.stream,d.Kf={}):B.isFile(d.mode)?(d.Jf=C.$f.file.node,d.Lf=C.$f.file.stream,d.Pf=0,d.Kf=null):B.vg(d.mode)?(d.Jf=C.$f.link.node,d.Lf=C.$f.link.stream):B.Ag(d.mode)&&(d.Jf=C.$f.fh.node,d.Lf=C.$f.fh.stream);d.timestamp=Date.now();a&&(a.Kf[c]=d,a.timestamp=d.timestamp);return d},Di:function(a){return a.Kf?a.Kf.subarray?a.Kf.subarray(0,a.Pf):new Uint8Array(a.Kf):new Uint8Array(0)},mh:function(a,c){var d=a.Kf?a.Kf.length:0;d>=c||(c=
Math.max(c,d*(1048576>d?2:1.125)>>>0),0!=d&&(c=Math.max(c,256)),d=a.Kf,a.Kf=new Uint8Array(c),0<a.Pf&&a.Kf.set(d.subarray(0,a.Pf),0))},ti:function(a,c){if(a.Pf!=c)if(0==c)a.Kf=null,a.Pf=0;else{var d=a.Kf;a.Kf=new Uint8Array(c);d&&a.Kf.set(d.subarray(0,Math.min(c,a.Pf)));a.Pf=c}},Jf:{Xf:function(a){var c={};c.dev=B.Ag(a.mode)?a.id:1;c.ino=a.id;c.mode=a.mode;c.nlink=1;c.uid=0;c.gid=0;c.rdev=a.rdev;B.Sf(a.mode)?c.size=4096:B.isFile(a.mode)?c.size=a.Pf:B.vg(a.mode)?c.size=a.link.length:c.size=0;c.atime=
new Date(a.timestamp);c.mtime=new Date(a.timestamp);c.ctime=new Date(a.timestamp);c.Dh=4096;c.blocks=Math.ceil(c.size/c.Dh);return c},Tf:function(a,c){void 0!==c.mode&&(a.mode=c.mode);void 0!==c.timestamp&&(a.timestamp=c.timestamp);void 0!==c.size&&C.ti(a,c.size)},lookup:function(){throw B.Mg[44];},dg:function(a,c,d,e){return C.createNode(a,c,d,e)},rename:function(a,c,d){if(B.Sf(a.mode)){try{var e=B.cg(c,d)}catch(h){}if(e)for(var g in e.Kf)throw new B.If(55);}delete a.parent.Kf[a.name];a.parent.timestamp=
Date.now();a.name=d;c.Kf[d]=a;c.timestamp=a.parent.timestamp;a.parent=c},unlink:function(a,c){delete a.Kf[c];a.timestamp=Date.now()},rmdir:function(a,c){var d=B.cg(a,c),e;for(e in d.Kf)throw new B.If(55);delete a.Kf[c];a.timestamp=Date.now()},readdir:function(a){var c=[".",".."],d;for(d in a.Kf)a.Kf.hasOwnProperty(d)&&c.push(d);return c},symlink:function(a,c,d){a=C.createNode(a,c,41471,0);a.link=d;return a},readlink:function(a){if(!B.vg(a.mode))throw new B.If(28);return a.link}},Lf:{read:function(a,
c,d,e,g){var h=a.node.Kf;if(g>=a.node.Pf)return 0;a=Math.min(a.node.Pf-g,e);if(8<a&&h.subarray)c.set(h.subarray(g,g+a),d);else for(e=0;e<a;e++)c[d+e]=h[g+e];return a},write:function(a,c,d,e,g,h){c.buffer===r.buffer&&(h=!1);if(!e)return 0;a=a.node;a.timestamp=Date.now();if(c.subarray&&(!a.Kf||a.Kf.subarray)){if(h)return a.Kf=c.subarray(d,d+e),a.Pf=e;if(0===a.Pf&&0===g)return a.Kf=c.slice(d,d+e),a.Pf=e;if(g+e<=a.Pf)return a.Kf.set(c.subarray(d,d+e),g),e}C.mh(a,g+e);if(a.Kf.subarray&&c.subarray)a.Kf.set(c.subarray(d,
d+e),g);else for(h=0;h<e;h++)a.Kf[g+h]=c[d+h];a.Pf=Math.max(a.Pf,g+e);return e},Yf:function(a,c,d){1===d?c+=a.position:2===d&&B.isFile(a.node.mode)&&(c+=a.node.Pf);if(0>c)throw new B.If(28);return c},sg:function(a,c,d){C.mh(a.node,c+d);a.node.Pf=Math.max(a.node.Pf,c+d)},kg:function(a,c,d,e,g){if(!B.isFile(a.node.mode))throw new B.If(43);a=a.node.Kf;if(g&2||a.buffer!==r.buffer){if(0<d||d+c<a.length)a.subarray?a=a.subarray(d,d+c):a=Array.prototype.slice.call(a,d,d+c);d=!0;p();c=void 0;if(!c)throw new B.If(48);
r.set(a,c)}else d=!1,c=a.byteOffset;return{Hf:c,Bh:d}},qg:function(a,c,d,e){C.Lf.write(a,c,0,e,d,!1);return 0}}};function pb(a,c,d){var e="al "+a;ka(a,g=>{g||p(`Loading data file "${a}" failed (no arrayBuffer).`);c(new Uint8Array(g));e&&Ha(e)},()=>{if(d)d();else throw`Loading data file "${a}" failed.`;});e&&Ga(e)}var qb=b.preloadPlugins||[];function rb(a,c,d,e){"undefined"!=typeof Browser&&Browser.hg();var g=!1;qb.forEach(function(h){!g&&h.canHandle(c)&&(h.handle(a,c,d,e),g=!0)});return g}
function sb(a,c){var d=0;a&&(d|=365);c&&(d|=146);return d}
var B={root:null,xg:[],kh:{},streams:[],ni:1,Zf:null,jh:"/",Tg:!1,th:!0,If:null,Mg:{},Nh:null,Eg:0,Of:(a,c={})=>{a=hb(a);if(!a)return{path:"",node:null};c=Object.assign({Kg:!0,ah:0},c);if(8<c.ah)throw new B.If(32);a=a.split("/").filter(k=>!!k);for(var d=B.root,e="/",g=0;g<a.length;g++){var h=g===a.length-1;if(h&&c.parent)break;d=B.cg(d,a[g]);e=bb(e+"/"+a[g]);B.ig(d)&&(!h||h&&c.Kg)&&(d=d.wg.root);if(!h||c.Wf)for(h=0;B.vg(d.mode);)if(d=B.readlink(e),e=hb(cb(e),d),d=B.Of(e,{ah:c.ah+1}).node,40<h++)throw new B.If(32);
}return{path:e,node:d}},eg:a=>{for(var c;;){if(B.Bg(a))return a=a.Rf.uh,c?"/"!==a[a.length-1]?a+"/"+c:a+c:a;c=c?a.name+"/"+c:a.name;a=a.parent}},Sg:(a,c)=>{for(var d=0,e=0;e<c.length;e++)d=(d<<5)-d+c.charCodeAt(e)|0;return(a+d>>>0)%B.Zf.length},rh:a=>{var c=B.Sg(a.parent.id,a.name);a.lg=B.Zf[c];B.Zf[c]=a},sh:a=>{var c=B.Sg(a.parent.id,a.name);if(B.Zf[c]===a)B.Zf[c]=a.lg;else for(c=B.Zf[c];c;){if(c.lg===a){c.lg=a.lg;break}c=c.lg}},cg:(a,c)=>{var d=B.li(a);if(d)throw new B.If(d,a);for(d=B.Zf[B.Sg(a.id,
c)];d;d=d.lg){var e=d.name;if(d.parent.id===a.id&&e===c)return d}return B.lookup(a,c)},createNode:(a,c,d,e)=>{a=new B.wh(a,c,d,e);B.rh(a);return a},Jg:a=>{B.sh(a)},Bg:a=>a===a.parent,ig:a=>!!a.wg,isFile:a=>32768===(a&61440),Sf:a=>16384===(a&61440),vg:a=>40960===(a&61440),Ag:a=>8192===(a&61440),ji:a=>24576===(a&61440),isFIFO:a=>4096===(a&61440),isSocket:a=>49152===(a&49152),nh:a=>{var c=["r","w","rw"][a&3];a&512&&(c+="w");return c},mg:(a,c)=>{if(B.th)return 0;if(!c.includes("r")||a.mode&292){if(c.includes("w")&&
!(a.mode&146)||c.includes("x")&&!(a.mode&73))return 2}else return 2;return 0},li:a=>{var c=B.mg(a,"x");return c?c:a.Jf.lookup?0:2},Yg:(a,c)=>{try{return B.cg(a,c),20}catch(d){}return B.mg(a,"wx")},Cg:(a,c,d)=>{try{var e=B.cg(a,c)}catch(g){return g.Qf}if(a=B.mg(a,"wx"))return a;if(d){if(!B.Sf(e.mode))return 54;if(B.Bg(e)||B.eg(e)===B.cwd())return 10}else if(B.Sf(e.mode))return 31;return 0},mi:(a,c)=>a?B.vg(a.mode)?32:B.Sf(a.mode)&&("r"!==B.nh(c)||c&512)?31:B.mg(a,B.nh(c)):44,yh:4096,oi:(a=0,c=B.yh)=>
{for(;a<=c;a++)if(!B.streams[a])return a;throw new B.If(33);},tg:a=>B.streams[a],ih:(a,c,d)=>{B.yg||(B.yg=function(){this.Uf={}},B.yg.prototype={},Object.defineProperties(B.yg.prototype,{object:{get:function(){return this.node},set:function(e){this.node=e}},flags:{get:function(){return this.Uf.flags},set:function(e){this.Uf.flags=e}},position:{get:function(){return this.Uf.position},set:function(e){this.Uf.position=e}}}));a=Object.assign(new B.yg,a);c=B.oi(c,d);a.fd=c;return B.streams[c]=a},Gh:a=>
{B.streams[a]=null},Fh:{open:a=>{a.Lf=B.Oh(a.node.rdev).Lf;a.Lf.open&&a.Lf.open(a)},Yf:()=>{throw new B.If(70);}},Xg:a=>a>>8,Ei:a=>a&255,jg:(a,c)=>a<<8|c,bh:(a,c)=>{B.kh[a]={Lf:c}},Oh:a=>B.kh[a],oh:a=>{var c=[];for(a=[a];a.length;){var d=a.pop();c.push(d);a.push.apply(a,d.xg)}return c},vh:(a,c)=>{function d(k){B.Eg--;return c(k)}function e(k){if(k){if(!e.Mh)return e.Mh=!0,d(k)}else++h>=g.length&&d(null)}"function"==typeof a&&(c=a,a=!1);B.Eg++;1<B.Eg&&n("warning: "+B.Eg+" FS.syncfs operations in flight at once, probably just doing extra work");
var g=B.oh(B.root.Rf),h=0;g.forEach(k=>{if(!k.type.vh)return e(null);k.type.vh(k,a,e)})},Rf:(a,c,d)=>{var e="/"===d,g=!d;if(e&&B.root)throw new B.If(10);if(!e&&!g){var h=B.Of(d,{Kg:!1});d=h.path;h=h.node;if(B.ig(h))throw new B.If(10);if(!B.Sf(h.mode))throw new B.If(54);}c={type:a,Hi:c,uh:d,xg:[]};a=a.Rf(c);a.Rf=c;c.root=a;e?B.root=a:h&&(h.wg=c,h.Rf&&h.Rf.xg.push(c));return a},Ki:a=>{a=B.Of(a,{Kg:!1});if(!B.ig(a.node))throw new B.If(28);a=a.node;var c=a.wg,d=B.oh(c);Object.keys(B.Zf).forEach(e=>{for(e=
B.Zf[e];e;){var g=e.lg;d.includes(e.Rf)&&B.Jg(e);e=g}});a.wg=null;a.Rf.xg.splice(a.Rf.xg.indexOf(c),1)},lookup:(a,c)=>a.Jf.lookup(a,c),dg:(a,c,d)=>{var e=B.Of(a,{parent:!0}).node;a=db(a);if(!a||"."===a||".."===a)throw new B.If(28);var g=B.Yg(e,a);if(g)throw new B.If(g);if(!e.Jf.dg)throw new B.If(63);return e.Jf.dg(e,a,c,d)},create:(a,c)=>B.dg(a,(void 0!==c?c:438)&4095|32768,0),mkdir:(a,c)=>B.dg(a,(void 0!==c?c:511)&1023|16384,0),Fi:(a,c)=>{a=a.split("/");for(var d="",e=0;e<a.length;++e)if(a[e]){d+=
"/"+a[e];try{B.mkdir(d,c)}catch(g){if(20!=g.Qf)throw g;}}},Dg:(a,c,d)=>{"undefined"==typeof d&&(d=c,c=438);return B.dg(a,c|8192,d)},symlink:(a,c)=>{if(!hb(a))throw new B.If(44);var d=B.Of(c,{parent:!0}).node;if(!d)throw new B.If(44);c=db(c);var e=B.Yg(d,c);if(e)throw new B.If(e);if(!d.Jf.symlink)throw new B.If(63);return d.Jf.symlink(d,c,a)},rename:(a,c)=>{var d=cb(a),e=cb(c),g=db(a),h=db(c);var k=B.Of(a,{parent:!0});var m=k.node;k=B.Of(c,{parent:!0});k=k.node;if(!m||!k)throw new B.If(44);if(m.Rf!==
k.Rf)throw new B.If(75);var v=B.cg(m,g);a=ib(a,e);if("."!==a.charAt(0))throw new B.If(28);a=ib(c,d);if("."!==a.charAt(0))throw new B.If(55);try{var q=B.cg(k,h)}catch(t){}if(v!==q){c=B.Sf(v.mode);if(g=B.Cg(m,g,c))throw new B.If(g);if(g=q?B.Cg(k,h,c):B.Yg(k,h))throw new B.If(g);if(!m.Jf.rename)throw new B.If(63);if(B.ig(v)||q&&B.ig(q))throw new B.If(10);if(k!==m&&(g=B.mg(m,"w")))throw new B.If(g);B.sh(v);try{m.Jf.rename(v,k,h)}catch(t){throw t;}finally{B.rh(v)}}},rmdir:a=>{var c=B.Of(a,{parent:!0}).node;
a=db(a);var d=B.cg(c,a),e=B.Cg(c,a,!0);if(e)throw new B.If(e);if(!c.Jf.rmdir)throw new B.If(63);if(B.ig(d))throw new B.If(10);c.Jf.rmdir(c,a);B.Jg(d)},readdir:a=>{a=B.Of(a,{Wf:!0}).node;if(!a.Jf.readdir)throw new B.If(54);return a.Jf.readdir(a)},unlink:a=>{var c=B.Of(a,{parent:!0}).node;if(!c)throw new B.If(44);a=db(a);var d=B.cg(c,a),e=B.Cg(c,a,!1);if(e)throw new B.If(e);if(!c.Jf.unlink)throw new B.If(63);if(B.ig(d))throw new B.If(10);c.Jf.unlink(c,a);B.Jg(d)},readlink:a=>{a=B.Of(a).node;if(!a)throw new B.If(44);
if(!a.Jf.readlink)throw new B.If(28);return hb(B.eg(a.parent),a.Jf.readlink(a))},stat:(a,c)=>{a=B.Of(a,{Wf:!c}).node;if(!a)throw new B.If(44);if(!a.Jf.Xf)throw new B.If(63);return a.Jf.Xf(a)},lstat:a=>B.stat(a,!0),chmod:(a,c,d)=>{a="string"==typeof a?B.Of(a,{Wf:!d}).node:a;if(!a.Jf.Tf)throw new B.If(63);a.Jf.Tf(a,{mode:c&4095|a.mode&-4096,timestamp:Date.now()})},lchmod:(a,c)=>{B.chmod(a,c,!0)},fchmod:(a,c)=>{a=B.tg(a);if(!a)throw new B.If(8);B.chmod(a.node,c)},chown:(a,c,d,e)=>{a="string"==typeof a?
B.Of(a,{Wf:!e}).node:a;if(!a.Jf.Tf)throw new B.If(63);a.Jf.Tf(a,{timestamp:Date.now()})},lchown:(a,c,d)=>{B.chown(a,c,d,!0)},fchown:(a,c,d)=>{a=B.tg(a);if(!a)throw new B.If(8);B.chown(a.node,c,d)},truncate:(a,c)=>{if(0>c)throw new B.If(28);a="string"==typeof a?B.Of(a,{Wf:!0}).node:a;if(!a.Jf.Tf)throw new B.If(63);if(B.Sf(a.mode))throw new B.If(31);if(!B.isFile(a.mode))throw new B.If(28);var d=B.mg(a,"w");if(d)throw new B.If(d);a.Jf.Tf(a,{size:c,timestamp:Date.now()})},Ci:(a,c)=>{a=B.tg(a);if(!a)throw new B.If(8);
if(0===(a.flags&2097155))throw new B.If(28);B.truncate(a.node,c)},Li:(a,c,d)=>{a=B.Of(a,{Wf:!0}).node;a.Jf.Tf(a,{timestamp:Math.max(c,d)})},open:(a,c,d)=>{if(""===a)throw new B.If(44);if("string"==typeof c){var e={r:0,"r+":2,w:577,"w+":578,a:1089,"a+":1090}[c];if("undefined"==typeof e)throw Error("Unknown file open mode: "+c);c=e}d=c&64?("undefined"==typeof d?438:d)&4095|32768:0;if("object"==typeof a)var g=a;else{a=bb(a);try{g=B.Of(a,{Wf:!(c&131072)}).node}catch(h){}}e=!1;if(c&64)if(g){if(c&128)throw new B.If(20);
}else g=B.dg(a,d,0),e=!0;if(!g)throw new B.If(44);B.Ag(g.mode)&&(c&=-513);if(c&65536&&!B.Sf(g.mode))throw new B.If(54);if(!e&&(d=B.mi(g,c)))throw new B.If(d);c&512&&!e&&B.truncate(g,0);c&=-131713;g=B.ih({node:g,path:B.eg(g),flags:c,seekable:!0,position:0,Lf:g.Lf,Ai:[],error:!1});g.Lf.open&&g.Lf.open(g);!b.logReadFiles||c&1||(B.$g||(B.$g={}),a in B.$g||(B.$g[a]=1));return g},close:a=>{if(B.ug(a))throw new B.If(8);a.Rg&&(a.Rg=null);try{a.Lf.close&&a.Lf.close(a)}catch(c){throw c;}finally{B.Gh(a.fd)}a.fd=
null},ug:a=>null===a.fd,Yf:(a,c,d)=>{if(B.ug(a))throw new B.If(8);if(!a.seekable||!a.Lf.Yf)throw new B.If(70);if(0!=d&&1!=d&&2!=d)throw new B.If(28);a.position=a.Lf.Yf(a,c,d);a.Ai=[];return a.position},read:(a,c,d,e,g)=>{if(0>e||0>g)throw new B.If(28);if(B.ug(a))throw new B.If(8);if(1===(a.flags&2097155))throw new B.If(8);if(B.Sf(a.node.mode))throw new B.If(31);if(!a.Lf.read)throw new B.If(28);var h="undefined"!=typeof g;if(!h)g=a.position;else if(!a.seekable)throw new B.If(70);c=a.Lf.read(a,c,d,
e,g);h||(a.position+=c);return c},write:(a,c,d,e,g,h)=>{if(0>e||0>g)throw new B.If(28);if(B.ug(a))throw new B.If(8);if(0===(a.flags&2097155))throw new B.If(8);if(B.Sf(a.node.mode))throw new B.If(31);if(!a.Lf.write)throw new B.If(28);a.seekable&&a.flags&1024&&B.Yf(a,0,2);var k="undefined"!=typeof g;if(!k)g=a.position;else if(!a.seekable)throw new B.If(70);c=a.Lf.write(a,c,d,e,g,h);k||(a.position+=c);return c},sg:(a,c,d)=>{if(B.ug(a))throw new B.If(8);if(0>c||0>=d)throw new B.If(28);if(0===(a.flags&
2097155))throw new B.If(8);if(!B.isFile(a.node.mode)&&!B.Sf(a.node.mode))throw new B.If(43);if(!a.Lf.sg)throw new B.If(138);a.Lf.sg(a,c,d)},kg:(a,c,d,e,g)=>{if(0!==(e&2)&&0===(g&2)&&2!==(a.flags&2097155))throw new B.If(2);if(1===(a.flags&2097155))throw new B.If(2);if(!a.Lf.kg)throw new B.If(43);return a.Lf.kg(a,c,d,e,g)},qg:(a,c,d,e,g)=>a.Lf.qg?a.Lf.qg(a,c,d,e,g):0,Gi:()=>0,Ug:(a,c,d)=>{if(!a.Lf.Ug)throw new B.If(59);return a.Lf.Ug(a,c,d)},readFile:(a,c={})=>{c.flags=c.flags||0;c.encoding=c.encoding||
"binary";if("utf8"!==c.encoding&&"binary"!==c.encoding)throw Error('Invalid encoding type "'+c.encoding+'"');var d,e=B.open(a,c.flags);a=B.stat(a).size;var g=new Uint8Array(a);B.read(e,g,0,a,0);"utf8"===c.encoding?d=Va(g,0):"binary"===c.encoding&&(d=g);B.close(e);return d},writeFile:(a,c,d={})=>{d.flags=d.flags||577;a=B.open(a,d.flags,d.mode);if("string"==typeof c){var e=new Uint8Array(Sa(c)+1);c=Ta(c,e,0,e.length);B.write(a,e,0,c,void 0,d.Eh)}else if(ArrayBuffer.isView(c))B.write(a,c,0,c.byteLength,
void 0,d.Eh);else throw Error("Unsupported data type");B.close(a)},cwd:()=>B.jh,chdir:a=>{a=B.Of(a,{Wf:!0});if(null===a.node)throw new B.If(44);if(!B.Sf(a.node.mode))throw new B.If(54);var c=B.mg(a.node,"x");if(c)throw new B.If(c);B.jh=a.path},Ih:()=>{B.mkdir("/tmp");B.mkdir("/home");B.mkdir("/home/<USER>")},Hh:()=>{B.mkdir("/dev");B.bh(B.jg(1,3),{read:()=>0,write:(e,g,h,k)=>k});B.Dg("/dev/null",B.jg(1,3));lb(B.jg(5,0),nb);lb(B.jg(6,0),ob);B.Dg("/dev/tty",B.jg(5,0));B.Dg("/dev/tty1",B.jg(6,0));
var a=new Uint8Array(1024),c=0,d=()=>{0===c&&(c=gb(a).byteLength);return a[--c]};B.Vf("/dev","random",d);B.Vf("/dev","urandom",d);B.mkdir("/dev/shm");B.mkdir("/dev/shm/tmp")},Kh:()=>{B.mkdir("/proc");var a=B.mkdir("/proc/self");B.mkdir("/proc/self/fd");B.Rf({Rf:()=>{var c=B.createNode(a,"fd",16895,73);c.Jf={lookup:(d,e)=>{var g=B.tg(+e);if(!g)throw new B.If(8);d={parent:null,Rf:{uh:"fake"},Jf:{readlink:()=>g.path}};return d.parent=d}};return c}},{},"/proc/self/fd")},Lh:()=>{b.stdin?B.Vf("/dev","stdin",
b.stdin):B.symlink("/dev/tty","/dev/stdin");b.stdout?B.Vf("/dev","stdout",null,b.stdout):B.symlink("/dev/tty","/dev/stdout");b.stderr?B.Vf("/dev","stderr",null,b.stderr):B.symlink("/dev/tty1","/dev/stderr");B.open("/dev/stdin",0);B.open("/dev/stdout",1);B.open("/dev/stderr",1)},lh:()=>{B.If||(B.If=function(a,c){this.name="ErrnoError";this.node=c;this.ui=function(d){this.Qf=d};this.ui(a);this.message="FS error"},B.If.prototype=Error(),B.If.prototype.constructor=B.If,[44].forEach(a=>{B.Mg[a]=new B.If(a);
B.Mg[a].stack="<generic error, no stack>"}))},vi:()=>{B.lh();B.Zf=Array(4096);B.Rf(C,{},"/");B.Ih();B.Hh();B.Kh();B.Nh={MEMFS:C}},hg:(a,c,d)=>{B.hg.Tg=!0;B.lh();b.stdin=a||b.stdin;b.stdout=c||b.stdout;b.stderr=d||b.stderr;B.Lh()},Ii:()=>{B.hg.Tg=!1;for(var a=0;a<B.streams.length;a++){var c=B.streams[a];c&&B.close(c)}},Bi:(a,c)=>{a=B.Ch(a,c);return a.exists?a.object:null},Ch:(a,c)=>{try{var d=B.Of(a,{Wf:!c});a=d.path}catch(g){}var e={Bg:!1,exists:!1,error:0,name:null,path:null,object:null,pi:!1,si:null,
ri:null};try{d=B.Of(a,{parent:!0}),e.pi=!0,e.si=d.path,e.ri=d.node,e.name=db(a),d=B.Of(a,{Wf:!c}),e.exists=!0,e.path=d.path,e.object=d.node,e.name=d.node.name,e.Bg="/"===d.path}catch(g){e.error=g.Qf}return e},Ig:(a,c)=>{a="string"==typeof a?a:B.eg(a);for(c=c.split("/").reverse();c.length;){var d=c.pop();if(d){var e=bb(a+"/"+d);try{B.mkdir(e)}catch(g){}a=e}}return e},Jh:(a,c,d,e,g)=>{a="string"==typeof a?a:B.eg(a);c=bb(a+"/"+c);return B.create(c,sb(e,g))},zg:(a,c,d,e,g,h)=>{var k=c;a&&(a="string"==
typeof a?a:B.eg(a),k=c?bb(a+"/"+c):a);a=sb(e,g);k=B.create(k,a);if(d){if("string"==typeof d){c=Array(d.length);e=0;for(g=d.length;e<g;++e)c[e]=d.charCodeAt(e);d=c}B.chmod(k,a|146);c=B.open(k,577);B.write(c,d,0,d.length,0,h);B.close(c);B.chmod(k,a)}return k},Vf:(a,c,d,e)=>{a=eb("string"==typeof a?a:B.eg(a),c);c=sb(!!d,!!e);B.Vf.Xg||(B.Vf.Xg=64);var g=B.jg(B.Vf.Xg++,0);B.bh(g,{open:h=>{h.seekable=!1},close:()=>{e&&e.buffer&&e.buffer.length&&e(10)},read:(h,k,m,v)=>{for(var q=0,t=0;t<v;t++){try{var F=
d()}catch(U){throw new B.If(29);}if(void 0===F&&0===q)throw new B.If(6);if(null===F||void 0===F)break;q++;k[m+t]=F}q&&(h.node.timestamp=Date.now());return q},write:(h,k,m,v)=>{for(var q=0;q<v;q++)try{e(k[m+q])}catch(t){throw new B.If(29);}v&&(h.node.timestamp=Date.now());return q}});return B.Dg(a,c,g)},Lg:a=>{if(a.Vg||a.ki||a.link||a.Kf)return!0;if("undefined"!=typeof XMLHttpRequest)throw Error("Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.");
if(ja)try{a.Kf=jb(ja(a.url),!0),a.Pf=a.Kf.length}catch(c){throw new B.If(29);}else throw Error("Cannot load without read() or XMLHttpRequest.");},gh:(a,c,d,e,g)=>{function h(){this.Wg=!1;this.Uf=[]}h.prototype.get=function(q){if(!(q>this.length-1||0>q)){var t=q%this.chunkSize;return this.qh(q/this.chunkSize|0)[t]}};h.prototype.Hg=function(q){this.qh=q};h.prototype.eh=function(){var q=new XMLHttpRequest;q.open("HEAD",d,!1);q.send(null);if(!(200<=q.status&&300>q.status||304===q.status))throw Error("Couldn't load "+
d+". Status: "+q.status);var t=Number(q.getResponseHeader("Content-length")),F,U=(F=q.getResponseHeader("Accept-Ranges"))&&"bytes"===F;q=(F=q.getResponseHeader("Content-Encoding"))&&"gzip"===F;var l=1048576;U||(l=t);var w=this;w.Hg(E=>{var W=E*l,qa=(E+1)*l-1;qa=Math.min(qa,t-1);if("undefined"==typeof w.Uf[E]){var Uh=w.Uf;if(W>qa)throw Error("invalid range ("+W+", "+qa+") or no bytes requested!");if(qa>t-1)throw Error("only "+t+" bytes available! programmer error!");var X=new XMLHttpRequest;X.open("GET",
d,!1);t!==l&&X.setRequestHeader("Range","bytes="+W+"-"+qa);X.responseType="arraybuffer";X.overrideMimeType&&X.overrideMimeType("text/plain; charset=x-user-defined");X.send(null);if(!(200<=X.status&&300>X.status||304===X.status))throw Error("Couldn't load "+d+". Status: "+X.status);W=void 0!==X.response?new Uint8Array(X.response||[]):jb(X.responseText||"",!0);Uh[E]=W}if("undefined"==typeof w.Uf[E])throw Error("doXHR failed!");return w.Uf[E]});if(q||!t)l=t=1,l=t=this.qh(0).length,na("LazyFiles on gzip forces download of the whole file when length is accessed");
this.Ah=t;this.zh=l;this.Wg=!0};if("undefined"!=typeof XMLHttpRequest){if(!ha)throw"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc";var k=new h;Object.defineProperties(k,{length:{get:function(){this.Wg||this.eh();return this.Ah}},chunkSize:{get:function(){this.Wg||this.eh();return this.zh}}});k={Vg:!1,Kf:k}}else k={Vg:!1,url:d};var m=B.Jh(a,c,k,e,g);k.Kf?m.Kf=k.Kf:k.url&&(m.Kf=null,m.url=k.url);Object.defineProperties(m,{Pf:{get:function(){return this.Kf.length}}});
var v={};Object.keys(m.Lf).forEach(q=>{var t=m.Lf[q];v[q]=function(){B.Lg(m);return t.apply(null,arguments)}});v.read=(q,t,F,U,l)=>{B.Lg(m);q=q.node.Kf;if(l>=q.length)t=0;else{U=Math.min(q.length-l,U);if(q.slice)for(var w=0;w<U;w++)t[F+w]=q[l+w];else for(w=0;w<U;w++)t[F+w]=q.get(l+w);t=U}return t};v.kg=()=>{B.Lg(m);p();throw new B.If(48);};m.Lf=v;return m}};
function tb(a,c,d){if("/"===c.charAt(0))return c;a=-100===a?B.cwd():ub(a).path;if(0==c.length){if(!d)throw new B.If(44);return a}return bb(a+"/"+c)}
function vb(a,c,d){try{var e=a(c)}catch(h){if(h&&h.node&&bb(c)!==bb(B.eg(h.node)))return-54;throw h;}u[d>>2]=e.dev;u[d+8>>2]=e.ino;u[d+12>>2]=e.mode;x[d+16>>2]=e.nlink;u[d+20>>2]=e.uid;u[d+24>>2]=e.gid;u[d+28>>2]=e.rdev;z=[e.size>>>0,(y=e.size,1<=+Math.abs(y)?0<y?+Math.floor(y/4294967296)>>>0:~~+Math.ceil((y-+(~~y>>>0))/4294967296)>>>0:0)];u[d+40>>2]=z[0];u[d+44>>2]=z[1];u[d+48>>2]=4096;u[d+52>>2]=e.blocks;a=e.atime.getTime();c=e.mtime.getTime();var g=e.ctime.getTime();z=[Math.floor(a/1E3)>>>0,(y=
Math.floor(a/1E3),1<=+Math.abs(y)?0<y?+Math.floor(y/4294967296)>>>0:~~+Math.ceil((y-+(~~y>>>0))/4294967296)>>>0:0)];u[d+56>>2]=z[0];u[d+60>>2]=z[1];x[d+64>>2]=a%1E3*1E3;z=[Math.floor(c/1E3)>>>0,(y=Math.floor(c/1E3),1<=+Math.abs(y)?0<y?+Math.floor(y/4294967296)>>>0:~~+Math.ceil((y-+(~~y>>>0))/4294967296)>>>0:0)];u[d+72>>2]=z[0];u[d+76>>2]=z[1];x[d+80>>2]=c%1E3*1E3;z=[Math.floor(g/1E3)>>>0,(y=Math.floor(g/1E3),1<=+Math.abs(y)?0<y?+Math.floor(y/4294967296)>>>0:~~+Math.ceil((y-+(~~y>>>0))/4294967296)>>>
0:0)];u[d+88>>2]=z[0];u[d+92>>2]=z[1];x[d+96>>2]=g%1E3*1E3;z=[e.ino>>>0,(y=e.ino,1<=+Math.abs(y)?0<y?+Math.floor(y/4294967296)>>>0:~~+Math.ceil((y-+(~~y>>>0))/4294967296)>>>0:0)];u[d+104>>2]=z[0];u[d+108>>2]=z[1];return 0}var wb=void 0;function xb(){wb+=4;return u[wb-4>>2]}function ub(a){a=B.tg(a);if(!a)throw new B.If(8);return a}function yb(){n("missing function: setThrew");p(-1)}function zb(a){return 0===a%4&&(0!==a%100||0===a%400)}
var Ab=[0,31,60,91,121,152,182,213,244,274,305,335],Bb=[0,31,59,90,120,151,181,212,243,273,304,334];function Cb(a){return(zb(a.getFullYear())?Ab:Bb)[a.getMonth()]+a.getDate()-1}function Db(a){var c=Sa(a)+1,d=Eb(c);d&&Ta(a,sa,d,c);return d}var Fb=[],Gb;Gb=ia?()=>{var a=process.hrtime();return 1E3*a[0]+a[1]/1E6}:()=>performance.now();var Hb={};
function Ib(){if(!Jb){var a={USER:"web_user",LOGNAME:"web_user",PATH:"/",PWD:"/",HOME:"/home/<USER>",LANG:("object"==typeof navigator&&navigator.languages&&navigator.languages[0]||"C").replace("-","_")+".UTF-8",_:da||"./this.program"},c;for(c in Hb)void 0===Hb[c]?delete a[c]:a[c]=Hb[c];var d=[];for(c in a)d.push(c+"="+a[c]);Jb=d}return Jb}var Jb,Kb=[31,29,31,30,31,30,31,31,30,31,30,31],Lb=[31,28,31,30,31,30,31,31,30,31,30,31];
function Mb(a,c,d,e){function g(l,w,E){for(l="number"==typeof l?l.toString():l||"";l.length<w;)l=E[0]+l;return l}function h(l,w){return g(l,w,"0")}function k(l,w){function E(qa){return 0>qa?-1:0<qa?1:0}var W;0===(W=E(l.getFullYear()-w.getFullYear()))&&0===(W=E(l.getMonth()-w.getMonth()))&&(W=E(l.getDate()-w.getDate()));return W}function m(l){switch(l.getDay()){case 0:return new Date(l.getFullYear()-1,11,29);case 1:return l;case 2:return new Date(l.getFullYear(),0,3);case 3:return new Date(l.getFullYear(),
0,2);case 4:return new Date(l.getFullYear(),0,1);case 5:return new Date(l.getFullYear()-1,11,31);case 6:return new Date(l.getFullYear()-1,11,30)}}function v(l){var w=l.og;for(l=new Date((new Date(l.pg+1900,0,1)).getTime());0<w;){var E=l.getMonth(),W=(zb(l.getFullYear())?Kb:Lb)[E];if(w>W-l.getDate())w-=W-l.getDate()+1,l.setDate(1),11>E?l.setMonth(E+1):(l.setMonth(0),l.setFullYear(l.getFullYear()+1));else{l.setDate(l.getDate()+w);break}}E=new Date(l.getFullYear()+1,0,4);w=m(new Date(l.getFullYear(),
0,4));E=m(E);return 0>=k(w,l)?0>=k(E,l)?l.getFullYear()+1:l.getFullYear():l.getFullYear()-1}var q=u[e+40>>2];e={yi:u[e>>2],xi:u[e+4>>2],Fg:u[e+8>>2],dh:u[e+12>>2],Gg:u[e+16>>2],pg:u[e+20>>2],ag:u[e+24>>2],og:u[e+28>>2],Ji:u[e+32>>2],wi:u[e+36>>2],zi:q?A(q):""};d=A(d);q={"%c":"%a %b %d %H:%M:%S %Y","%D":"%m/%d/%y","%F":"%Y-%m-%d","%h":"%b","%r":"%I:%M:%S %p","%R":"%H:%M","%T":"%H:%M:%S","%x":"%m/%d/%y","%X":"%H:%M:%S","%Ec":"%c","%EC":"%C","%Ex":"%m/%d/%y","%EX":"%H:%M:%S","%Ey":"%y","%EY":"%Y","%Od":"%d",
"%Oe":"%e","%OH":"%H","%OI":"%I","%Om":"%m","%OM":"%M","%OS":"%S","%Ou":"%u","%OU":"%U","%OV":"%V","%Ow":"%w","%OW":"%W","%Oy":"%y"};for(var t in q)d=d.replace(new RegExp(t,"g"),q[t]);var F="Sunday Monday Tuesday Wednesday Thursday Friday Saturday".split(" "),U="January February March April May June July August September October November December".split(" ");q={"%a":function(l){return F[l.ag].substring(0,3)},"%A":function(l){return F[l.ag]},"%b":function(l){return U[l.Gg].substring(0,3)},"%B":function(l){return U[l.Gg]},
"%C":function(l){return h((l.pg+1900)/100|0,2)},"%d":function(l){return h(l.dh,2)},"%e":function(l){return g(l.dh,2," ")},"%g":function(l){return v(l).toString().substring(2)},"%G":function(l){return v(l)},"%H":function(l){return h(l.Fg,2)},"%I":function(l){l=l.Fg;0==l?l=12:12<l&&(l-=12);return h(l,2)},"%j":function(l){for(var w=0,E=0;E<=l.Gg-1;w+=(zb(l.pg+1900)?Kb:Lb)[E++]);return h(l.dh+w,3)},"%m":function(l){return h(l.Gg+1,2)},"%M":function(l){return h(l.xi,2)},"%n":function(){return"\n"},"%p":function(l){return 0<=
l.Fg&&12>l.Fg?"AM":"PM"},"%S":function(l){return h(l.yi,2)},"%t":function(){return"\t"},"%u":function(l){return l.ag||7},"%U":function(l){return h(Math.floor((l.og+7-l.ag)/7),2)},"%V":function(l){var w=Math.floor((l.og+7-(l.ag+6)%7)/7);2>=(l.ag+371-l.og-2)%7&&w++;if(w)53==w&&(E=(l.ag+371-l.og)%7,4==E||3==E&&zb(l.pg)||(w=1));else{w=52;var E=(l.ag+7-l.og-1)%7;(4==E||5==E&&zb(l.pg%400-1))&&w++}return h(w,2)},"%w":function(l){return l.ag},"%W":function(l){return h(Math.floor((l.og+7-(l.ag+6)%7)/7),2)},
"%y":function(l){return(l.pg+1900).toString().substring(2)},"%Y":function(l){return l.pg+1900},"%z":function(l){l=l.wi;var w=0<=l;l=Math.abs(l)/60;return(w?"+":"-")+String("0000"+(l/60*100+l%60)).slice(-4)},"%Z":function(l){return l.zi},"%%":function(){return"%"}};d=d.replace(/%%/g,"\x00\x00");for(t in q)d.includes(t)&&(d=d.replace(new RegExp(t,"g"),q[t](e)));d=d.replace(/\0\0/g,"%");t=jb(d,!1);if(t.length>c)return 0;r.set(t,a);return t.length-1}var Nb=[];
function Ob(a){var c=Nb[a];c||(a>=Nb.length&&(Nb.length=a+1),Nb[a]=c=xa.get(a));return c}function Pb(a,c,d,e){a||(a=this);this.parent=a;this.Rf=a.Rf;this.wg=null;this.id=B.ni++;this.name=c;this.mode=d;this.Jf={};this.Lf={};this.rdev=e}
Object.defineProperties(Pb.prototype,{read:{get:function(){return 365===(this.mode&365)},set:function(a){a?this.mode|=365:this.mode&=-366}},write:{get:function(){return 146===(this.mode&146)},set:function(a){a?this.mode|=146:this.mode&=-147}},ki:{get:function(){return B.Sf(this.mode)}},Vg:{get:function(){return B.Ag(this.mode)}}});B.wh=Pb;
B.hh=function(a,c,d,e,g,h,k,m,v,q){function t(l){function w(E){q&&q();m||B.zg(a,c,E,e,g,v);h&&h();Ha(U)}rb(l,F,w,()=>{k&&k();Ha(U)})||w(l)}var F=c?hb(bb(a+"/"+c)):a,U="cp "+F;Ga(U);"string"==typeof d?pb(d,l=>t(l),k):t(d)};B.vi();b.FS_createPath=B.Ig;b.FS_createDataFile=B.zg;b.FS_createPreloadedFile=B.hh;b.FS_unlink=B.unlink;b.FS_createLazyFile=B.gh;b.FS_createDevice=B.Vf;
var bc={U:function(){n("missing function: _ZN9tesseract11TessBaseAPI10GetOsdTextEi");p(-1)},X:function(){n("missing function: _ZN9tesseract11TessBaseAPI23ClearAdaptiveClassifierEv");p(-1)},P:function(){n("missing function: _ZN9tesseract11TessBaseAPI8DetectOSEPNS_9OSResultsE");p(-1)},F:function(){n("missing function: _ZNK9tesseract9OSResults12print_scoresEv");p(-1)},a:function(a,c,d,e){p(`Assertion failed: ${A(a)}, at: `+[c?A(c):"unknown filename",d,e?A(e):"unknown function"])},l:function(a,c,d){(new Ya(a)).hg(c,
d);Za=a;$a++;throw Za;},r:function(a,c,d){wb=d;try{var e=ub(a);switch(c){case 0:var g=xb();return 0>g?-28:B.ih(e,g).fd;case 1:case 2:return 0;case 3:return e.flags;case 4:return g=xb(),e.flags|=g,0;case 5:return g=xb(),ta[g+0>>1]=2,0;case 6:case 7:return 0;case 16:case 8:return-28;case 9:return u[Qb()>>2]=28,-1;default:return-28}}catch(h){if("undefined"==typeof B||"ErrnoError"!==h.name)throw h;return-h.Qf}},N:function(a,c){try{var d=ub(a);return vb(B.stat,d.path,c)}catch(e){if("undefined"==typeof B||
"ErrnoError"!==e.name)throw e;return-e.Qf}},K:function(a,c){try{if(0===c)return-28;var d=B.cwd(),e=Sa(d)+1;if(c<e)return-68;Ta(d,sa,a,c);return e}catch(g){if("undefined"==typeof B||"ErrnoError"!==g.name)throw g;return-g.Qf}},S:function(a,c,d){wb=d;try{var e=ub(a);switch(c){case 21509:case 21505:return e.tty?0:-59;case 21510:case 21511:case 21512:case 21506:case 21507:case 21508:return e.tty?0:-59;case 21519:if(!e.tty)return-59;var g=xb();return u[g>>2]=0;case 21520:return e.tty?-28:-59;case 21531:return g=
xb(),B.Ug(e,c,g);case 21523:return e.tty?0:-59;case 21524:return e.tty?0:-59;default:return-28}}catch(h){if("undefined"==typeof B||"ErrnoError"!==h.name)throw h;return-h.Qf}},L:function(a,c,d,e){try{c=A(c);var g=e&256;c=tb(a,c,e&4096);return vb(g?B.lstat:B.stat,c,d)}catch(h){if("undefined"==typeof B||"ErrnoError"!==h.name)throw h;return-h.Qf}},p:function(a,c,d,e){wb=e;try{c=A(c);c=tb(a,c);var g=e?xb():0;return B.open(c,d,g).fd}catch(h){if("undefined"==typeof B||"ErrnoError"!==h.name)throw h;return-h.Qf}},
A:function(a){try{return a=A(a),B.rmdir(a),0}catch(c){if("undefined"==typeof B||"ErrnoError"!==c.name)throw c;return-c.Qf}},M:function(a,c){try{return a=A(a),vb(B.stat,a,c)}catch(d){if("undefined"==typeof B||"ErrnoError"!==d.name)throw d;return-d.Qf}},B:function(a,c,d){try{return c=A(c),c=tb(a,c),0===d?B.unlink(c):512===d?B.rmdir(c):p("Invalid flags passed to unlinkat"),0}catch(e){if("undefined"==typeof B||"ErrnoError"!==e.name)throw e;return-e.Qf}},T:function(a){do{var c=x[a>>2];a+=4;var d=x[a>>
2];a+=4;var e=x[a>>2];a+=4;c=A(c);B.Ig("/",cb(c),!0,!0);B.zg(c,null,r.subarray(e,e+d),!0,!0,!0)}while(x[a>>2])},Q:function(){return!0},x:function(){throw Infinity;},E:function(a,c){a=new Date(1E3*(x[a>>2]+4294967296*u[a+4>>2]));u[c>>2]=a.getUTCSeconds();u[c+4>>2]=a.getUTCMinutes();u[c+8>>2]=a.getUTCHours();u[c+12>>2]=a.getUTCDate();u[c+16>>2]=a.getUTCMonth();u[c+20>>2]=a.getUTCFullYear()-1900;u[c+24>>2]=a.getUTCDay();u[c+28>>2]=(a.getTime()-Date.UTC(a.getUTCFullYear(),0,1,0,0,0,0))/864E5|0},G:function(a,
c){a=new Date(1E3*(x[a>>2]+4294967296*u[a+4>>2]));u[c>>2]=a.getSeconds();u[c+4>>2]=a.getMinutes();u[c+8>>2]=a.getHours();u[c+12>>2]=a.getDate();u[c+16>>2]=a.getMonth();u[c+20>>2]=a.getFullYear()-1900;u[c+24>>2]=a.getDay();u[c+28>>2]=Cb(a)|0;u[c+36>>2]=-(60*a.getTimezoneOffset());var d=(new Date(a.getFullYear(),6,1)).getTimezoneOffset(),e=(new Date(a.getFullYear(),0,1)).getTimezoneOffset();u[c+32>>2]=(d!=e&&a.getTimezoneOffset()==Math.min(e,d))|0},H:function(a){var c=new Date(u[a+20>>2]+1900,u[a+16>>
2],u[a+12>>2],u[a+8>>2],u[a+4>>2],u[a>>2],0),d=u[a+32>>2],e=c.getTimezoneOffset(),g=(new Date(c.getFullYear(),6,1)).getTimezoneOffset(),h=(new Date(c.getFullYear(),0,1)).getTimezoneOffset(),k=Math.min(h,g);0>d?u[a+32>>2]=Number(g!=h&&k==e):0<d!=(k==e)&&(g=Math.max(h,g),c.setTime(c.getTime()+6E4*((0<d?k:g)-e)));u[a+24>>2]=c.getDay();u[a+28>>2]=Cb(c)|0;u[a>>2]=c.getSeconds();u[a+4>>2]=c.getMinutes();u[a+8>>2]=c.getHours();u[a+12>>2]=c.getDate();u[a+16>>2]=c.getMonth();u[a+20>>2]=c.getYear();return c.getTime()/
1E3|0},C:function(a,c,d,e,g,h,k){try{var m=ub(e),v=B.kg(m,a,g,c,d),q=v.Hf;u[h>>2]=v.Bh;x[k>>2]=q;return 0}catch(t){if("undefined"==typeof B||"ErrnoError"!==t.name)throw t;return-t.Qf}},D:function(a,c,d,e,g,h){try{var k=ub(g);if(d&2){if(!B.isFile(k.node.mode))throw new B.If(43);if(!(e&2)){var m=sa.slice(a,a+c);B.qg(k,m,h,c,e)}}}catch(v){if("undefined"==typeof B||"ErrnoError"!==v.name)throw v;return-v.Qf}},z:function(a,c,d){function e(v){return(v=v.toTimeString().match(/\(([A-Za-z ]+)\)$/))?v[1]:"GMT"}
var g=(new Date).getFullYear(),h=new Date(g,0,1),k=new Date(g,6,1);g=h.getTimezoneOffset();var m=k.getTimezoneOffset();x[a>>2]=60*Math.max(g,m);u[c>>2]=Number(g!=m);a=e(h);c=e(k);a=Db(a);c=Db(c);m<g?(x[d>>2]=a,x[d+4>>2]=c):(x[d>>2]=c,x[d+4>>2]=a)},k:function(){p("")},u:function(a,c,d){Fb.length=0;var e;for(d>>=2;e=sa[c++];)d+=105!=e&d,Fb.push(105==e?u[d]:va[d++>>1]),++d;return Pa[a].apply(null,Fb)},m:function(){return Date.now()},O:Gb,R:function(a,c,d){sa.copyWithin(a,c,c+d)},y:function(a){var c=
sa.length;a>>>=0;if(2147483648<a)return!1;for(var d=1;4>=d;d*=2){var e=c*(1+.2/d);e=Math.min(e,a+100663296);var g=Math,h=g.min;e=Math.max(a,e);e+=(65536-e%65536)%65536;a:{var k=pa.buffer;try{pa.grow(h.call(g,2147483648,e)-k.byteLength+65535>>>16);wa();var m=1;break a}catch(v){}m=void 0}if(m)return!0}return!1},I:function(a,c){var d=0;Ib().forEach(function(e,g){var h=c+d;g=x[a+4*g>>2]=h;for(h=0;h<e.length;++h)r[g++>>0]=e.charCodeAt(h);r[g>>0]=0;d+=e.length+1});return 0},J:function(a,c){var d=Ib();x[a>>
2]=d.length;var e=0;d.forEach(function(g){e+=g.length+1});x[c>>2]=e;return 0},V:function(a){if(!noExitRuntime){if(b.onExit)b.onExit(a);ra=!0}ea(a,new Qa(a))},o:function(a){try{var c=ub(a);B.close(c);return 0}catch(d){if("undefined"==typeof B||"ErrnoError"!==d.name)throw d;return d.Qf}},q:function(a,c,d,e){try{a:{var g=ub(a);a=c;for(var h,k=c=0;k<d;k++){var m=x[a>>2],v=x[a+4>>2];a+=8;var q=B.read(g,r,m,v,h);if(0>q){var t=-1;break a}c+=q;if(q<v)break;"undefined"!==typeof h&&(h+=q)}t=c}x[e>>2]=t;return 0}catch(F){if("undefined"==
typeof B||"ErrnoError"!==F.name)throw F;return F.Qf}},v:function(a,c,d,e,g){try{c=d+2097152>>>0<4194305-!!c?(c>>>0)+4294967296*d:NaN;if(isNaN(c))return 61;var h=ub(a);B.Yf(h,c,e);z=[h.position>>>0,(y=h.position,1<=+Math.abs(y)?0<y?+Math.floor(y/4294967296)>>>0:~~+Math.ceil((y-+(~~y>>>0))/4294967296)>>>0:0)];u[g>>2]=z[0];u[g+4>>2]=z[1];h.Rg&&0===c&&0===e&&(h.Rg=null);return 0}catch(k){if("undefined"==typeof B||"ErrnoError"!==k.name)throw k;return k.Qf}},n:function(a,c,d,e){try{a:{var g=ub(a);a=c;for(var h,
k=c=0;k<d;k++){var m=x[a>>2],v=x[a+4>>2];a+=8;var q=B.write(g,r,m,v,h);if(0>q){var t=-1;break a}c+=q;"undefined"!==typeof h&&(h+=q)}t=c}x[e>>2]=t;return 0}catch(F){if("undefined"==typeof B||"ErrnoError"!==F.name)throw F;return F.Qf}},c:Rb,e:Sb,b:Tb,h:Ub,i:Vb,d:Wb,f:Xb,g:Yb,j:Zb,s:$b,t:ac,W:Mb,w:function(a,c,d,e){return Mb(a,c,d,e)}};
(function(){function a(d){d=d.exports;b.asm=d;pa=b.asm.Y;wa();xa=b.asm.vf;za.unshift(b.asm.Z);Ha("wasm-instantiate");return d}var c={a:bc};Ga("wasm-instantiate");if(b.instantiateWasm)try{return b.instantiateWasm(c,a)}catch(d){n("Module.instantiateWasm callback failed with error: "+d),ba(d)}Oa(c,function(d){a(d.instance)}).catch(ba);return{}})();
var cc=b._emscripten_bind_ParagraphJustification___destroy___0=function(){return(cc=b._emscripten_bind_ParagraphJustification___destroy___0=b.asm._).apply(null,arguments)},dc=b._emscripten_bind_BoolPtr___destroy___0=function(){return(dc=b._emscripten_bind_BoolPtr___destroy___0=b.asm.$).apply(null,arguments)},ec=b._emscripten_bind_TessResultRenderer_BeginDocument_1=function(){return(ec=b._emscripten_bind_TessResultRenderer_BeginDocument_1=b.asm.aa).apply(null,arguments)},fc=b._emscripten_bind_TessResultRenderer_AddImage_1=
function(){return(fc=b._emscripten_bind_TessResultRenderer_AddImage_1=b.asm.ba).apply(null,arguments)},gc=b._emscripten_bind_TessResultRenderer_EndDocument_0=function(){return(gc=b._emscripten_bind_TessResultRenderer_EndDocument_0=b.asm.ca).apply(null,arguments)},hc=b._emscripten_bind_TessResultRenderer_happy_0=function(){return(hc=b._emscripten_bind_TessResultRenderer_happy_0=b.asm.da).apply(null,arguments)},ic=b._emscripten_bind_TessResultRenderer_file_extension_0=function(){return(ic=b._emscripten_bind_TessResultRenderer_file_extension_0=
b.asm.ea).apply(null,arguments)},jc=b._emscripten_bind_TessResultRenderer_title_0=function(){return(jc=b._emscripten_bind_TessResultRenderer_title_0=b.asm.fa).apply(null,arguments)},kc=b._emscripten_bind_TessResultRenderer_imagenum_0=function(){return(kc=b._emscripten_bind_TessResultRenderer_imagenum_0=b.asm.ga).apply(null,arguments)},lc=b._emscripten_bind_TessResultRenderer___destroy___0=function(){return(lc=b._emscripten_bind_TessResultRenderer___destroy___0=b.asm.ha).apply(null,arguments)},mc=
b._emscripten_bind_LongStarPtr___destroy___0=function(){return(mc=b._emscripten_bind_LongStarPtr___destroy___0=b.asm.ia).apply(null,arguments)},nc=b._emscripten_bind_VoidPtr___destroy___0=function(){return(nc=b._emscripten_bind_VoidPtr___destroy___0=b.asm.ja).apply(null,arguments)},oc=b._emscripten_bind_ResultIterator_ResultIterator_1=function(){return(oc=b._emscripten_bind_ResultIterator_ResultIterator_1=b.asm.ka).apply(null,arguments)},pc=b._emscripten_bind_ResultIterator_Begin_0=function(){return(pc=
b._emscripten_bind_ResultIterator_Begin_0=b.asm.la).apply(null,arguments)},qc=b._emscripten_bind_ResultIterator_RestartParagraph_0=function(){return(qc=b._emscripten_bind_ResultIterator_RestartParagraph_0=b.asm.ma).apply(null,arguments)},rc=b._emscripten_bind_ResultIterator_IsWithinFirstTextlineOfParagraph_0=function(){return(rc=b._emscripten_bind_ResultIterator_IsWithinFirstTextlineOfParagraph_0=b.asm.na).apply(null,arguments)},sc=b._emscripten_bind_ResultIterator_RestartRow_0=function(){return(sc=
b._emscripten_bind_ResultIterator_RestartRow_0=b.asm.oa).apply(null,arguments)},tc=b._emscripten_bind_ResultIterator_Next_1=function(){return(tc=b._emscripten_bind_ResultIterator_Next_1=b.asm.pa).apply(null,arguments)},uc=b._emscripten_bind_ResultIterator_IsAtBeginningOf_1=function(){return(uc=b._emscripten_bind_ResultIterator_IsAtBeginningOf_1=b.asm.qa).apply(null,arguments)},vc=b._emscripten_bind_ResultIterator_IsAtFinalElement_2=function(){return(vc=b._emscripten_bind_ResultIterator_IsAtFinalElement_2=
b.asm.ra).apply(null,arguments)},wc=b._emscripten_bind_ResultIterator_Cmp_1=function(){return(wc=b._emscripten_bind_ResultIterator_Cmp_1=b.asm.sa).apply(null,arguments)},xc=b._emscripten_bind_ResultIterator_SetBoundingBoxComponents_2=function(){return(xc=b._emscripten_bind_ResultIterator_SetBoundingBoxComponents_2=b.asm.ta).apply(null,arguments)},yc=b._emscripten_bind_ResultIterator_BoundingBox_5=function(){return(yc=b._emscripten_bind_ResultIterator_BoundingBox_5=b.asm.ua).apply(null,arguments)},
zc=b._emscripten_bind_ResultIterator_BoundingBox_6=function(){return(zc=b._emscripten_bind_ResultIterator_BoundingBox_6=b.asm.va).apply(null,arguments)},Ac=b._emscripten_bind_ResultIterator_BoundingBoxInternal_5=function(){return(Ac=b._emscripten_bind_ResultIterator_BoundingBoxInternal_5=b.asm.wa).apply(null,arguments)},Bc=b._emscripten_bind_ResultIterator_Empty_1=function(){return(Bc=b._emscripten_bind_ResultIterator_Empty_1=b.asm.xa).apply(null,arguments)},Cc=b._emscripten_bind_ResultIterator_BlockType_0=
function(){return(Cc=b._emscripten_bind_ResultIterator_BlockType_0=b.asm.ya).apply(null,arguments)},Dc=b._emscripten_bind_ResultIterator_BlockPolygon_0=function(){return(Dc=b._emscripten_bind_ResultIterator_BlockPolygon_0=b.asm.za).apply(null,arguments)},Ec=b._emscripten_bind_ResultIterator_GetBinaryImage_1=function(){return(Ec=b._emscripten_bind_ResultIterator_GetBinaryImage_1=b.asm.Aa).apply(null,arguments)},Fc=b._emscripten_bind_ResultIterator_GetImage_5=function(){return(Fc=b._emscripten_bind_ResultIterator_GetImage_5=
b.asm.Ba).apply(null,arguments)},Gc=b._emscripten_bind_ResultIterator_Baseline_5=function(){return(Gc=b._emscripten_bind_ResultIterator_Baseline_5=b.asm.Ca).apply(null,arguments)},Hc=b._emscripten_bind_ResultIterator_RowAttributes_3=function(){return(Hc=b._emscripten_bind_ResultIterator_RowAttributes_3=b.asm.Da).apply(null,arguments)},Ic=b._emscripten_bind_ResultIterator_Orientation_4=function(){return(Ic=b._emscripten_bind_ResultIterator_Orientation_4=b.asm.Ea).apply(null,arguments)},Jc=b._emscripten_bind_ResultIterator_ParagraphInfo_4=
function(){return(Jc=b._emscripten_bind_ResultIterator_ParagraphInfo_4=b.asm.Fa).apply(null,arguments)},Kc=b._emscripten_bind_ResultIterator_ParagraphIsLtr_0=function(){return(Kc=b._emscripten_bind_ResultIterator_ParagraphIsLtr_0=b.asm.Ga).apply(null,arguments)},Lc=b._emscripten_bind_ResultIterator_GetUTF8Text_1=function(){return(Lc=b._emscripten_bind_ResultIterator_GetUTF8Text_1=b.asm.Ha).apply(null,arguments)},Mc=b._emscripten_bind_ResultIterator_SetLineSeparator_1=function(){return(Mc=b._emscripten_bind_ResultIterator_SetLineSeparator_1=
b.asm.Ia).apply(null,arguments)},Nc=b._emscripten_bind_ResultIterator_SetParagraphSeparator_1=function(){return(Nc=b._emscripten_bind_ResultIterator_SetParagraphSeparator_1=b.asm.Ja).apply(null,arguments)},Oc=b._emscripten_bind_ResultIterator_Confidence_1=function(){return(Oc=b._emscripten_bind_ResultIterator_Confidence_1=b.asm.Ka).apply(null,arguments)},Pc=b._emscripten_bind_ResultIterator_WordFontAttributes_8=function(){return(Pc=b._emscripten_bind_ResultIterator_WordFontAttributes_8=b.asm.La).apply(null,
arguments)},Qc=b._emscripten_bind_ResultIterator_WordRecognitionLanguage_0=function(){return(Qc=b._emscripten_bind_ResultIterator_WordRecognitionLanguage_0=b.asm.Ma).apply(null,arguments)},Rc=b._emscripten_bind_ResultIterator_WordDirection_0=function(){return(Rc=b._emscripten_bind_ResultIterator_WordDirection_0=b.asm.Na).apply(null,arguments)},Sc=b._emscripten_bind_ResultIterator_WordIsFromDictionary_0=function(){return(Sc=b._emscripten_bind_ResultIterator_WordIsFromDictionary_0=b.asm.Oa).apply(null,
arguments)},Tc=b._emscripten_bind_ResultIterator_WordIsNumeric_0=function(){return(Tc=b._emscripten_bind_ResultIterator_WordIsNumeric_0=b.asm.Pa).apply(null,arguments)},Uc=b._emscripten_bind_ResultIterator_HasBlamerInfo_0=function(){return(Uc=b._emscripten_bind_ResultIterator_HasBlamerInfo_0=b.asm.Qa).apply(null,arguments)},Vc=b._emscripten_bind_ResultIterator_HasTruthString_0=function(){return(Vc=b._emscripten_bind_ResultIterator_HasTruthString_0=b.asm.Ra).apply(null,arguments)},Wc=b._emscripten_bind_ResultIterator_EquivalentToTruth_1=
function(){return(Wc=b._emscripten_bind_ResultIterator_EquivalentToTruth_1=b.asm.Sa).apply(null,arguments)},Xc=b._emscripten_bind_ResultIterator_WordTruthUTF8Text_0=function(){return(Xc=b._emscripten_bind_ResultIterator_WordTruthUTF8Text_0=b.asm.Ta).apply(null,arguments)},Yc=b._emscripten_bind_ResultIterator_WordNormedUTF8Text_0=function(){return(Yc=b._emscripten_bind_ResultIterator_WordNormedUTF8Text_0=b.asm.Ua).apply(null,arguments)},Zc=b._emscripten_bind_ResultIterator_WordLattice_1=function(){return(Zc=
b._emscripten_bind_ResultIterator_WordLattice_1=b.asm.Va).apply(null,arguments)},$c=b._emscripten_bind_ResultIterator_SymbolIsSuperscript_0=function(){return($c=b._emscripten_bind_ResultIterator_SymbolIsSuperscript_0=b.asm.Wa).apply(null,arguments)},ad=b._emscripten_bind_ResultIterator_SymbolIsSubscript_0=function(){return(ad=b._emscripten_bind_ResultIterator_SymbolIsSubscript_0=b.asm.Xa).apply(null,arguments)},bd=b._emscripten_bind_ResultIterator_SymbolIsDropcap_0=function(){return(bd=b._emscripten_bind_ResultIterator_SymbolIsDropcap_0=
b.asm.Ya).apply(null,arguments)},cd=b._emscripten_bind_ResultIterator___destroy___0=function(){return(cd=b._emscripten_bind_ResultIterator___destroy___0=b.asm.Za).apply(null,arguments)},dd=b._emscripten_bind_TextlineOrder___destroy___0=function(){return(dd=b._emscripten_bind_TextlineOrder___destroy___0=b.asm._a).apply(null,arguments)},ed=b._emscripten_bind_ETEXT_DESC___destroy___0=function(){return(ed=b._emscripten_bind_ETEXT_DESC___destroy___0=b.asm.$a).apply(null,arguments)},fd=b._emscripten_bind_PageIterator_Begin_0=
function(){return(fd=b._emscripten_bind_PageIterator_Begin_0=b.asm.ab).apply(null,arguments)},gd=b._emscripten_bind_PageIterator_RestartParagraph_0=function(){return(gd=b._emscripten_bind_PageIterator_RestartParagraph_0=b.asm.bb).apply(null,arguments)},hd=b._emscripten_bind_PageIterator_IsWithinFirstTextlineOfParagraph_0=function(){return(hd=b._emscripten_bind_PageIterator_IsWithinFirstTextlineOfParagraph_0=b.asm.cb).apply(null,arguments)},jd=b._emscripten_bind_PageIterator_RestartRow_0=function(){return(jd=
b._emscripten_bind_PageIterator_RestartRow_0=b.asm.db).apply(null,arguments)},kd=b._emscripten_bind_PageIterator_Next_1=function(){return(kd=b._emscripten_bind_PageIterator_Next_1=b.asm.eb).apply(null,arguments)},ld=b._emscripten_bind_PageIterator_IsAtBeginningOf_1=function(){return(ld=b._emscripten_bind_PageIterator_IsAtBeginningOf_1=b.asm.fb).apply(null,arguments)},md=b._emscripten_bind_PageIterator_IsAtFinalElement_2=function(){return(md=b._emscripten_bind_PageIterator_IsAtFinalElement_2=b.asm.gb).apply(null,
arguments)},nd=b._emscripten_bind_PageIterator_Cmp_1=function(){return(nd=b._emscripten_bind_PageIterator_Cmp_1=b.asm.hb).apply(null,arguments)},od=b._emscripten_bind_PageIterator_SetBoundingBoxComponents_2=function(){return(od=b._emscripten_bind_PageIterator_SetBoundingBoxComponents_2=b.asm.ib).apply(null,arguments)},pd=b._emscripten_bind_PageIterator_BoundingBox_5=function(){return(pd=b._emscripten_bind_PageIterator_BoundingBox_5=b.asm.jb).apply(null,arguments)},qd=b._emscripten_bind_PageIterator_BoundingBox_6=
function(){return(qd=b._emscripten_bind_PageIterator_BoundingBox_6=b.asm.kb).apply(null,arguments)},rd=b._emscripten_bind_PageIterator_BoundingBoxInternal_5=function(){return(rd=b._emscripten_bind_PageIterator_BoundingBoxInternal_5=b.asm.lb).apply(null,arguments)},sd=b._emscripten_bind_PageIterator_Empty_1=function(){return(sd=b._emscripten_bind_PageIterator_Empty_1=b.asm.mb).apply(null,arguments)},td=b._emscripten_bind_PageIterator_BlockType_0=function(){return(td=b._emscripten_bind_PageIterator_BlockType_0=
b.asm.nb).apply(null,arguments)},ud=b._emscripten_bind_PageIterator_BlockPolygon_0=function(){return(ud=b._emscripten_bind_PageIterator_BlockPolygon_0=b.asm.ob).apply(null,arguments)},vd=b._emscripten_bind_PageIterator_GetBinaryImage_1=function(){return(vd=b._emscripten_bind_PageIterator_GetBinaryImage_1=b.asm.pb).apply(null,arguments)},wd=b._emscripten_bind_PageIterator_GetImage_5=function(){return(wd=b._emscripten_bind_PageIterator_GetImage_5=b.asm.qb).apply(null,arguments)},xd=b._emscripten_bind_PageIterator_Baseline_5=
function(){return(xd=b._emscripten_bind_PageIterator_Baseline_5=b.asm.rb).apply(null,arguments)},yd=b._emscripten_bind_PageIterator_Orientation_4=function(){return(yd=b._emscripten_bind_PageIterator_Orientation_4=b.asm.sb).apply(null,arguments)},zd=b._emscripten_bind_PageIterator_ParagraphInfo_4=function(){return(zd=b._emscripten_bind_PageIterator_ParagraphInfo_4=b.asm.tb).apply(null,arguments)},Ad=b._emscripten_bind_PageIterator___destroy___0=function(){return(Ad=b._emscripten_bind_PageIterator___destroy___0=
b.asm.ub).apply(null,arguments)},Bd=b._emscripten_bind_WritingDirection___destroy___0=function(){return(Bd=b._emscripten_bind_WritingDirection___destroy___0=b.asm.vb).apply(null,arguments)},Cd=b._emscripten_bind_WordChoiceIterator_WordChoiceIterator_1=function(){return(Cd=b._emscripten_bind_WordChoiceIterator_WordChoiceIterator_1=b.asm.wb).apply(null,arguments)},Dd=b._emscripten_bind_WordChoiceIterator_Next_0=function(){return(Dd=b._emscripten_bind_WordChoiceIterator_Next_0=b.asm.xb).apply(null,arguments)},
Ed=b._emscripten_bind_WordChoiceIterator_GetUTF8Text_0=function(){return(Ed=b._emscripten_bind_WordChoiceIterator_GetUTF8Text_0=b.asm.yb).apply(null,arguments)},Fd=b._emscripten_bind_WordChoiceIterator_Confidence_0=function(){return(Fd=b._emscripten_bind_WordChoiceIterator_Confidence_0=b.asm.zb).apply(null,arguments)},Gd=b._emscripten_bind_WordChoiceIterator___destroy___0=function(){return(Gd=b._emscripten_bind_WordChoiceIterator___destroy___0=b.asm.Ab).apply(null,arguments)},Hd=b._emscripten_bind_Box_get_x_0=
function(){return(Hd=b._emscripten_bind_Box_get_x_0=b.asm.Bb).apply(null,arguments)},Id=b._emscripten_bind_Box_get_y_0=function(){return(Id=b._emscripten_bind_Box_get_y_0=b.asm.Cb).apply(null,arguments)},Jd=b._emscripten_bind_Box_get_w_0=function(){return(Jd=b._emscripten_bind_Box_get_w_0=b.asm.Db).apply(null,arguments)},Kd=b._emscripten_bind_Box_get_h_0=function(){return(Kd=b._emscripten_bind_Box_get_h_0=b.asm.Eb).apply(null,arguments)},Ld=b._emscripten_bind_Box_get_refcount_0=function(){return(Ld=
b._emscripten_bind_Box_get_refcount_0=b.asm.Fb).apply(null,arguments)},Md=b._emscripten_bind_Box___destroy___0=function(){return(Md=b._emscripten_bind_Box___destroy___0=b.asm.Gb).apply(null,arguments)},Nd=b._emscripten_bind_TessPDFRenderer_TessPDFRenderer_3=function(){return(Nd=b._emscripten_bind_TessPDFRenderer_TessPDFRenderer_3=b.asm.Hb).apply(null,arguments)},Od=b._emscripten_bind_TessPDFRenderer_BeginDocument_1=function(){return(Od=b._emscripten_bind_TessPDFRenderer_BeginDocument_1=b.asm.Ib).apply(null,
arguments)},Pd=b._emscripten_bind_TessPDFRenderer_AddImage_1=function(){return(Pd=b._emscripten_bind_TessPDFRenderer_AddImage_1=b.asm.Jb).apply(null,arguments)},Qd=b._emscripten_bind_TessPDFRenderer_EndDocument_0=function(){return(Qd=b._emscripten_bind_TessPDFRenderer_EndDocument_0=b.asm.Kb).apply(null,arguments)},Rd=b._emscripten_bind_TessPDFRenderer_happy_0=function(){return(Rd=b._emscripten_bind_TessPDFRenderer_happy_0=b.asm.Lb).apply(null,arguments)},Sd=b._emscripten_bind_TessPDFRenderer_file_extension_0=
function(){return(Sd=b._emscripten_bind_TessPDFRenderer_file_extension_0=b.asm.Mb).apply(null,arguments)},Td=b._emscripten_bind_TessPDFRenderer_title_0=function(){return(Td=b._emscripten_bind_TessPDFRenderer_title_0=b.asm.Nb).apply(null,arguments)},Ud=b._emscripten_bind_TessPDFRenderer_imagenum_0=function(){return(Ud=b._emscripten_bind_TessPDFRenderer_imagenum_0=b.asm.Ob).apply(null,arguments)},Vd=b._emscripten_bind_TessPDFRenderer___destroy___0=function(){return(Vd=b._emscripten_bind_TessPDFRenderer___destroy___0=
b.asm.Pb).apply(null,arguments)},Wd=b._emscripten_bind_PixaPtr___destroy___0=function(){return(Wd=b._emscripten_bind_PixaPtr___destroy___0=b.asm.Qb).apply(null,arguments)},Xd=b._emscripten_bind_FloatPtr___destroy___0=function(){return(Xd=b._emscripten_bind_FloatPtr___destroy___0=b.asm.Rb).apply(null,arguments)},Yd=b._emscripten_bind_ChoiceIterator_ChoiceIterator_1=function(){return(Yd=b._emscripten_bind_ChoiceIterator_ChoiceIterator_1=b.asm.Sb).apply(null,arguments)},Zd=b._emscripten_bind_ChoiceIterator_Next_0=
function(){return(Zd=b._emscripten_bind_ChoiceIterator_Next_0=b.asm.Tb).apply(null,arguments)},$d=b._emscripten_bind_ChoiceIterator_GetUTF8Text_0=function(){return($d=b._emscripten_bind_ChoiceIterator_GetUTF8Text_0=b.asm.Ub).apply(null,arguments)},ae=b._emscripten_bind_ChoiceIterator_Confidence_0=function(){return(ae=b._emscripten_bind_ChoiceIterator_Confidence_0=b.asm.Vb).apply(null,arguments)},be=b._emscripten_bind_ChoiceIterator___destroy___0=function(){return(be=b._emscripten_bind_ChoiceIterator___destroy___0=
b.asm.Wb).apply(null,arguments)},ce=b._emscripten_bind_PixPtr___destroy___0=function(){return(ce=b._emscripten_bind_PixPtr___destroy___0=b.asm.Xb).apply(null,arguments)},de=b._emscripten_bind_UNICHARSET_get_script_from_script_id_1=function(){return(de=b._emscripten_bind_UNICHARSET_get_script_from_script_id_1=b.asm.Yb).apply(null,arguments)},ee=b._emscripten_bind_UNICHARSET_get_script_id_from_name_1=function(){return(ee=b._emscripten_bind_UNICHARSET_get_script_id_from_name_1=b.asm.Zb).apply(null,arguments)},
fe=b._emscripten_bind_UNICHARSET_get_script_table_size_0=function(){return(fe=b._emscripten_bind_UNICHARSET_get_script_table_size_0=b.asm._b).apply(null,arguments)},ge=b._emscripten_bind_UNICHARSET___destroy___0=function(){return(ge=b._emscripten_bind_UNICHARSET___destroy___0=b.asm.$b).apply(null,arguments)},he=b._emscripten_bind_IntPtr___destroy___0=function(){return(he=b._emscripten_bind_IntPtr___destroy___0=b.asm.ac).apply(null,arguments)},ie=b._emscripten_bind_Orientation___destroy___0=function(){return(ie=
b._emscripten_bind_Orientation___destroy___0=b.asm.bc).apply(null,arguments)},je=b._emscripten_bind_OSBestResult_get_orientation_id_0=function(){return(je=b._emscripten_bind_OSBestResult_get_orientation_id_0=b.asm.cc).apply(null,arguments)},ke=b._emscripten_bind_OSBestResult_get_script_id_0=function(){return(ke=b._emscripten_bind_OSBestResult_get_script_id_0=b.asm.dc).apply(null,arguments)},le=b._emscripten_bind_OSBestResult_get_sconfidence_0=function(){return(le=b._emscripten_bind_OSBestResult_get_sconfidence_0=
b.asm.ec).apply(null,arguments)},me=b._emscripten_bind_OSBestResult_get_oconfidence_0=function(){return(me=b._emscripten_bind_OSBestResult_get_oconfidence_0=b.asm.fc).apply(null,arguments)},ne=b._emscripten_bind_OSBestResult___destroy___0=function(){return(ne=b._emscripten_bind_OSBestResult___destroy___0=b.asm.gc).apply(null,arguments)},oe=b._emscripten_bind_Boxa_get_n_0=function(){return(oe=b._emscripten_bind_Boxa_get_n_0=b.asm.hc).apply(null,arguments)},pe=b._emscripten_bind_Boxa_get_nalloc_0=function(){return(pe=
b._emscripten_bind_Boxa_get_nalloc_0=b.asm.ic).apply(null,arguments)},qe=b._emscripten_bind_Boxa_get_refcount_0=function(){return(qe=b._emscripten_bind_Boxa_get_refcount_0=b.asm.jc).apply(null,arguments)},re=b._emscripten_bind_Boxa_get_box_0=function(){return(re=b._emscripten_bind_Boxa_get_box_0=b.asm.kc).apply(null,arguments)},se=b._emscripten_bind_Boxa___destroy___0=function(){return(se=b._emscripten_bind_Boxa___destroy___0=b.asm.lc).apply(null,arguments)},te=b._emscripten_bind_PixColormap_get_array_0=
function(){return(te=b._emscripten_bind_PixColormap_get_array_0=b.asm.mc).apply(null,arguments)},ue=b._emscripten_bind_PixColormap_get_depth_0=function(){return(ue=b._emscripten_bind_PixColormap_get_depth_0=b.asm.nc).apply(null,arguments)},ve=b._emscripten_bind_PixColormap_get_nalloc_0=function(){return(ve=b._emscripten_bind_PixColormap_get_nalloc_0=b.asm.oc).apply(null,arguments)},we=b._emscripten_bind_PixColormap_get_n_0=function(){return(we=b._emscripten_bind_PixColormap_get_n_0=b.asm.pc).apply(null,
arguments)},xe=b._emscripten_bind_PixColormap___destroy___0=function(){return(xe=b._emscripten_bind_PixColormap___destroy___0=b.asm.qc).apply(null,arguments)},ye=b._emscripten_bind_Pta_get_n_0=function(){return(ye=b._emscripten_bind_Pta_get_n_0=b.asm.rc).apply(null,arguments)},ze=b._emscripten_bind_Pta_get_nalloc_0=function(){return(ze=b._emscripten_bind_Pta_get_nalloc_0=b.asm.sc).apply(null,arguments)},Ae=b._emscripten_bind_Pta_get_refcount_0=function(){return(Ae=b._emscripten_bind_Pta_get_refcount_0=
b.asm.tc).apply(null,arguments)},Be=b._emscripten_bind_Pta_get_x_0=function(){return(Be=b._emscripten_bind_Pta_get_x_0=b.asm.uc).apply(null,arguments)},Ce=b._emscripten_bind_Pta_get_y_0=function(){return(Ce=b._emscripten_bind_Pta_get_y_0=b.asm.vc).apply(null,arguments)},De=b._emscripten_bind_Pta___destroy___0=function(){return(De=b._emscripten_bind_Pta___destroy___0=b.asm.wc).apply(null,arguments)},Ee=b._emscripten_bind_Pix_get_w_0=function(){return(Ee=b._emscripten_bind_Pix_get_w_0=b.asm.xc).apply(null,
arguments)},Fe=b._emscripten_bind_Pix_get_h_0=function(){return(Fe=b._emscripten_bind_Pix_get_h_0=b.asm.yc).apply(null,arguments)},Ge=b._emscripten_bind_Pix_get_d_0=function(){return(Ge=b._emscripten_bind_Pix_get_d_0=b.asm.zc).apply(null,arguments)},He=b._emscripten_bind_Pix_get_spp_0=function(){return(He=b._emscripten_bind_Pix_get_spp_0=b.asm.Ac).apply(null,arguments)},Ie=b._emscripten_bind_Pix_get_wpl_0=function(){return(Ie=b._emscripten_bind_Pix_get_wpl_0=b.asm.Bc).apply(null,arguments)},Je=b._emscripten_bind_Pix_get_refcount_0=
function(){return(Je=b._emscripten_bind_Pix_get_refcount_0=b.asm.Cc).apply(null,arguments)},Ke=b._emscripten_bind_Pix_get_xres_0=function(){return(Ke=b._emscripten_bind_Pix_get_xres_0=b.asm.Dc).apply(null,arguments)},Le=b._emscripten_bind_Pix_get_yres_0=function(){return(Le=b._emscripten_bind_Pix_get_yres_0=b.asm.Ec).apply(null,arguments)},Me=b._emscripten_bind_Pix_get_informat_0=function(){return(Me=b._emscripten_bind_Pix_get_informat_0=b.asm.Fc).apply(null,arguments)},Ne=b._emscripten_bind_Pix_get_special_0=
function(){return(Ne=b._emscripten_bind_Pix_get_special_0=b.asm.Gc).apply(null,arguments)},Oe=b._emscripten_bind_Pix_get_text_0=function(){return(Oe=b._emscripten_bind_Pix_get_text_0=b.asm.Hc).apply(null,arguments)},Pe=b._emscripten_bind_Pix_get_colormap_0=function(){return(Pe=b._emscripten_bind_Pix_get_colormap_0=b.asm.Ic).apply(null,arguments)},Qe=b._emscripten_bind_Pix_get_data_0=function(){return(Qe=b._emscripten_bind_Pix_get_data_0=b.asm.Jc).apply(null,arguments)},Re=b._emscripten_bind_Pix___destroy___0=
function(){return(Re=b._emscripten_bind_Pix___destroy___0=b.asm.Kc).apply(null,arguments)},Se=b._emscripten_bind_DoublePtr___destroy___0=function(){return(Se=b._emscripten_bind_DoublePtr___destroy___0=b.asm.Lc).apply(null,arguments)},Te=b._emscripten_bind_Dawg___destroy___0=function(){return(Te=b._emscripten_bind_Dawg___destroy___0=b.asm.Mc).apply(null,arguments)},Ue=b._emscripten_bind_BoxPtr___destroy___0=function(){return(Ue=b._emscripten_bind_BoxPtr___destroy___0=b.asm.Nc).apply(null,arguments)},
Ve=b._emscripten_bind_TessBaseAPI_TessBaseAPI_0=function(){return(Ve=b._emscripten_bind_TessBaseAPI_TessBaseAPI_0=b.asm.Oc).apply(null,arguments)},We=b._emscripten_bind_TessBaseAPI_Version_0=function(){return(We=b._emscripten_bind_TessBaseAPI_Version_0=b.asm.Pc).apply(null,arguments)},Xe=b._emscripten_bind_TessBaseAPI_SetInputName_1=function(){return(Xe=b._emscripten_bind_TessBaseAPI_SetInputName_1=b.asm.Qc).apply(null,arguments)},Ye=b._emscripten_bind_TessBaseAPI_GetInputName_0=function(){return(Ye=
b._emscripten_bind_TessBaseAPI_GetInputName_0=b.asm.Rc).apply(null,arguments)},Ze=b._emscripten_bind_TessBaseAPI_SetInputImage_1=function(){return(Ze=b._emscripten_bind_TessBaseAPI_SetInputImage_1=b.asm.Sc).apply(null,arguments)},$e=b._emscripten_bind_TessBaseAPI_GetInputImage_0=function(){return($e=b._emscripten_bind_TessBaseAPI_GetInputImage_0=b.asm.Tc).apply(null,arguments)},af=b._emscripten_bind_TessBaseAPI_GetSourceYResolution_0=function(){return(af=b._emscripten_bind_TessBaseAPI_GetSourceYResolution_0=
b.asm.Uc).apply(null,arguments)},bf=b._emscripten_bind_TessBaseAPI_GetDatapath_0=function(){return(bf=b._emscripten_bind_TessBaseAPI_GetDatapath_0=b.asm.Vc).apply(null,arguments)},cf=b._emscripten_bind_TessBaseAPI_SetOutputName_1=function(){return(cf=b._emscripten_bind_TessBaseAPI_SetOutputName_1=b.asm.Wc).apply(null,arguments)},df=b._emscripten_bind_TessBaseAPI_SetVariable_2=function(){return(df=b._emscripten_bind_TessBaseAPI_SetVariable_2=b.asm.Xc).apply(null,arguments)},ef=b._emscripten_bind_TessBaseAPI_SetDebugVariable_2=
function(){return(ef=b._emscripten_bind_TessBaseAPI_SetDebugVariable_2=b.asm.Yc).apply(null,arguments)},ff=b._emscripten_bind_TessBaseAPI_GetIntVariable_2=function(){return(ff=b._emscripten_bind_TessBaseAPI_GetIntVariable_2=b.asm.Zc).apply(null,arguments)},gf=b._emscripten_bind_TessBaseAPI_GetBoolVariable_2=function(){return(gf=b._emscripten_bind_TessBaseAPI_GetBoolVariable_2=b.asm._c).apply(null,arguments)},hf=b._emscripten_bind_TessBaseAPI_GetDoubleVariable_2=function(){return(hf=b._emscripten_bind_TessBaseAPI_GetDoubleVariable_2=
b.asm.$c).apply(null,arguments)},jf=b._emscripten_bind_TessBaseAPI_GetStringVariable_1=function(){return(jf=b._emscripten_bind_TessBaseAPI_GetStringVariable_1=b.asm.ad).apply(null,arguments)},kf=b._emscripten_bind_TessBaseAPI_SaveParameters_1=function(){return(kf=b._emscripten_bind_TessBaseAPI_SaveParameters_1=b.asm.bd).apply(null,arguments)},lf=b._emscripten_bind_TessBaseAPI_RestoreParameters_1=function(){return(lf=b._emscripten_bind_TessBaseAPI_RestoreParameters_1=b.asm.cd).apply(null,arguments)},
mf=b._emscripten_bind_TessBaseAPI_Init_2=function(){return(mf=b._emscripten_bind_TessBaseAPI_Init_2=b.asm.dd).apply(null,arguments)},nf=b._emscripten_bind_TessBaseAPI_Init_3=function(){return(nf=b._emscripten_bind_TessBaseAPI_Init_3=b.asm.ed).apply(null,arguments)},of=b._emscripten_bind_TessBaseAPI_Init_4=function(){return(of=b._emscripten_bind_TessBaseAPI_Init_4=b.asm.fd).apply(null,arguments)},pf=b._emscripten_bind_TessBaseAPI_GetInitLanguagesAsString_0=function(){return(pf=b._emscripten_bind_TessBaseAPI_GetInitLanguagesAsString_0=
b.asm.gd).apply(null,arguments)},qf=b._emscripten_bind_TessBaseAPI_InitForAnalysePage_0=function(){return(qf=b._emscripten_bind_TessBaseAPI_InitForAnalysePage_0=b.asm.hd).apply(null,arguments)},rf=b._emscripten_bind_TessBaseAPI_ReadConfigFile_1=function(){return(rf=b._emscripten_bind_TessBaseAPI_ReadConfigFile_1=b.asm.id).apply(null,arguments)},sf=b._emscripten_bind_TessBaseAPI_ReadDebugConfigFile_1=function(){return(sf=b._emscripten_bind_TessBaseAPI_ReadDebugConfigFile_1=b.asm.jd).apply(null,arguments)},
tf=b._emscripten_bind_TessBaseAPI_SetPageSegMode_1=function(){return(tf=b._emscripten_bind_TessBaseAPI_SetPageSegMode_1=b.asm.kd).apply(null,arguments)},uf=b._emscripten_bind_TessBaseAPI_GetPageSegMode_0=function(){return(uf=b._emscripten_bind_TessBaseAPI_GetPageSegMode_0=b.asm.ld).apply(null,arguments)},vf=b._emscripten_bind_TessBaseAPI_TesseractRect_7=function(){return(vf=b._emscripten_bind_TessBaseAPI_TesseractRect_7=b.asm.md).apply(null,arguments)},wf=b._emscripten_bind_TessBaseAPI_ClearAdaptiveClassifier_0=
function(){return(wf=b._emscripten_bind_TessBaseAPI_ClearAdaptiveClassifier_0=b.asm.nd).apply(null,arguments)},xf=b._emscripten_bind_TessBaseAPI_SetImage_1=function(){return(xf=b._emscripten_bind_TessBaseAPI_SetImage_1=b.asm.od).apply(null,arguments)},yf=b._emscripten_bind_TessBaseAPI_SetImage_5=function(){return(yf=b._emscripten_bind_TessBaseAPI_SetImage_5=b.asm.pd).apply(null,arguments)},zf=b._emscripten_bind_TessBaseAPI_SetImageFile_1=function(){return(zf=b._emscripten_bind_TessBaseAPI_SetImageFile_1=
b.asm.qd).apply(null,arguments)},Af=b._emscripten_bind_TessBaseAPI_SetSourceResolution_1=function(){return(Af=b._emscripten_bind_TessBaseAPI_SetSourceResolution_1=b.asm.rd).apply(null,arguments)},Bf=b._emscripten_bind_TessBaseAPI_SetRectangle_4=function(){return(Bf=b._emscripten_bind_TessBaseAPI_SetRectangle_4=b.asm.sd).apply(null,arguments)},Cf=b._emscripten_bind_TessBaseAPI_GetThresholdedImage_0=function(){return(Cf=b._emscripten_bind_TessBaseAPI_GetThresholdedImage_0=b.asm.td).apply(null,arguments)},
Df=b._emscripten_bind_TessBaseAPI_WriteImage_0=function(){return(Df=b._emscripten_bind_TessBaseAPI_WriteImage_0=b.asm.ud).apply(null,arguments)},Ef=b._emscripten_bind_TessBaseAPI_FindLines_0=function(){return(Ef=b._emscripten_bind_TessBaseAPI_FindLines_0=b.asm.vd).apply(null,arguments)},Ff=b._emscripten_bind_TessBaseAPI_GetGradient_0=function(){return(Ff=b._emscripten_bind_TessBaseAPI_GetGradient_0=b.asm.wd).apply(null,arguments)},Gf=b._emscripten_bind_TessBaseAPI_GetRegions_1=function(){return(Gf=
b._emscripten_bind_TessBaseAPI_GetRegions_1=b.asm.xd).apply(null,arguments)},Hf=b._emscripten_bind_TessBaseAPI_GetTextlines_2=function(){return(Hf=b._emscripten_bind_TessBaseAPI_GetTextlines_2=b.asm.yd).apply(null,arguments)},If=b._emscripten_bind_TessBaseAPI_GetTextlines_5=function(){return(If=b._emscripten_bind_TessBaseAPI_GetTextlines_5=b.asm.zd).apply(null,arguments)},Jf=b._emscripten_bind_TessBaseAPI_GetStrips_2=function(){return(Jf=b._emscripten_bind_TessBaseAPI_GetStrips_2=b.asm.Ad).apply(null,
arguments)},Kf=b._emscripten_bind_TessBaseAPI_GetWords_1=function(){return(Kf=b._emscripten_bind_TessBaseAPI_GetWords_1=b.asm.Bd).apply(null,arguments)},Lf=b._emscripten_bind_TessBaseAPI_GetConnectedComponents_1=function(){return(Lf=b._emscripten_bind_TessBaseAPI_GetConnectedComponents_1=b.asm.Cd).apply(null,arguments)},Mf=b._emscripten_bind_TessBaseAPI_GetComponentImages_4=function(){return(Mf=b._emscripten_bind_TessBaseAPI_GetComponentImages_4=b.asm.Dd).apply(null,arguments)},Nf=b._emscripten_bind_TessBaseAPI_GetComponentImages_7=
function(){return(Nf=b._emscripten_bind_TessBaseAPI_GetComponentImages_7=b.asm.Ed).apply(null,arguments)},Of=b._emscripten_bind_TessBaseAPI_GetThresholdedImageScaleFactor_0=function(){return(Of=b._emscripten_bind_TessBaseAPI_GetThresholdedImageScaleFactor_0=b.asm.Fd).apply(null,arguments)},Pf=b._emscripten_bind_TessBaseAPI_AnalyseLayout_0=function(){return(Pf=b._emscripten_bind_TessBaseAPI_AnalyseLayout_0=b.asm.Gd).apply(null,arguments)},Qf=b._emscripten_bind_TessBaseAPI_AnalyseLayout_1=function(){return(Qf=
b._emscripten_bind_TessBaseAPI_AnalyseLayout_1=b.asm.Hd).apply(null,arguments)},Rf=b._emscripten_bind_TessBaseAPI_Recognize_1=function(){return(Rf=b._emscripten_bind_TessBaseAPI_Recognize_1=b.asm.Id).apply(null,arguments)},Sf=b._emscripten_bind_TessBaseAPI_ProcessPages_4=function(){return(Sf=b._emscripten_bind_TessBaseAPI_ProcessPages_4=b.asm.Jd).apply(null,arguments)},Tf=b._emscripten_bind_TessBaseAPI_ProcessPage_6=function(){return(Tf=b._emscripten_bind_TessBaseAPI_ProcessPage_6=b.asm.Kd).apply(null,
arguments)},Uf=b._emscripten_bind_TessBaseAPI_GetIterator_0=function(){return(Uf=b._emscripten_bind_TessBaseAPI_GetIterator_0=b.asm.Ld).apply(null,arguments)},Vf=b._emscripten_bind_TessBaseAPI_GetUTF8Text_0=function(){return(Vf=b._emscripten_bind_TessBaseAPI_GetUTF8Text_0=b.asm.Md).apply(null,arguments)},Wf=b._emscripten_bind_TessBaseAPI_GetHOCRText_1=function(){return(Wf=b._emscripten_bind_TessBaseAPI_GetHOCRText_1=b.asm.Nd).apply(null,arguments)},Xf=b._emscripten_bind_TessBaseAPI_GetJSONText_1=
function(){return(Xf=b._emscripten_bind_TessBaseAPI_GetJSONText_1=b.asm.Od).apply(null,arguments)},Yf=b._emscripten_bind_TessBaseAPI_GetTSVText_1=function(){return(Yf=b._emscripten_bind_TessBaseAPI_GetTSVText_1=b.asm.Pd).apply(null,arguments)},Zf=b._emscripten_bind_TessBaseAPI_GetBoxText_1=function(){return(Zf=b._emscripten_bind_TessBaseAPI_GetBoxText_1=b.asm.Qd).apply(null,arguments)},$f=b._emscripten_bind_TessBaseAPI_GetUNLVText_0=function(){return($f=b._emscripten_bind_TessBaseAPI_GetUNLVText_0=
b.asm.Rd).apply(null,arguments)},ag=b._emscripten_bind_TessBaseAPI_GetOsdText_1=function(){return(ag=b._emscripten_bind_TessBaseAPI_GetOsdText_1=b.asm.Sd).apply(null,arguments)},bg=b._emscripten_bind_TessBaseAPI_MeanTextConf_0=function(){return(bg=b._emscripten_bind_TessBaseAPI_MeanTextConf_0=b.asm.Td).apply(null,arguments)},cg=b._emscripten_bind_TessBaseAPI_AllWordConfidences_0=function(){return(cg=b._emscripten_bind_TessBaseAPI_AllWordConfidences_0=b.asm.Ud).apply(null,arguments)},dg=b._emscripten_bind_TessBaseAPI_Clear_0=
function(){return(dg=b._emscripten_bind_TessBaseAPI_Clear_0=b.asm.Vd).apply(null,arguments)},eg=b._emscripten_bind_TessBaseAPI_End_0=function(){return(eg=b._emscripten_bind_TessBaseAPI_End_0=b.asm.Wd).apply(null,arguments)},fg=b._emscripten_bind_TessBaseAPI_ClearPersistentCache_0=function(){return(fg=b._emscripten_bind_TessBaseAPI_ClearPersistentCache_0=b.asm.Xd).apply(null,arguments)},gg=b._emscripten_bind_TessBaseAPI_IsValidWord_1=function(){return(gg=b._emscripten_bind_TessBaseAPI_IsValidWord_1=
b.asm.Yd).apply(null,arguments)},hg=b._emscripten_bind_TessBaseAPI_IsValidCharacter_1=function(){return(hg=b._emscripten_bind_TessBaseAPI_IsValidCharacter_1=b.asm.Zd).apply(null,arguments)},ig=b._emscripten_bind_TessBaseAPI_DetectOS_1=function(){return(ig=b._emscripten_bind_TessBaseAPI_DetectOS_1=b.asm._d).apply(null,arguments)},jg=b._emscripten_bind_TessBaseAPI_GetUnichar_1=function(){return(jg=b._emscripten_bind_TessBaseAPI_GetUnichar_1=b.asm.$d).apply(null,arguments)},kg=b._emscripten_bind_TessBaseAPI_GetDawg_1=
function(){return(kg=b._emscripten_bind_TessBaseAPI_GetDawg_1=b.asm.ae).apply(null,arguments)},lg=b._emscripten_bind_TessBaseAPI_NumDawgs_0=function(){return(lg=b._emscripten_bind_TessBaseAPI_NumDawgs_0=b.asm.be).apply(null,arguments)},mg=b._emscripten_bind_TessBaseAPI_oem_0=function(){return(mg=b._emscripten_bind_TessBaseAPI_oem_0=b.asm.ce).apply(null,arguments)},ng=b._emscripten_bind_TessBaseAPI___destroy___0=function(){return(ng=b._emscripten_bind_TessBaseAPI___destroy___0=b.asm.de).apply(null,
arguments)},og=b._emscripten_bind_OSResults_OSResults_0=function(){return(og=b._emscripten_bind_OSResults_OSResults_0=b.asm.ee).apply(null,arguments)},pg=b._emscripten_bind_OSResults_print_scores_0=function(){return(pg=b._emscripten_bind_OSResults_print_scores_0=b.asm.fe).apply(null,arguments)},qg=b._emscripten_bind_OSResults_get_best_result_0=function(){return(qg=b._emscripten_bind_OSResults_get_best_result_0=b.asm.ge).apply(null,arguments)},rg=b._emscripten_bind_OSResults_get_unicharset_0=function(){return(rg=
b._emscripten_bind_OSResults_get_unicharset_0=b.asm.he).apply(null,arguments)},sg=b._emscripten_bind_OSResults___destroy___0=function(){return(sg=b._emscripten_bind_OSResults___destroy___0=b.asm.ie).apply(null,arguments)},tg=b._emscripten_bind_Pixa_get_n_0=function(){return(tg=b._emscripten_bind_Pixa_get_n_0=b.asm.je).apply(null,arguments)},ug=b._emscripten_bind_Pixa_get_nalloc_0=function(){return(ug=b._emscripten_bind_Pixa_get_nalloc_0=b.asm.ke).apply(null,arguments)},vg=b._emscripten_bind_Pixa_get_refcount_0=
function(){return(vg=b._emscripten_bind_Pixa_get_refcount_0=b.asm.le).apply(null,arguments)},wg=b._emscripten_bind_Pixa_get_pix_0=function(){return(wg=b._emscripten_bind_Pixa_get_pix_0=b.asm.me).apply(null,arguments)},xg=b._emscripten_bind_Pixa_get_boxa_0=function(){return(xg=b._emscripten_bind_Pixa_get_boxa_0=b.asm.ne).apply(null,arguments)},yg=b._emscripten_bind_Pixa___destroy___0=function(){return(yg=b._emscripten_bind_Pixa___destroy___0=b.asm.oe).apply(null,arguments)},zg=b._emscripten_enum_PageIteratorLevel_RIL_BLOCK=
function(){return(zg=b._emscripten_enum_PageIteratorLevel_RIL_BLOCK=b.asm.pe).apply(null,arguments)},Ag=b._emscripten_enum_PageIteratorLevel_RIL_PARA=function(){return(Ag=b._emscripten_enum_PageIteratorLevel_RIL_PARA=b.asm.qe).apply(null,arguments)},Bg=b._emscripten_enum_PageIteratorLevel_RIL_TEXTLINE=function(){return(Bg=b._emscripten_enum_PageIteratorLevel_RIL_TEXTLINE=b.asm.re).apply(null,arguments)},Cg=b._emscripten_enum_PageIteratorLevel_RIL_WORD=function(){return(Cg=b._emscripten_enum_PageIteratorLevel_RIL_WORD=
b.asm.se).apply(null,arguments)},Dg=b._emscripten_enum_PageIteratorLevel_RIL_SYMBOL=function(){return(Dg=b._emscripten_enum_PageIteratorLevel_RIL_SYMBOL=b.asm.te).apply(null,arguments)},Eg=b._emscripten_enum_OcrEngineMode_OEM_TESSERACT_ONLY=function(){return(Eg=b._emscripten_enum_OcrEngineMode_OEM_TESSERACT_ONLY=b.asm.ue).apply(null,arguments)},Fg=b._emscripten_enum_OcrEngineMode_OEM_LSTM_ONLY=function(){return(Fg=b._emscripten_enum_OcrEngineMode_OEM_LSTM_ONLY=b.asm.ve).apply(null,arguments)},Gg=
b._emscripten_enum_OcrEngineMode_OEM_TESSERACT_LSTM_COMBINED=function(){return(Gg=b._emscripten_enum_OcrEngineMode_OEM_TESSERACT_LSTM_COMBINED=b.asm.we).apply(null,arguments)},Hg=b._emscripten_enum_OcrEngineMode_OEM_DEFAULT=function(){return(Hg=b._emscripten_enum_OcrEngineMode_OEM_DEFAULT=b.asm.xe).apply(null,arguments)},Ig=b._emscripten_enum_OcrEngineMode_OEM_COUNT=function(){return(Ig=b._emscripten_enum_OcrEngineMode_OEM_COUNT=b.asm.ye).apply(null,arguments)},Jg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_LEFT_TO_RIGHT=
function(){return(Jg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_LEFT_TO_RIGHT=b.asm.ze).apply(null,arguments)},Kg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_RIGHT_TO_LEFT=function(){return(Kg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_RIGHT_TO_LEFT=b.asm.Ae).apply(null,arguments)},Lg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_TOP_TO_BOTTOM=function(){return(Lg=b._emscripten_enum_WritingDirection__WRITING_DIRECTION_TOP_TO_BOTTOM=b.asm.Be).apply(null,
arguments)},Mg=b._emscripten_enum_PolyBlockType_PT_UNKNOWN=function(){return(Mg=b._emscripten_enum_PolyBlockType_PT_UNKNOWN=b.asm.Ce).apply(null,arguments)},Ng=b._emscripten_enum_PolyBlockType_PT_FLOWING_TEXT=function(){return(Ng=b._emscripten_enum_PolyBlockType_PT_FLOWING_TEXT=b.asm.De).apply(null,arguments)},Og=b._emscripten_enum_PolyBlockType_PT_HEADING_TEXT=function(){return(Og=b._emscripten_enum_PolyBlockType_PT_HEADING_TEXT=b.asm.Ee).apply(null,arguments)},Pg=b._emscripten_enum_PolyBlockType_PT_PULLOUT_TEXT=
function(){return(Pg=b._emscripten_enum_PolyBlockType_PT_PULLOUT_TEXT=b.asm.Fe).apply(null,arguments)},Qg=b._emscripten_enum_PolyBlockType_PT_EQUATION=function(){return(Qg=b._emscripten_enum_PolyBlockType_PT_EQUATION=b.asm.Ge).apply(null,arguments)},Rg=b._emscripten_enum_PolyBlockType_PT_INLINE_EQUATION=function(){return(Rg=b._emscripten_enum_PolyBlockType_PT_INLINE_EQUATION=b.asm.He).apply(null,arguments)},Sg=b._emscripten_enum_PolyBlockType_PT_TABLE=function(){return(Sg=b._emscripten_enum_PolyBlockType_PT_TABLE=
b.asm.Ie).apply(null,arguments)},Tg=b._emscripten_enum_PolyBlockType_PT_VERTICAL_TEXT=function(){return(Tg=b._emscripten_enum_PolyBlockType_PT_VERTICAL_TEXT=b.asm.Je).apply(null,arguments)},Ug=b._emscripten_enum_PolyBlockType_PT_CAPTION_TEXT=function(){return(Ug=b._emscripten_enum_PolyBlockType_PT_CAPTION_TEXT=b.asm.Ke).apply(null,arguments)},Vg=b._emscripten_enum_PolyBlockType_PT_FLOWING_IMAGE=function(){return(Vg=b._emscripten_enum_PolyBlockType_PT_FLOWING_IMAGE=b.asm.Le).apply(null,arguments)},
Wg=b._emscripten_enum_PolyBlockType_PT_HEADING_IMAGE=function(){return(Wg=b._emscripten_enum_PolyBlockType_PT_HEADING_IMAGE=b.asm.Me).apply(null,arguments)},Xg=b._emscripten_enum_PolyBlockType_PT_PULLOUT_IMAGE=function(){return(Xg=b._emscripten_enum_PolyBlockType_PT_PULLOUT_IMAGE=b.asm.Ne).apply(null,arguments)},Yg=b._emscripten_enum_PolyBlockType_PT_HORZ_LINE=function(){return(Yg=b._emscripten_enum_PolyBlockType_PT_HORZ_LINE=b.asm.Oe).apply(null,arguments)},Zg=b._emscripten_enum_PolyBlockType_PT_VERT_LINE=
function(){return(Zg=b._emscripten_enum_PolyBlockType_PT_VERT_LINE=b.asm.Pe).apply(null,arguments)},$g=b._emscripten_enum_PolyBlockType_PT_NOISE=function(){return($g=b._emscripten_enum_PolyBlockType_PT_NOISE=b.asm.Qe).apply(null,arguments)},ah=b._emscripten_enum_PolyBlockType_PT_COUNT=function(){return(ah=b._emscripten_enum_PolyBlockType_PT_COUNT=b.asm.Re).apply(null,arguments)},bh=b._emscripten_enum_StrongScriptDirection_DIR_NEUTRAL=function(){return(bh=b._emscripten_enum_StrongScriptDirection_DIR_NEUTRAL=
b.asm.Se).apply(null,arguments)},ch=b._emscripten_enum_StrongScriptDirection_DIR_LEFT_TO_RIGHT=function(){return(ch=b._emscripten_enum_StrongScriptDirection_DIR_LEFT_TO_RIGHT=b.asm.Te).apply(null,arguments)},dh=b._emscripten_enum_StrongScriptDirection_DIR_RIGHT_TO_LEFT=function(){return(dh=b._emscripten_enum_StrongScriptDirection_DIR_RIGHT_TO_LEFT=b.asm.Ue).apply(null,arguments)},eh=b._emscripten_enum_StrongScriptDirection_DIR_MIX=function(){return(eh=b._emscripten_enum_StrongScriptDirection_DIR_MIX=
b.asm.Ve).apply(null,arguments)},fh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_UNKNOWN=function(){return(fh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_UNKNOWN=b.asm.We).apply(null,arguments)},gh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_LEFT=function(){return(gh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_LEFT=b.asm.Xe).apply(null,arguments)},hh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_CENTER=function(){return(hh=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_CENTER=
b.asm.Ye).apply(null,arguments)},ih=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_RIGHT=function(){return(ih=b._emscripten_enum_ParagraphJustification__JUSTIFICATION_RIGHT=b.asm.Ze).apply(null,arguments)},jh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_LEFT_TO_RIGHT=function(){return(jh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_LEFT_TO_RIGHT=b.asm._e).apply(null,arguments)},kh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_RIGHT_TO_LEFT=function(){return(kh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_RIGHT_TO_LEFT=
b.asm.$e).apply(null,arguments)},lh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_TOP_TO_BOTTOM=function(){return(lh=b._emscripten_enum_TextlineOrder__TEXTLINE_ORDER_TOP_TO_BOTTOM=b.asm.af).apply(null,arguments)},mh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_UP=function(){return(mh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_UP=b.asm.bf).apply(null,arguments)},nh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_RIGHT=function(){return(nh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_RIGHT=
b.asm.cf).apply(null,arguments)},oh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_DOWN=function(){return(oh=b._emscripten_enum_Orientation__ORIENTATION_PAGE_DOWN=b.asm.df).apply(null,arguments)},ph=b._emscripten_enum_Orientation__ORIENTATION_PAGE_LEFT=function(){return(ph=b._emscripten_enum_Orientation__ORIENTATION_PAGE_LEFT=b.asm.ef).apply(null,arguments)},qh=b._emscripten_enum_PageSegMode_PSM_OSD_ONLY=function(){return(qh=b._emscripten_enum_PageSegMode_PSM_OSD_ONLY=b.asm.ff).apply(null,arguments)},
rh=b._emscripten_enum_PageSegMode_PSM_AUTO_OSD=function(){return(rh=b._emscripten_enum_PageSegMode_PSM_AUTO_OSD=b.asm.gf).apply(null,arguments)},sh=b._emscripten_enum_PageSegMode_PSM_AUTO_ONLY=function(){return(sh=b._emscripten_enum_PageSegMode_PSM_AUTO_ONLY=b.asm.hf).apply(null,arguments)},th=b._emscripten_enum_PageSegMode_PSM_AUTO=function(){return(th=b._emscripten_enum_PageSegMode_PSM_AUTO=b.asm.jf).apply(null,arguments)},uh=b._emscripten_enum_PageSegMode_PSM_SINGLE_COLUMN=function(){return(uh=
b._emscripten_enum_PageSegMode_PSM_SINGLE_COLUMN=b.asm.kf).apply(null,arguments)},vh=b._emscripten_enum_PageSegMode_PSM_SINGLE_BLOCK_VERT_TEXT=function(){return(vh=b._emscripten_enum_PageSegMode_PSM_SINGLE_BLOCK_VERT_TEXT=b.asm.lf).apply(null,arguments)},wh=b._emscripten_enum_PageSegMode_PSM_SINGLE_BLOCK=function(){return(wh=b._emscripten_enum_PageSegMode_PSM_SINGLE_BLOCK=b.asm.mf).apply(null,arguments)},xh=b._emscripten_enum_PageSegMode_PSM_SINGLE_LINE=function(){return(xh=b._emscripten_enum_PageSegMode_PSM_SINGLE_LINE=
b.asm.nf).apply(null,arguments)},yh=b._emscripten_enum_PageSegMode_PSM_SINGLE_WORD=function(){return(yh=b._emscripten_enum_PageSegMode_PSM_SINGLE_WORD=b.asm.of).apply(null,arguments)},zh=b._emscripten_enum_PageSegMode_PSM_CIRCLE_WORD=function(){return(zh=b._emscripten_enum_PageSegMode_PSM_CIRCLE_WORD=b.asm.pf).apply(null,arguments)},Ah=b._emscripten_enum_PageSegMode_PSM_SINGLE_CHAR=function(){return(Ah=b._emscripten_enum_PageSegMode_PSM_SINGLE_CHAR=b.asm.qf).apply(null,arguments)},Bh=b._emscripten_enum_PageSegMode_PSM_SPARSE_TEXT=
function(){return(Bh=b._emscripten_enum_PageSegMode_PSM_SPARSE_TEXT=b.asm.rf).apply(null,arguments)},Ch=b._emscripten_enum_PageSegMode_PSM_SPARSE_TEXT_OSD=function(){return(Ch=b._emscripten_enum_PageSegMode_PSM_SPARSE_TEXT_OSD=b.asm.sf).apply(null,arguments)},Dh=b._emscripten_enum_PageSegMode_PSM_RAW_LINE=function(){return(Dh=b._emscripten_enum_PageSegMode_PSM_RAW_LINE=b.asm.tf).apply(null,arguments)},Eh=b._emscripten_enum_PageSegMode_PSM_COUNT=function(){return(Eh=b._emscripten_enum_PageSegMode_PSM_COUNT=
b.asm.uf).apply(null,arguments)};b._pixDestroy=function(){return(b._pixDestroy=b.asm.wf).apply(null,arguments)};b._ptaDestroy=function(){return(b._ptaDestroy=b.asm.xf).apply(null,arguments)};b._boxaDestroy=function(){return(b._boxaDestroy=b.asm.yf).apply(null,arguments)};b._pixaDestroy=function(){return(b._pixaDestroy=b.asm.zf).apply(null,arguments)};b._pixReadMem=function(){return(b._pixReadMem=b.asm.Af).apply(null,arguments)};function Qb(){return(Qb=b.asm.Bf).apply(null,arguments)}
var Fh=b._free=function(){return(Fh=b._free=b.asm.Cf).apply(null,arguments)},Eb=b._malloc=function(){return(Eb=b._malloc=b.asm.Df).apply(null,arguments)};b._pixReadHeaderMem=function(){return(b._pixReadHeaderMem=b.asm.Ef).apply(null,arguments)};function D(){return(D=b.asm.Ff).apply(null,arguments)}function Gh(){return(Gh=b.asm.Gf).apply(null,arguments)}b.___emscripten_embedded_file_data=391584;function Tb(a,c,d,e){var g=D();try{return Ob(a)(c,d,e)}catch(h){Gh(g);if(h!==h+0)throw h;yb()}}
function Wb(a,c){var d=D();try{Ob(a)(c)}catch(e){Gh(d);if(e!==e+0)throw e;yb()}}function Rb(a,c){var d=D();try{return Ob(a)(c)}catch(e){Gh(d);if(e!==e+0)throw e;yb()}}function Yb(a,c,d,e){var g=D();try{Ob(a)(c,d,e)}catch(h){Gh(g);if(h!==h+0)throw h;yb()}}function Xb(a,c,d){var e=D();try{Ob(a)(c,d)}catch(g){Gh(e);if(g!==g+0)throw g;yb()}}function Sb(a,c,d){var e=D();try{return Ob(a)(c,d)}catch(g){Gh(e);if(g!==g+0)throw g;yb()}}
function Ub(a,c,d,e,g){var h=D();try{return Ob(a)(c,d,e,g)}catch(k){Gh(h);if(k!==k+0)throw k;yb()}}function Zb(a,c,d,e,g){var h=D();try{Ob(a)(c,d,e,g)}catch(k){Gh(h);if(k!==k+0)throw k;yb()}}function Vb(a,c,d,e,g,h){var k=D();try{return Ob(a)(c,d,e,g,h)}catch(m){Gh(k);if(m!==m+0)throw m;yb()}}function ac(a,c,d,e,g,h,k,m,v,q){var t=D();try{Ob(a)(c,d,e,g,h,k,m,v,q)}catch(F){Gh(t);if(F!==F+0)throw F;yb()}}
function $b(a,c,d,e,g,h){var k=D();try{Ob(a)(c,d,e,g,h)}catch(m){Gh(k);if(m!==m+0)throw m;yb()}}b.addRunDependency=Ga;b.removeRunDependency=Ha;b.FS_createPath=B.Ig;b.FS_createDataFile=B.zg;b.FS_createLazyFile=B.gh;b.FS_createDevice=B.Vf;b.FS_unlink=B.unlink;b.setValue=Xa;b.getValue=Wa;b.FS_createPreloadedFile=B.hh;b.FS=B;var Hh;Fa=function Ih(){Hh||Jh();Hh||(Fa=Ih)};
function Jh(){function a(){if(!Hh&&(Hh=!0,b.calledRun=!0,!ra)){Ba=!0;b.noFSInit||B.hg.Tg||B.hg();B.th=!1;Ra(za);aa(b);if(b.onRuntimeInitialized)b.onRuntimeInitialized();if(b.postRun)for("function"==typeof b.postRun&&(b.postRun=[b.postRun]);b.postRun.length;){var c=b.postRun.shift();Aa.unshift(c)}Ra(Aa)}}if(!(0<Da)){if(b.preRun)for("function"==typeof b.preRun&&(b.preRun=[b.preRun]);b.preRun.length;)Ca();Ra(ya);0<Da||(b.setStatus?(b.setStatus("Running..."),setTimeout(function(){setTimeout(function(){b.setStatus("")},
1);a()},1)):a())}}if(b.preInit)for("function"==typeof b.preInit&&(b.preInit=[b.preInit]);0<b.preInit.length;)b.preInit.pop()();Jh();function G(){}G.prototype=Object.create(G.prototype);G.prototype.constructor=G;G.prototype.Mf=G;G.Nf={};b.WrapperObject=G;function Kh(a){return(a||G).Nf}b.getCache=Kh;function H(a,c){var d=Kh(c),e=d[a];if(e)return e;e=Object.create((c||G).prototype);e.Hf=a;return d[a]=e}b.wrapPointer=H;b.castObject=function(a,c){return H(a.Hf,c)};b.NULL=H(0);
b.destroy=function(a){if(!a.__destroy__)throw"Error: Cannot destroy object. (Did you create it yourself?)";a.__destroy__();delete Kh(a.Mf)[a.Hf]};b.compare=function(a,c){return a.Hf===c.Hf};b.getPointer=function(a){return a.Hf};b.getClass=function(a){return a.Mf};var Lh=0,Mh=0,Nh=0,Oh=[],Ph=0;function I(){if(Ph){for(var a=0;a<Oh.length;a++)b._free(Oh[a]);Oh.length=0;b._free(Lh);Lh=0;Mh+=Ph;Ph=0}Lh||(Mh+=128,(Lh=b._malloc(Mh))||p());Nh=0}
function J(a){if("string"===typeof a){a=jb(a);var c=r;Lh||p();c=a.length*c.BYTES_PER_ELEMENT;c=c+7&-8;if(Nh+c>=Mh){0<c||p();Ph+=c;var d=b._malloc(c);Oh.push(d)}else d=Lh+Nh,Nh+=c;c=d;d=r;var e=c;switch(d.BYTES_PER_ELEMENT){case 2:e>>=1;break;case 4:e>>=2;break;case 8:e>>=3}for(var g=0;g<a.length;g++)d[e+g]=a[g];return c}return a}function Qh(){throw"cannot construct a ParagraphJustification, no constructor in IDL";}Qh.prototype=Object.create(G.prototype);Qh.prototype.constructor=Qh;
Qh.prototype.Mf=Qh;Qh.Nf={};b.ParagraphJustification=Qh;Qh.prototype.__destroy__=function(){cc(this.Hf)};function Rh(){throw"cannot construct a BoolPtr, no constructor in IDL";}Rh.prototype=Object.create(G.prototype);Rh.prototype.constructor=Rh;Rh.prototype.Mf=Rh;Rh.Nf={};b.BoolPtr=Rh;Rh.prototype.__destroy__=function(){dc(this.Hf)};function K(){throw"cannot construct a TessResultRenderer, no constructor in IDL";}K.prototype=Object.create(G.prototype);K.prototype.constructor=K;K.prototype.Mf=K;
K.Nf={};b.TessResultRenderer=K;K.prototype.BeginDocument=function(a){var c=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);return!!ec(c,a)};K.prototype.AddImage=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return!!fc(c,a)};K.prototype.EndDocument=function(){return!!gc(this.Hf)};K.prototype.happy=function(){return!!hc(this.Hf)};K.prototype.file_extension=function(){return A(ic(this.Hf))};K.prototype.title=K.prototype.title=function(){return A(jc(this.Hf))};K.prototype.imagenum=function(){return kc(this.Hf)};
K.prototype.__destroy__=function(){lc(this.Hf)};function Sh(){throw"cannot construct a LongStarPtr, no constructor in IDL";}Sh.prototype=Object.create(G.prototype);Sh.prototype.constructor=Sh;Sh.prototype.Mf=Sh;Sh.Nf={};b.LongStarPtr=Sh;Sh.prototype.__destroy__=function(){mc(this.Hf)};function Th(){throw"cannot construct a VoidPtr, no constructor in IDL";}Th.prototype=Object.create(G.prototype);Th.prototype.constructor=Th;Th.prototype.Mf=Th;Th.Nf={};b.VoidPtr=Th;Th.prototype.__destroy__=function(){nc(this.Hf)};
function L(a){a&&"object"===typeof a&&(a=a.Hf);this.Hf=oc(a);Kh(L)[this.Hf]=this}L.prototype=Object.create(G.prototype);L.prototype.constructor=L;L.prototype.Mf=L;L.Nf={};b.ResultIterator=L;L.prototype.Begin=function(){pc(this.Hf)};L.prototype.RestartParagraph=function(){qc(this.Hf)};L.prototype.IsWithinFirstTextlineOfParagraph=function(){return!!rc(this.Hf)};L.prototype.RestartRow=function(){sc(this.Hf)};L.prototype.Next=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return!!tc(c,a)};
L.prototype.IsAtBeginningOf=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return!!uc(c,a)};L.prototype.IsAtFinalElement=function(a,c){var d=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);return!!vc(d,a,c)};L.prototype.Cmp=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return wc(c,a)};L.prototype.SetBoundingBoxComponents=function(a,c){var d=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);xc(d,a,c)};
L.prototype.BoundingBox=function(a,c,d,e,g,h){var k=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);g&&"object"===typeof g&&(g=g.Hf);h&&"object"===typeof h&&(h=h.Hf);return void 0===h?!!yc(k,a,c,d,e,g):!!zc(k,a,c,d,e,g,h)};
L.prototype.BoundingBoxInternal=function(a,c,d,e,g){var h=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);g&&"object"===typeof g&&(g=g.Hf);return!!Ac(h,a,c,d,e,g)};L.prototype.Empty=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return!!Bc(c,a)};L.prototype.BlockType=function(){return Cc(this.Hf)};L.prototype.BlockPolygon=function(){return H(Dc(this.Hf),M)};
L.prototype.GetBinaryImage=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return H(Ec(c,a),N)};L.prototype.GetImage=function(a,c,d,e,g){var h=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);g&&"object"===typeof g&&(g=g.Hf);return H(Fc(h,a,c,d,e,g),N)};
L.prototype.Baseline=function(a,c,d,e,g){var h=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);g&&"object"===typeof g&&(g=g.Hf);return!!Gc(h,a,c,d,e,g)};L.prototype.RowAttributes=function(a,c,d){var e=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);return!!Hc(e,a,c,d)};
L.prototype.Orientation=function(a,c,d,e){var g=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);Ic(g,a,c,d,e)};L.prototype.ParagraphInfo=function(a,c,d,e){var g=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);Jc(g,a,c,d,e)};L.prototype.ParagraphIsLtr=function(){return!!Kc(this.Hf)};
L.prototype.GetUTF8Text=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return A(Lc(c,a))};L.prototype.SetLineSeparator=function(a){var c=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);Mc(c,a)};L.prototype.SetParagraphSeparator=function(a){var c=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);Nc(c,a)};L.prototype.Confidence=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return Oc(c,a)};
L.prototype.WordFontAttributes=function(a,c,d,e,g,h,k,m){var v=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);g&&"object"===typeof g&&(g=g.Hf);h&&"object"===typeof h&&(h=h.Hf);k&&"object"===typeof k&&(k=k.Hf);m&&"object"===typeof m&&(m=m.Hf);return A(Pc(v,a,c,d,e,g,h,k,m))};L.prototype.WordRecognitionLanguage=function(){return A(Qc(this.Hf))};L.prototype.WordDirection=function(){return Rc(this.Hf)};
L.prototype.WordIsFromDictionary=function(){return!!Sc(this.Hf)};L.prototype.WordIsNumeric=function(){return!!Tc(this.Hf)};L.prototype.HasBlamerInfo=function(){return!!Uc(this.Hf)};L.prototype.HasTruthString=function(){return!!Vc(this.Hf)};L.prototype.EquivalentToTruth=function(a){var c=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);return!!Wc(c,a)};L.prototype.WordTruthUTF8Text=function(){return A(Xc(this.Hf))};L.prototype.WordNormedUTF8Text=function(){return A(Yc(this.Hf))};
L.prototype.WordLattice=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return A(Zc(c,a))};L.prototype.SymbolIsSuperscript=function(){return!!$c(this.Hf)};L.prototype.SymbolIsSubscript=function(){return!!ad(this.Hf)};L.prototype.SymbolIsDropcap=function(){return!!bd(this.Hf)};L.prototype.__destroy__=function(){cd(this.Hf)};function Vh(){throw"cannot construct a TextlineOrder, no constructor in IDL";}Vh.prototype=Object.create(G.prototype);Vh.prototype.constructor=Vh;Vh.prototype.Mf=Vh;
Vh.Nf={};b.TextlineOrder=Vh;Vh.prototype.__destroy__=function(){dd(this.Hf)};function Wh(){throw"cannot construct a ETEXT_DESC, no constructor in IDL";}Wh.prototype=Object.create(G.prototype);Wh.prototype.constructor=Wh;Wh.prototype.Mf=Wh;Wh.Nf={};b.ETEXT_DESC=Wh;Wh.prototype.__destroy__=function(){ed(this.Hf)};function O(){throw"cannot construct a PageIterator, no constructor in IDL";}O.prototype=Object.create(G.prototype);O.prototype.constructor=O;O.prototype.Mf=O;O.Nf={};b.PageIterator=O;
O.prototype.Begin=function(){fd(this.Hf)};O.prototype.RestartParagraph=function(){gd(this.Hf)};O.prototype.IsWithinFirstTextlineOfParagraph=function(){return!!hd(this.Hf)};O.prototype.RestartRow=function(){jd(this.Hf)};O.prototype.Next=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return!!kd(c,a)};O.prototype.IsAtBeginningOf=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return!!ld(c,a)};
O.prototype.IsAtFinalElement=function(a,c){var d=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);return!!md(d,a,c)};O.prototype.Cmp=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return nd(c,a)};O.prototype.SetBoundingBoxComponents=function(a,c){var d=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);od(d,a,c)};
O.prototype.BoundingBox=function(a,c,d,e,g,h){var k=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);g&&"object"===typeof g&&(g=g.Hf);h&&"object"===typeof h&&(h=h.Hf);return void 0===h?!!pd(k,a,c,d,e,g):!!qd(k,a,c,d,e,g,h)};
O.prototype.BoundingBoxInternal=function(a,c,d,e,g){var h=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);g&&"object"===typeof g&&(g=g.Hf);return!!rd(h,a,c,d,e,g)};O.prototype.Empty=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return!!sd(c,a)};O.prototype.BlockType=function(){return td(this.Hf)};O.prototype.BlockPolygon=function(){return H(ud(this.Hf),M)};
O.prototype.GetBinaryImage=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return H(vd(c,a),N)};O.prototype.GetImage=function(a,c,d,e,g){var h=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);g&&"object"===typeof g&&(g=g.Hf);return H(wd(h,a,c,d,e,g),N)};
O.prototype.Baseline=function(a,c,d,e,g){var h=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);g&&"object"===typeof g&&(g=g.Hf);return!!xd(h,a,c,d,e,g)};O.prototype.Orientation=function(a,c,d,e){var g=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);yd(g,a,c,d,e)};
O.prototype.ParagraphInfo=function(a,c,d,e){var g=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);zd(g,a,c,d,e)};O.prototype.__destroy__=function(){Ad(this.Hf)};function Xh(){throw"cannot construct a WritingDirection, no constructor in IDL";}Xh.prototype=Object.create(G.prototype);Xh.prototype.constructor=Xh;Xh.prototype.Mf=Xh;Xh.Nf={};b.WritingDirection=Xh;Xh.prototype.__destroy__=function(){Bd(this.Hf)};
function Yh(a){a&&"object"===typeof a&&(a=a.Hf);this.Hf=Cd(a);Kh(Yh)[this.Hf]=this}Yh.prototype=Object.create(G.prototype);Yh.prototype.constructor=Yh;Yh.prototype.Mf=Yh;Yh.Nf={};b.WordChoiceIterator=Yh;Yh.prototype.Next=function(){return!!Dd(this.Hf)};Yh.prototype.GetUTF8Text=function(){return A(Ed(this.Hf))};Yh.prototype.Confidence=function(){return Fd(this.Hf)};Yh.prototype.__destroy__=function(){Gd(this.Hf)};function P(){throw"cannot construct a Box, no constructor in IDL";}P.prototype=Object.create(G.prototype);
P.prototype.constructor=P;P.prototype.Mf=P;P.Nf={};b.Box=P;P.prototype.get_x=P.prototype.Pg=function(){return Hd(this.Hf)};Object.defineProperty(P.prototype,"x",{get:P.prototype.Pg});P.prototype.get_y=P.prototype.Qg=function(){return Id(this.Hf)};Object.defineProperty(P.prototype,"y",{get:P.prototype.Qg});P.prototype.get_w=P.prototype.Og=function(){return Jd(this.Hf)};Object.defineProperty(P.prototype,"w",{get:P.prototype.Og});P.prototype.get_h=P.prototype.Ng=function(){return Kd(this.Hf)};
Object.defineProperty(P.prototype,"h",{get:P.prototype.Ng});P.prototype.get_refcount=P.prototype.bg=function(){return Ld(this.Hf)};Object.defineProperty(P.prototype,"refcount",{get:P.prototype.bg});P.prototype.__destroy__=function(){Md(this.Hf)};function Q(a,c,d){I();a=a&&"object"===typeof a?a.Hf:J(a);c=c&&"object"===typeof c?c.Hf:J(c);d&&"object"===typeof d&&(d=d.Hf);this.Hf=Nd(a,c,d);Kh(Q)[this.Hf]=this}Q.prototype=Object.create(G.prototype);Q.prototype.constructor=Q;Q.prototype.Mf=Q;Q.Nf={};
b.TessPDFRenderer=Q;Q.prototype.BeginDocument=function(a){var c=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);return!!Od(c,a)};Q.prototype.AddImage=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return!!Pd(c,a)};Q.prototype.EndDocument=function(){return!!Qd(this.Hf)};Q.prototype.happy=function(){return!!Rd(this.Hf)};Q.prototype.file_extension=function(){return A(Sd(this.Hf))};Q.prototype.title=Q.prototype.title=function(){return A(Td(this.Hf))};Q.prototype.imagenum=function(){return Ud(this.Hf)};
Q.prototype.__destroy__=function(){Vd(this.Hf)};function Zh(){throw"cannot construct a PixaPtr, no constructor in IDL";}Zh.prototype=Object.create(G.prototype);Zh.prototype.constructor=Zh;Zh.prototype.Mf=Zh;Zh.Nf={};b.PixaPtr=Zh;Zh.prototype.__destroy__=function(){Wd(this.Hf)};function $h(){throw"cannot construct a FloatPtr, no constructor in IDL";}$h.prototype=Object.create(G.prototype);$h.prototype.constructor=$h;$h.prototype.Mf=$h;$h.Nf={};b.FloatPtr=$h;$h.prototype.__destroy__=function(){Xd(this.Hf)};
function ai(a){a&&"object"===typeof a&&(a=a.Hf);this.Hf=Yd(a);Kh(ai)[this.Hf]=this}ai.prototype=Object.create(G.prototype);ai.prototype.constructor=ai;ai.prototype.Mf=ai;ai.Nf={};b.ChoiceIterator=ai;ai.prototype.Next=function(){return!!Zd(this.Hf)};ai.prototype.GetUTF8Text=function(){return A($d(this.Hf))};ai.prototype.Confidence=function(){return ae(this.Hf)};ai.prototype.__destroy__=function(){be(this.Hf)};function bi(){throw"cannot construct a PixPtr, no constructor in IDL";}bi.prototype=Object.create(G.prototype);
bi.prototype.constructor=bi;bi.prototype.Mf=bi;bi.Nf={};b.PixPtr=bi;bi.prototype.__destroy__=function(){ce(this.Hf)};function ci(){throw"cannot construct a UNICHARSET, no constructor in IDL";}ci.prototype=Object.create(G.prototype);ci.prototype.constructor=ci;ci.prototype.Mf=ci;ci.Nf={};b.UNICHARSET=ci;ci.prototype.get_script_from_script_id=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return A(de(c,a))};
ci.prototype.get_script_id_from_name=function(a){var c=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);return ee(c,a)};ci.prototype.get_script_table_size=function(){return fe(this.Hf)};ci.prototype.__destroy__=function(){ge(this.Hf)};function di(){throw"cannot construct a IntPtr, no constructor in IDL";}di.prototype=Object.create(G.prototype);di.prototype.constructor=di;di.prototype.Mf=di;di.Nf={};b.IntPtr=di;di.prototype.__destroy__=function(){he(this.Hf)};
function ei(){throw"cannot construct a Orientation, no constructor in IDL";}ei.prototype=Object.create(G.prototype);ei.prototype.constructor=ei;ei.prototype.Mf=ei;ei.Nf={};b.Orientation=ei;ei.prototype.__destroy__=function(){ie(this.Hf)};function R(){throw"cannot construct a OSBestResult, no constructor in IDL";}R.prototype=Object.create(G.prototype);R.prototype.constructor=R;R.prototype.Mf=R;R.Nf={};b.OSBestResult=R;R.prototype.get_orientation_id=R.prototype.Zh=function(){return je(this.Hf)};
Object.defineProperty(R.prototype,"orientation_id",{get:R.prototype.Zh});R.prototype.get_script_id=R.prototype.bi=function(){return ke(this.Hf)};Object.defineProperty(R.prototype,"script_id",{get:R.prototype.bi});R.prototype.get_sconfidence=R.prototype.ai=function(){return le(this.Hf)};Object.defineProperty(R.prototype,"sconfidence",{get:R.prototype.ai});R.prototype.get_oconfidence=R.prototype.Yh=function(){return me(this.Hf)};Object.defineProperty(R.prototype,"oconfidence",{get:R.prototype.Yh});
R.prototype.__destroy__=function(){ne(this.Hf)};function S(){throw"cannot construct a Boxa, no constructor in IDL";}S.prototype=Object.create(G.prototype);S.prototype.constructor=S;S.prototype.Mf=S;S.Nf={};b.Boxa=S;S.prototype.get_n=S.prototype.fg=function(){return oe(this.Hf)};Object.defineProperty(S.prototype,"n",{get:S.prototype.fg});S.prototype.get_nalloc=S.prototype.gg=function(){return pe(this.Hf)};Object.defineProperty(S.prototype,"nalloc",{get:S.prototype.gg});
S.prototype.get_refcount=S.prototype.bg=function(){return qe(this.Hf)};Object.defineProperty(S.prototype,"refcount",{get:S.prototype.bg});S.prototype.get_box=S.prototype.Rh=function(){return H(re(this.Hf),fi)};Object.defineProperty(S.prototype,"box",{get:S.prototype.Rh});S.prototype.__destroy__=function(){se(this.Hf)};function T(){throw"cannot construct a PixColormap, no constructor in IDL";}T.prototype=Object.create(G.prototype);T.prototype.constructor=T;T.prototype.Mf=T;T.Nf={};b.PixColormap=T;
T.prototype.get_array=T.prototype.Ph=function(){return te(this.Hf)};Object.defineProperty(T.prototype,"array",{get:T.prototype.Ph});T.prototype.get_depth=T.prototype.Wh=function(){return ue(this.Hf)};Object.defineProperty(T.prototype,"depth",{get:T.prototype.Wh});T.prototype.get_nalloc=T.prototype.gg=function(){return ve(this.Hf)};Object.defineProperty(T.prototype,"nalloc",{get:T.prototype.gg});T.prototype.get_n=T.prototype.fg=function(){return we(this.Hf)};Object.defineProperty(T.prototype,"n",{get:T.prototype.fg});
T.prototype.__destroy__=function(){xe(this.Hf)};function M(){throw"cannot construct a Pta, no constructor in IDL";}M.prototype=Object.create(G.prototype);M.prototype.constructor=M;M.prototype.Mf=M;M.Nf={};b.Pta=M;M.prototype.get_n=M.prototype.fg=function(){return ye(this.Hf)};Object.defineProperty(M.prototype,"n",{get:M.prototype.fg});M.prototype.get_nalloc=M.prototype.gg=function(){return ze(this.Hf)};Object.defineProperty(M.prototype,"nalloc",{get:M.prototype.gg});
M.prototype.get_refcount=M.prototype.bg=function(){return Ae(this.Hf)};Object.defineProperty(M.prototype,"refcount",{get:M.prototype.bg});M.prototype.get_x=M.prototype.Pg=function(){return H(Be(this.Hf),$h)};Object.defineProperty(M.prototype,"x",{get:M.prototype.Pg});M.prototype.get_y=M.prototype.Qg=function(){return H(Ce(this.Hf),$h)};Object.defineProperty(M.prototype,"y",{get:M.prototype.Qg});M.prototype.__destroy__=function(){De(this.Hf)};
function N(){throw"cannot construct a Pix, no constructor in IDL";}N.prototype=Object.create(G.prototype);N.prototype.constructor=N;N.prototype.Mf=N;N.Nf={};b.Pix=N;N.prototype.get_w=N.prototype.Og=function(){return Ee(this.Hf)};Object.defineProperty(N.prototype,"w",{get:N.prototype.Og});N.prototype.get_h=N.prototype.Ng=function(){return Fe(this.Hf)};Object.defineProperty(N.prototype,"h",{get:N.prototype.Ng});N.prototype.get_d=N.prototype.Uh=function(){return Ge(this.Hf)};
Object.defineProperty(N.prototype,"d",{get:N.prototype.Uh});N.prototype.get_spp=N.prototype.di=function(){return He(this.Hf)};Object.defineProperty(N.prototype,"spp",{get:N.prototype.di});N.prototype.get_wpl=N.prototype.gi=function(){return Ie(this.Hf)};Object.defineProperty(N.prototype,"wpl",{get:N.prototype.gi});N.prototype.get_refcount=N.prototype.bg=function(){return Je(this.Hf)};Object.defineProperty(N.prototype,"refcount",{get:N.prototype.bg});N.prototype.get_xres=N.prototype.hi=function(){return Ke(this.Hf)};
Object.defineProperty(N.prototype,"xres",{get:N.prototype.hi});N.prototype.get_yres=N.prototype.ii=function(){return Le(this.Hf)};Object.defineProperty(N.prototype,"yres",{get:N.prototype.ii});N.prototype.get_informat=N.prototype.Xh=function(){return Me(this.Hf)};Object.defineProperty(N.prototype,"informat",{get:N.prototype.Xh});N.prototype.get_special=N.prototype.ci=function(){return Ne(this.Hf)};Object.defineProperty(N.prototype,"special",{get:N.prototype.ci});
N.prototype.get_text=N.prototype.ei=function(){return A(Oe(this.Hf))};Object.defineProperty(N.prototype,"text",{get:N.prototype.ei});N.prototype.get_colormap=N.prototype.Th=function(){return H(Pe(this.Hf),T)};Object.defineProperty(N.prototype,"colormap",{get:N.prototype.Th});N.prototype.get_data=N.prototype.Vh=function(){return Qe(this.Hf)};Object.defineProperty(N.prototype,"data",{get:N.prototype.Vh});N.prototype.__destroy__=function(){Re(this.Hf)};
function gi(){throw"cannot construct a DoublePtr, no constructor in IDL";}gi.prototype=Object.create(G.prototype);gi.prototype.constructor=gi;gi.prototype.Mf=gi;gi.Nf={};b.DoublePtr=gi;gi.prototype.__destroy__=function(){Se(this.Hf)};function hi(){throw"cannot construct a Dawg, no constructor in IDL";}hi.prototype=Object.create(G.prototype);hi.prototype.constructor=hi;hi.prototype.Mf=hi;hi.Nf={};b.Dawg=hi;hi.prototype.__destroy__=function(){Te(this.Hf)};
function fi(){throw"cannot construct a BoxPtr, no constructor in IDL";}fi.prototype=Object.create(G.prototype);fi.prototype.constructor=fi;fi.prototype.Mf=fi;fi.Nf={};b.BoxPtr=fi;fi.prototype.__destroy__=function(){Ue(this.Hf)};function V(){this.Hf=Ve();Kh(V)[this.Hf]=this}V.prototype=Object.create(G.prototype);V.prototype.constructor=V;V.prototype.Mf=V;V.Nf={};b.TessBaseAPI=V;V.prototype.Version=function(){return A(We(this.Hf))};
V.prototype.SetInputName=function(a){var c=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);Xe(c,a)};V.prototype.GetInputName=function(){return A(Ye(this.Hf))};V.prototype.SetInputImage=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);Ze(c,a)};V.prototype.GetInputImage=function(){return H($e(this.Hf),N)};V.prototype.GetSourceYResolution=function(){return af(this.Hf)};V.prototype.GetDatapath=function(){return A(bf(this.Hf))};
V.prototype.SetOutputName=function(a){var c=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);cf(c,a)};V.prototype.SetVariable=V.prototype.SetVariable=function(a,c){var d=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);c=c&&"object"===typeof c?c.Hf:J(c);return!!df(d,a,c)};V.prototype.SetDebugVariable=function(a,c){var d=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);c=c&&"object"===typeof c?c.Hf:J(c);return!!ef(d,a,c)};
V.prototype.GetIntVariable=function(a,c){var d=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);c&&"object"===typeof c&&(c=c.Hf);return!!ff(d,a,c)};V.prototype.GetBoolVariable=function(a,c){var d=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);c&&"object"===typeof c&&(c=c.Hf);return!!gf(d,a,c)};V.prototype.GetDoubleVariable=function(a,c){var d=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);c&&"object"===typeof c&&(c=c.Hf);return!!hf(d,a,c)};
V.prototype.GetStringVariable=function(a){var c=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);return A(jf(c,a))};V.prototype.Init=function(a,c,d,e){void 0===d&&void 0!==e&&(d=3);var g=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);c=c&&"object"===typeof c?c.Hf:J(c);e=e&&"object"===typeof e?e.Hf:J(e);d&&"object"===typeof d&&(d=d.Hf);return void 0===d&&void 0!==e?of(g,a,c,3,e):void 0===d?mf(g,a,c):void 0===e?nf(g,a,c,d):of(g,a,c,d,e)};V.prototype.GetInitLanguagesAsString=function(){return A(pf(this.Hf))};
V.prototype.InitForAnalysePage=function(){qf(this.Hf)};V.prototype.SaveParameters=function(){kf(this.Hf)};V.prototype.RestoreParameters=function(){lf(this.Hf)};V.prototype.ReadConfigFile=function(a){var c=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);rf(c,a)};V.prototype.ReadDebugConfigFile=function(a){var c=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);sf(c,a)};V.prototype.SetPageSegMode=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);tf(c,a)};V.prototype.GetPageSegMode=function(){return uf(this.Hf)};
V.prototype.TesseractRect=function(a,c,d,e,g,h,k){var m=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);g&&"object"===typeof g&&(g=g.Hf);h&&"object"===typeof h&&(h=h.Hf);k&&"object"===typeof k&&(k=k.Hf);return A(vf(m,a,c,d,e,g,h,k))};V.prototype.ClearAdaptiveClassifier=function(){wf(this.Hf)};
V.prototype.SetImage=function(a,c,d,e,g,h=1,k=0){var m=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);g&&"object"===typeof g&&(g=g.Hf);void 0===c||null===c?xf(m,a,h,k):yf(m,a,c,d,e,g,h,k)};V.prototype.SetImageFile=function(a=1,c=0){return zf(this.Hf,a,c)};V.prototype.SetSourceResolution=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);Af(c,a)};
V.prototype.SetRectangle=function(a,c,d,e){var g=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);Bf(g,a,c,d,e)};V.prototype.GetThresholdedImage=function(){return H(Cf(this.Hf),N)};V.prototype.WriteImage=function(a){Df(this.Hf,a)};V.prototype.GetRegions=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return H(Gf(c,a),S)};
V.prototype.GetTextlines=function(a,c,d,e,g){var h=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);g&&"object"===typeof g&&(g=g.Hf);return void 0===d?H(Hf(h,a,c),S):void 0===e?H(_emscripten_bind_TessBaseAPI_GetTextlines_3(h,a,c,d),S):void 0===g?H(_emscripten_bind_TessBaseAPI_GetTextlines_4(h,a,c,d,e),S):H(If(h,a,c,d,e,g),S)};
V.prototype.GetStrips=function(a,c){var d=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);return H(Jf(d,a,c),S)};V.prototype.GetWords=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return H(Kf(c,a),S)};V.prototype.GetConnectedComponents=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return H(Lf(c,a),S)};
V.prototype.GetComponentImages=function(a,c,d,e,g,h,k){var m=this.Hf;a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);g&&"object"===typeof g&&(g=g.Hf);h&&"object"===typeof h&&(h=h.Hf);k&&"object"===typeof k&&(k=k.Hf);return void 0===g?H(Mf(m,a,c,d,e),S):void 0===h?H(_emscripten_bind_TessBaseAPI_GetComponentImages_5(m,a,c,d,e,g),S):void 0===k?H(_emscripten_bind_TessBaseAPI_GetComponentImages_6(m,a,c,d,e,g,h),S):H(Nf(m,
a,c,d,e,g,h,k),S)};V.prototype.GetThresholdedImageScaleFactor=function(){return Of(this.Hf)};V.prototype.AnalyseLayout=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return void 0===a?H(Pf(c),O):H(Qf(c,a),O)};V.prototype.Recognize=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return Rf(c,a)};V.prototype.FindLines=function(){return Ef(this.Hf)};V.prototype.GetGradient=function(){return Ff(this.Hf)};
V.prototype.ProcessPages=function(a,c,d,e){var g=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);c=c&&"object"===typeof c?c.Hf:J(c);d&&"object"===typeof d&&(d=d.Hf);e&&"object"===typeof e&&(e=e.Hf);return!!Sf(g,a,c,d,e)};
V.prototype.ProcessPage=function(a,c,d,e,g,h){var k=this.Hf;I();a&&"object"===typeof a&&(a=a.Hf);c&&"object"===typeof c&&(c=c.Hf);d=d&&"object"===typeof d?d.Hf:J(d);e=e&&"object"===typeof e?e.Hf:J(e);g&&"object"===typeof g&&(g=g.Hf);h&&"object"===typeof h&&(h=h.Hf);return!!Tf(k,a,c,d,e,g,h)};V.prototype.GetIterator=function(){return H(Uf(this.Hf),L)};V.prototype.GetUTF8Text=function(){var a=Vf(this.Hf),c=A(a);Fh(a);return c};
V.prototype.GetHOCRText=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);a=Wf(c,a);c=A(a);Fh(a);return c};V.prototype.GetTSVText=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);a=Yf(c,a);c=A(a);Fh(a);return c};V.prototype.GetJSONText=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);a=Xf(c,a);c=A(a);Fh(a);return c};V.prototype.GetBoxText=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);a=Zf(c,a);c=A(a);Fh(a);return c};
V.prototype.GetUNLVText=function(){var a=$f(this.Hf),c=A(a);Fh(a);return c};V.prototype.GetOsdText=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);a=ag(c,a);c=A(a);Fh(a);return c};V.prototype.MeanTextConf=function(){return bg(this.Hf)};V.prototype.AllWordConfidences=function(){return H(cg(this.Hf),di)};V.prototype.AdaptToWordStr=function(a,c){var d=this.Hf;I();a&&"object"===typeof a&&(a=a.Hf);c=c&&"object"===typeof c?c.Hf:J(c);return!!_emscripten_bind_TessBaseAPI_AdaptToWordStr_2(d,a,c)};
V.prototype.Clear=function(){dg(this.Hf)};V.prototype.End=function(){eg(this.Hf)};V.prototype.ClearPersistentCache=function(){fg(this.Hf)};V.prototype.IsValidWord=function(a){var c=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);return gg(c,a)};V.prototype.IsValidCharacter=function(a){var c=this.Hf;I();a=a&&"object"===typeof a?a.Hf:J(a);return!!hg(c,a)};V.prototype.DetectOS=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return!!ig(c,a)};
V.prototype.GetUnichar=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return A(jg(c,a))};V.prototype.GetDawg=function(a){var c=this.Hf;a&&"object"===typeof a&&(a=a.Hf);return H(kg(c,a),hi)};V.prototype.NumDawgs=function(){return lg(this.Hf)};V.prototype.oem=function(){return mg(this.Hf)};V.prototype.__destroy__=function(){ng(this.Hf)};function Y(){this.Hf=og();Kh(Y)[this.Hf]=this}Y.prototype=Object.create(G.prototype);Y.prototype.constructor=Y;Y.prototype.Mf=Y;Y.Nf={};b.OSResults=Y;
Y.prototype.print_scores=function(){pg(this.Hf)};Y.prototype.get_best_result=Y.prototype.Qh=function(){return H(qg(this.Hf),R)};Object.defineProperty(Y.prototype,"best_result",{get:Y.prototype.Qh});Y.prototype.get_unicharset=Y.prototype.fi=function(){return H(rg(this.Hf),ci)};Object.defineProperty(Y.prototype,"unicharset",{get:Y.prototype.fi});Y.prototype.__destroy__=function(){sg(this.Hf)};function Z(){throw"cannot construct a Pixa, no constructor in IDL";}Z.prototype=Object.create(G.prototype);
Z.prototype.constructor=Z;Z.prototype.Mf=Z;Z.Nf={};b.Pixa=Z;Z.prototype.get_n=Z.prototype.fg=function(){return tg(this.Hf)};Object.defineProperty(Z.prototype,"n",{get:Z.prototype.fg});Z.prototype.get_nalloc=Z.prototype.gg=function(){return ug(this.Hf)};Object.defineProperty(Z.prototype,"nalloc",{get:Z.prototype.gg});Z.prototype.get_refcount=Z.prototype.bg=function(){return vg(this.Hf)};Object.defineProperty(Z.prototype,"refcount",{get:Z.prototype.bg});
Z.prototype.get_pix=Z.prototype.$h=function(){return H(wg(this.Hf),bi)};Object.defineProperty(Z.prototype,"pix",{get:Z.prototype.$h});Z.prototype.get_boxa=Z.prototype.Sh=function(){return H(xg(this.Hf),S)};Object.defineProperty(Z.prototype,"boxa",{get:Z.prototype.Sh});Z.prototype.__destroy__=function(){yg(this.Hf)};
(function(){function a(){b.RIL_BLOCK=zg();b.RIL_PARA=Ag();b.RIL_TEXTLINE=Bg();b.RIL_WORD=Cg();b.RIL_SYMBOL=Dg();b.OEM_TESSERACT_ONLY=Eg();b.OEM_LSTM_ONLY=Fg();b.OEM_TESSERACT_LSTM_COMBINED=Gg();b.OEM_DEFAULT=Hg();b.OEM_COUNT=Ig();b.WRITING_DIRECTION_LEFT_TO_RIGHT=Jg();b.WRITING_DIRECTION_RIGHT_TO_LEFT=Kg();b.WRITING_DIRECTION_TOP_TO_BOTTOM=Lg();b.PT_UNKNOWN=Mg();b.PT_FLOWING_TEXT=Ng();b.PT_HEADING_TEXT=Og();b.PT_PULLOUT_TEXT=Pg();b.PT_EQUATION=Qg();b.PT_INLINE_EQUATION=Rg();b.PT_TABLE=Sg();b.PT_VERTICAL_TEXT=
Tg();b.PT_CAPTION_TEXT=Ug();b.PT_FLOWING_IMAGE=Vg();b.PT_HEADING_IMAGE=Wg();b.PT_PULLOUT_IMAGE=Xg();b.PT_HORZ_LINE=Yg();b.PT_VERT_LINE=Zg();b.PT_NOISE=$g();b.PT_COUNT=ah();b.DIR_NEUTRAL=bh();b.DIR_LEFT_TO_RIGHT=ch();b.DIR_RIGHT_TO_LEFT=dh();b.DIR_MIX=eh();b.JUSTIFICATION_UNKNOWN=fh();b.JUSTIFICATION_LEFT=gh();b.JUSTIFICATION_CENTER=hh();b.JUSTIFICATION_RIGHT=ih();b.TEXTLINE_ORDER_LEFT_TO_RIGHT=jh();b.TEXTLINE_ORDER_RIGHT_TO_LEFT=kh();b.TEXTLINE_ORDER_TOP_TO_BOTTOM=lh();b.ORIENTATION_PAGE_UP=mh();
b.ORIENTATION_PAGE_RIGHT=nh();b.ORIENTATION_PAGE_DOWN=oh();b.ORIENTATION_PAGE_LEFT=ph();b.PSM_OSD_ONLY=qh();b.PSM_AUTO_OSD=rh();b.PSM_AUTO_ONLY=sh();b.PSM_AUTO=th();b.PSM_SINGLE_COLUMN=uh();b.PSM_SINGLE_BLOCK_VERT_TEXT=vh();b.PSM_SINGLE_BLOCK=wh();b.PSM_SINGLE_LINE=xh();b.PSM_SINGLE_WORD=yh();b.PSM_CIRCLE_WORD=zh();b.PSM_SINGLE_CHAR=Ah();b.PSM_SPARSE_TEXT=Bh();b.PSM_SPARSE_TEXT_OSD=Ch();b.PSM_RAW_LINE=Dh();b.PSM_COUNT=Eh()}Ba?a():za.unshift(a)})();
Rh.prototype.getValue=function(a){return!!Wa(this.Hf+(a||0),"i8")};di.prototype.getValue=function(a){return Wa(this.Hf+4*(a||0),"i32")};$h.prototype.getValue=function(a){return Wa(this.Hf+4*(a||0),"float")};gi.prototype.getValue=function(a){return Wa(this.Hf+8*(a||0),"double")};fi.prototype.get=Zh.prototype.get=bi.prototype.get=function(a){return Wa(this.Hf+4*(a||0),"*")};function ii(){this.ng={}}ii.prototype.wrap=function(a,c){var d=Eb(4);Xa(d,0,"i32");return this.ng[a]=H(d,c)};
ii.prototype.bool=function(a){return this.wrap(a,Rh)};ii.prototype.i32=function(a){return this.wrap(a,di)};ii.prototype.f32=function(a){return this.wrap(a,$h)};ii.prototype.f64=function(a){return this.ng[a]=H(Eb(8),gi)};ii.prototype.peek=function(){var a={},c;for(c in this.ng)a[c]=this.ng[c].getValue();return a};ii.prototype.get=function(){var a={},c;for(c in this.ng)a[c]=this.ng[c].getValue(),Fh(this.ng[c].Hf);return a};
L.prototype.getBoundingBox=function(a){var c=new ii;this.BoundingBox(a,c.i32("x0"),c.i32("y0"),c.i32("x1"),c.i32("y1"));return c.get()};L.prototype.getBaseline=function(a){var c=new ii;a=!!this.Baseline(a,c.i32("x0"),c.i32("y0"),c.i32("x1"),c.i32("y1"));c=c.get();c.has_baseline=a;return c};L.prototype.getRowAttributes=function(){var a=new ii;this.RowAttributes(a.f32("row_height"),a.f32("descenders"),a.f32("ascenders"));return a.get()};
L.prototype.getWordFontAttributes=function(){var a=new ii,c=this.WordFontAttributes(a.bool("is_bold"),a.bool("is_italic"),a.bool("is_underlined"),a.bool("is_monospace"),a.bool("is_serif"),a.bool("is_smallcaps"),a.i32("pointsize"),a.i32("font_id"));a=a.get();a.font_name=c;return a};b.pointerHelper=ii;


  return TesseractCore.ready
}

);
})();
if (typeof exports === 'object' && typeof module === 'object')
  module.exports = TesseractCore;
else if (typeof define === 'function' && define['amd'])
  define([], function() { return TesseractCore; });
else if (typeof exports === 'object')
  exports["TesseractCore"] = TesseractCore;
