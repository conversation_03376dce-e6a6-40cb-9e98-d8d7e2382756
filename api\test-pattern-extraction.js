// Test the pattern extraction logic with the actual extracted text

const sampleText = `Рантят
alcpate Palt
INCOME TAX DEPARTMENT
GOVT. OF INDIA
1 1
Ferraft Heal chis
B
Permanent Account Number Card
**********
7TH / Name
GANESH DULBAJI KHUPASE
fual ahl 714 / Father's Name
DULBAJI KHUPASE
********
$1 and /
Date of Birth
Anipase
15/08/2002
Real / Signature`.toUpperCase();

function extractFromPlainText(allText, fields, isGSTDocument) {
  console.log('Extracting from plain text...');
  
  if (isGSTDocument) {
    // GST document pattern extraction
    const gstinMatch = allText.match(/GSTIN[:\s]*([A-Z0-9]{15})/i);
    if (gstinMatch) fields.GSTIN = gstinMatch[1];
    
    const legalNameMatch = allText.match(/LEGAL NAME[:\s]*([A-Z\s]+)/i);
    if (legalNameMatch) fields.LEGAL_NAME = legalNameMatch[1].trim();
    
  } else {
    // Personal document pattern extraction
    
    // Extract Aadhaar number patterns
    const aadhaarMatch = allText.match(/(\d{4}[\s-]?\d{4}[\s-]?\d{4})/);
    if (aadhaarMatch) {
      fields.DOCUMENT_NUMBER = aadhaarMatch[1].replace(/[\s-]/g, ' ').replace(/\s+/g, ' ').trim();
    }
    
    // Extract PAN number patterns
    const panMatch = allText.match(/([A-Z]{5}\d{4}[A-Z])/);
    if (panMatch) {
      fields.DOCUMENT_NUMBER = panMatch[1];
    }
    
    // Extract passport number patterns
    const passportMatch = allText.match(/PASSPORT\s*NO[:\s]*([A-Z]\d{7})/i);
    if (passportMatch) {
      fields.DOCUMENT_NUMBER = passportMatch[1];
    }
    
    // Extract date patterns (DD/MM/YYYY or DD-MM-YYYY)
    const datePatterns = [
      /DATE OF BIRTH[:\s]*\n?(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/i,
      /DOB[:\s]*\n?(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/i,
      /(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{4})/
    ];
    
    for (const pattern of datePatterns) {
      const dateMatch = allText.match(pattern);
      if (dateMatch) {
        fields.DATE_OF_BIRTH = dateMatch[1];
        break;
      }
    }
    
    // Extract gender
    const genderMatch = allText.match(/\b(MALE|FEMALE|M|F)\b/i);
    if (genderMatch) {
      const gender = genderMatch[1].toUpperCase();
      fields.GENDER = gender === 'M' ? 'MALE' : gender === 'F' ? 'FEMALE' : gender;
    }
    
    // Extract name patterns - look for common name indicators
    const namePatterns = [
      /NAME[:\s]*\n([A-Z\s]+?)(?:\n|FATHER|DOB|GENDER|ADDRESS|$)/i, // Name followed by newline, stop at next field
      /NAME[:\s]*([A-Z\s]+?)(?:\n|FATHER|DOB|GENDER|ADDRESS|$)/i,
      /([A-Z]{2,}\s+[A-Z]{2,}\s+[A-Z]{2,})(?:\s|$)/i, // Three uppercase words followed by space or end
    ];

    for (const pattern of namePatterns) {
      const nameMatch = allText.match(pattern);
      if (nameMatch && !fields.FIRST_NAME) {
        let fullName = nameMatch[1].trim();

        // Clean up the name - remove numbers and special characters
        fullName = fullName.replace(/\d+/g, '').replace(/[\/\$]/g, '').trim();

        // Filter out common non-name words and stop at father's name
        if (!fullName.match(/INCOME|TAX|DEPARTMENT|GOVT|INDIA|PERMANENT|ACCOUNT|NUMBER|CARD|SIGNATURE|FATHER/)) {
          // Split by common separators and take only the first meaningful part
          const cleanName = fullName.split(/FATHER|DOB|GENDER|ADDRESS/)[0].trim();
          const parts = cleanName.split(/\s+/).filter(part =>
            part.length > 1 &&
            !part.match(/\d/) &&
            !part.match(/[\/\$]/) &&
            part !== 'AHL' // Filter out specific OCR artifacts
          );

          if (parts.length === 1) {
            fields.FIRST_NAME = parts[0];
          } else if (parts.length === 2) {
            fields.FIRST_NAME = parts[0];
            fields.LAST_NAME = parts[1];
          } else if (parts.length >= 3) {
            fields.FIRST_NAME = parts[0];
            fields.MIDDLE_NAME = parts.slice(1, -1).join(" ");
            fields.LAST_NAME = parts[parts.length - 1];
          }
          break;
        }
      }
    }
    
    // Extract address - look for address patterns
    const addressPatterns = [
      /ADDRESS[:\s]*([A-Z0-9\s,.-]+?)(?:\n\n|PIN|PHONE|$)/i,
      /S\/O[:\s]*[A-Z\s]+\n([A-Z0-9\s,.-]+?)(?:\n\n|PIN|$)/i, // Son/Daughter of pattern
    ];
    
    for (const pattern of addressPatterns) {
      const addressMatch = allText.match(pattern);
      if (addressMatch && !fields.ADDRESS) {
        fields.ADDRESS = addressMatch[1].trim();
        break;
      }
    }
  }
  
  console.log('Pattern extraction completed:', fields);
}

// Test the extraction
const fields = {
  FIRST_NAME: "",
  MIDDLE_NAME: "",
  LAST_NAME: "",
  DOCUMENT_NUMBER: "",
  DATE_OF_BIRTH: "",
  ADDRESS: "",
  GENDER: "",
  DOCUMENT_TYPE: ""
};

console.log('Testing pattern extraction with sample text...');
console.log('Sample text:', sampleText);
console.log('\n--- Extraction Results ---');

extractFromPlainText(sampleText, fields, false);

console.log('\nFinal extracted fields:');
console.log(JSON.stringify(fields, null, 2));
