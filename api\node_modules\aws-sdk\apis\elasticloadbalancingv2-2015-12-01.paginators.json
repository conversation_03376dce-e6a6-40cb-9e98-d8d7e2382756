{"pagination": {"DescribeListeners": {"input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "result_key": "Listeners"}, "DescribeLoadBalancers": {"input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "result_key": "LoadBalancers"}, "DescribeTargetGroups": {"input_token": "<PERSON><PERSON>", "output_token": "NextMarker", "result_key": "TargetGroups"}, "DescribeTrustStoreAssociations": {"input_token": "<PERSON><PERSON>", "limit_key": "PageSize", "output_token": "NextMarker"}, "DescribeTrustStoreRevocations": {"input_token": "<PERSON><PERSON>", "limit_key": "PageSize", "output_token": "NextMarker"}, "DescribeTrustStores": {"input_token": "<PERSON><PERSON>", "limit_key": "PageSize", "output_token": "NextMarker"}}}