const http = require('http');
const url = require('url');

const PORT = 3005;

// Mock data with proper document types
const mockResults = {
  success: true,
  total_files: 4,
  processed_files: 4,
  personal_documents: {
    count: 4,
    grouped_by_person: [
      {
        group_id: 1,
        person_name: "<PERSON>",
        total_documents: 2,
        documents: [
          {
            filename: "john-aadhaar.jpg",
            FIRST_NAME: "<PERSON>",
            MIDDLE_NAME: "<PERSON>",
            LAST_NAME: "<PERSON>e",
            DOCUMENT_NUMBER: "1234 5678 9012",
            DATE_OF_BIRTH: "01/01/1990",
            ADDRESS: "123 Main Street, City, State - 400001",
            GENDER: "MALE",
            DOCUMENT_TYPE: "AADHA<PERSON>"
          },
          {
            filename: "john-pan.jpg",
            FIRST_NAME: "<PERSON>",
            MIDDLE_NAME: "<PERSON>",
            LAST_NAME: "<PERSON><PERSON>",
            DOCUMENT_NUMBER: "**********",
            DATE_OF_BIRTH: "01/01/1990",
            ADDRESS: "123 Main Street, City, State - 400001",
            GENDER: "MALE",
            DOCUMENT_TYPE: "PAN"
          }
        ]
      },
      {
        group_id: 2,
        person_name: "Priya <PERSON>",
        total_documents: 2,
        documents: [
          {
            filename: "priya-aadhaar.jpg",
            FIRST_NAME: "Priya",
            MIDDLE_NAME: "",
            LAST_NAME: "Sharma",
            DOCUMENT_NUMBER: "**************",
            DATE_OF_BIRTH: "15/05/1985",
            ADDRESS: "456 Park Avenue, Mumbai, Maharashtra - 400002",
            GENDER: "FEMALE",
            DOCUMENT_TYPE: "AADHAAR"
          },
          {
            filename: "priya-passport.jpg",
            FIRST_NAME: "Priya",
            MIDDLE_NAME: "",
            LAST_NAME: "Sharma",
            DOCUMENT_NUMBER: "********",
            DATE_OF_BIRTH: "15/05/1985",
            ADDRESS: "456 Park Avenue, Mumbai, Maharashtra - 400002",
            GENDER: "FEMALE",
            DOCUMENT_TYPE: "PASSPORT"
          }
        ]
      }
    ]
  }
};

const server = http.createServer((req, res) => {
  // Enable CORS
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type');
  
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }

  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;

  console.log(`${req.method} ${path}`);

  res.setHeader('Content-Type', 'application/json');

  if (path === '/health') {
    res.writeHead(200);
    res.end(JSON.stringify({ status: 'OK', message: 'Document Extraction API is running' }));
  } else if (path === '/upload' && req.method === 'POST') {
    res.writeHead(200);
    res.end(JSON.stringify(mockResults));
  } else if (path === '/api/grouped-by-person') {
    res.writeHead(200);
    res.end(JSON.stringify(mockResults));
  } else {
    res.writeHead(404);
    res.end(JSON.stringify({ error: 'Not found' }));
  }
});

server.listen(PORT, () => {
  console.log(`Document Extraction API running on http://localhost:${PORT}`);
  console.log('Available endpoints:');
  console.log('  POST /upload - Main upload endpoint');
  console.log('  GET /api/grouped-by-person - Get grouped results');
  console.log('  GET /health - Health check');
});
