/** @license zlib.js 2012 - imaya [ https://github.com/imaya/zlib.js ] The MIT License */(function() {'use strict';function m(d){throw d;}var w=void 0,z=!0,aa=this;function A(d,a){var c=d.split("."),e=aa;!(c[0]in e)&&e.execScript&&e.execScript("var "+c[0]);for(var b;c.length&&(b=c.shift());)!c.length&&a!==w?e[b]=a:e=e[b]?e[b]:e[b]={}};var G="undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint16Array&&"undefined"!==typeof Uint32Array&&"undefined"!==typeof DataView;function I(d,a){this.index="number"===typeof a?a:0;this.i=0;this.buffer=d instanceof(G?Uint8Array:Array)?d:new (G?Uint8Array:Array)(32768);2*this.buffer.length<=this.index&&m(Error("invalid index"));this.buffer.length<=this.index&&this.f()}I.prototype.f=function(){var d=this.buffer,a,c=d.length,e=new (G?Uint8Array:Array)(c<<1);if(G)e.set(d);else for(a=0;a<c;++a)e[a]=d[a];return this.buffer=e};
I.prototype.d=function(d,a,c){var e=this.buffer,b=this.index,f=this.i,g=e[b],h;c&&1<a&&(d=8<a?(Q[d&255]<<24|Q[d>>>8&255]<<16|Q[d>>>16&255]<<8|Q[d>>>24&255])>>32-a:Q[d]>>8-a);if(8>a+f)g=g<<a|d,f+=a;else for(h=0;h<a;++h)g=g<<1|d>>a-h-1&1,8===++f&&(f=0,e[b++]=Q[g],g=0,b===e.length&&(e=this.f()));e[b]=g;this.buffer=e;this.i=f;this.index=b};I.prototype.finish=function(){var d=this.buffer,a=this.index,c;0<this.i&&(d[a]<<=8-this.i,d[a]=Q[d[a]],a++);G?c=d.subarray(0,a):(d.length=a,c=d);return c};
var ba=new (G?Uint8Array:Array)(256),ca;for(ca=0;256>ca;++ca){for(var R=ca,ha=R,ia=7,R=R>>>1;R;R>>>=1)ha<<=1,ha|=R&1,--ia;ba[ca]=(ha<<ia&255)>>>0}var Q=ba;function ja(d){this.buffer=new (G?Uint16Array:Array)(2*d);this.length=0}ja.prototype.getParent=function(d){return 2*((d-2)/4|0)};ja.prototype.push=function(d,a){var c,e,b=this.buffer,f;c=this.length;b[this.length++]=a;for(b[this.length++]=d;0<c;)if(e=this.getParent(c),b[c]>b[e])f=b[c],b[c]=b[e],b[e]=f,f=b[c+1],b[c+1]=b[e+1],b[e+1]=f,c=e;else break;return this.length};
ja.prototype.pop=function(){var d,a,c=this.buffer,e,b,f;a=c[0];d=c[1];this.length-=2;c[0]=c[this.length];c[1]=c[this.length+1];for(f=0;;){b=2*f+2;if(b>=this.length)break;b+2<this.length&&c[b+2]>c[b]&&(b+=2);if(c[b]>c[f])e=c[f],c[f]=c[b],c[b]=e,e=c[f+1],c[f+1]=c[b+1],c[b+1]=e;else break;f=b}return{index:d,value:a,length:this.length}};function S(d){var a=d.length,c=0,e=Number.POSITIVE_INFINITY,b,f,g,h,k,p,q,r,n,l;for(r=0;r<a;++r)d[r]>c&&(c=d[r]),d[r]<e&&(e=d[r]);b=1<<c;f=new (G?Uint32Array:Array)(b);g=1;h=0;for(k=2;g<=c;){for(r=0;r<a;++r)if(d[r]===g){p=0;q=h;for(n=0;n<g;++n)p=p<<1|q&1,q>>=1;l=g<<16|r;for(n=p;n<b;n+=k)f[n]=l;++h}++g;h<<=1;k<<=1}return[f,c,e]};function ka(d,a){this.h=na;this.w=0;this.input=G&&d instanceof Array?new Uint8Array(d):d;this.b=0;a&&(a.lazy&&(this.w=a.lazy),"number"===typeof a.compressionType&&(this.h=a.compressionType),a.outputBuffer&&(this.a=G&&a.outputBuffer instanceof Array?new Uint8Array(a.outputBuffer):a.outputBuffer),"number"===typeof a.outputIndex&&(this.b=a.outputIndex));this.a||(this.a=new (G?Uint8Array:Array)(32768))}var na=2,oa={NONE:0,r:1,k:na,N:3},pa=[],T;
for(T=0;288>T;T++)switch(z){case 143>=T:pa.push([T+48,8]);break;case 255>=T:pa.push([T-144+400,9]);break;case 279>=T:pa.push([T-256+0,7]);break;case 287>=T:pa.push([T-280+192,8]);break;default:m("invalid literal: "+T)}
ka.prototype.j=function(){var d,a,c,e,b=this.input;switch(this.h){case 0:c=0;for(e=b.length;c<e;){a=G?b.subarray(c,c+65535):b.slice(c,c+65535);c+=a.length;var f=a,g=c===e,h=w,k=w,p=w,q=w,r=w,n=this.a,l=this.b;if(G){for(n=new Uint8Array(this.a.buffer);n.length<=l+f.length+5;)n=new Uint8Array(n.length<<1);n.set(this.a)}h=g?1:0;n[l++]=h|0;k=f.length;p=~k+65536&65535;n[l++]=k&255;n[l++]=k>>>8&255;n[l++]=p&255;n[l++]=p>>>8&255;if(G)n.set(f,l),l+=f.length,n=n.subarray(0,l);else{q=0;for(r=f.length;q<r;++q)n[l++]=
f[q];n.length=l}this.b=l;this.a=n}break;case 1:var s=new I(G?new Uint8Array(this.a.buffer):this.a,this.b);s.d(1,1,z);s.d(1,2,z);var t=qa(this,b),x,E,B;x=0;for(E=t.length;x<E;x++)if(B=t[x],I.prototype.d.apply(s,pa[B]),256<B)s.d(t[++x],t[++x],z),s.d(t[++x],5),s.d(t[++x],t[++x],z);else if(256===B)break;this.a=s.finish();this.b=this.a.length;break;case na:var C=new I(G?new Uint8Array(this.a.buffer):this.a,this.b),L,v,M,Y,Z,gb=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],da,Fa,ea,Ga,la,sa=Array(19),
Ha,$,ma,D,Ia;L=na;C.d(1,1,z);C.d(L,2,z);v=qa(this,b);da=ra(this.L,15);Fa=ta(da);ea=ra(this.K,7);Ga=ta(ea);for(M=286;257<M&&0===da[M-1];M--);for(Y=30;1<Y&&0===ea[Y-1];Y--);var Ja=M,Ka=Y,K=new (G?Uint32Array:Array)(Ja+Ka),u,N,y,fa,J=new (G?Uint32Array:Array)(316),H,F,O=new (G?Uint8Array:Array)(19);for(u=N=0;u<Ja;u++)K[N++]=da[u];for(u=0;u<Ka;u++)K[N++]=ea[u];if(!G){u=0;for(fa=O.length;u<fa;++u)O[u]=0}u=H=0;for(fa=K.length;u<fa;u+=N){for(N=1;u+N<fa&&K[u+N]===K[u];++N);y=N;if(0===K[u])if(3>y)for(;0<y--;)J[H++]=
0,O[0]++;else for(;0<y;)F=138>y?y:138,F>y-3&&F<y&&(F=y-3),10>=F?(J[H++]=17,J[H++]=F-3,O[17]++):(J[H++]=18,J[H++]=F-11,O[18]++),y-=F;else if(J[H++]=K[u],O[K[u]]++,y--,3>y)for(;0<y--;)J[H++]=K[u],O[K[u]]++;else for(;0<y;)F=6>y?y:6,F>y-3&&F<y&&(F=y-3),J[H++]=16,J[H++]=F-3,O[16]++,y-=F}d=G?J.subarray(0,H):J.slice(0,H);la=ra(O,7);for(D=0;19>D;D++)sa[D]=la[gb[D]];for(Z=19;4<Z&&0===sa[Z-1];Z--);Ha=ta(la);C.d(M-257,5,z);C.d(Y-1,5,z);C.d(Z-4,4,z);for(D=0;D<Z;D++)C.d(sa[D],3,z);D=0;for(Ia=d.length;D<Ia;D++)if($=
d[D],C.d(Ha[$],la[$],z),16<=$){D++;switch($){case 16:ma=2;break;case 17:ma=3;break;case 18:ma=7;break;default:m("invalid code: "+$)}C.d(d[D],ma,z)}var La=[Fa,da],Ma=[Ga,ea],P,Na,ga,va,Oa,Pa,Qa,Ra;Oa=La[0];Pa=La[1];Qa=Ma[0];Ra=Ma[1];P=0;for(Na=v.length;P<Na;++P)if(ga=v[P],C.d(Oa[ga],Pa[ga],z),256<ga)C.d(v[++P],v[++P],z),va=v[++P],C.d(Qa[va],Ra[va],z),C.d(v[++P],v[++P],z);else if(256===ga)break;this.a=C.finish();this.b=this.a.length;break;default:m("invalid compression type")}return this.a};
function ua(d,a){this.length=d;this.G=a}
var wa=function(){function d(b){switch(z){case 3===b:return[257,b-3,0];case 4===b:return[258,b-4,0];case 5===b:return[259,b-5,0];case 6===b:return[260,b-6,0];case 7===b:return[261,b-7,0];case 8===b:return[262,b-8,0];case 9===b:return[263,b-9,0];case 10===b:return[264,b-10,0];case 12>=b:return[265,b-11,1];case 14>=b:return[266,b-13,1];case 16>=b:return[267,b-15,1];case 18>=b:return[268,b-17,1];case 22>=b:return[269,b-19,2];case 26>=b:return[270,b-23,2];case 30>=b:return[271,b-27,2];case 34>=b:return[272,
b-31,2];case 42>=b:return[273,b-35,3];case 50>=b:return[274,b-43,3];case 58>=b:return[275,b-51,3];case 66>=b:return[276,b-59,3];case 82>=b:return[277,b-67,4];case 98>=b:return[278,b-83,4];case 114>=b:return[279,b-99,4];case 130>=b:return[280,b-115,4];case 162>=b:return[281,b-131,5];case 194>=b:return[282,b-163,5];case 226>=b:return[283,b-195,5];case 257>=b:return[284,b-227,5];case 258===b:return[285,b-258,0];default:m("invalid length: "+b)}}var a=[],c,e;for(c=3;258>=c;c++)e=d(c),a[c]=e[2]<<24|e[1]<<
16|e[0];return a}(),xa=G?new Uint32Array(wa):wa;
function qa(d,a){function c(b,c){var a=b.G,d=[],e=0,f;f=xa[b.length];d[e++]=f&65535;d[e++]=f>>16&255;d[e++]=f>>24;var g;switch(z){case 1===a:g=[0,a-1,0];break;case 2===a:g=[1,a-2,0];break;case 3===a:g=[2,a-3,0];break;case 4===a:g=[3,a-4,0];break;case 6>=a:g=[4,a-5,1];break;case 8>=a:g=[5,a-7,1];break;case 12>=a:g=[6,a-9,2];break;case 16>=a:g=[7,a-13,2];break;case 24>=a:g=[8,a-17,3];break;case 32>=a:g=[9,a-25,3];break;case 48>=a:g=[10,a-33,4];break;case 64>=a:g=[11,a-49,4];break;case 96>=a:g=[12,a-
65,5];break;case 128>=a:g=[13,a-97,5];break;case 192>=a:g=[14,a-129,6];break;case 256>=a:g=[15,a-193,6];break;case 384>=a:g=[16,a-257,7];break;case 512>=a:g=[17,a-385,7];break;case 768>=a:g=[18,a-513,8];break;case 1024>=a:g=[19,a-769,8];break;case 1536>=a:g=[20,a-1025,9];break;case 2048>=a:g=[21,a-1537,9];break;case 3072>=a:g=[22,a-2049,10];break;case 4096>=a:g=[23,a-3073,10];break;case 6144>=a:g=[24,a-4097,11];break;case 8192>=a:g=[25,a-6145,11];break;case 12288>=a:g=[26,a-8193,12];break;case 16384>=
a:g=[27,a-12289,12];break;case 24576>=a:g=[28,a-16385,13];break;case 32768>=a:g=[29,a-24577,13];break;default:m("invalid distance")}f=g;d[e++]=f[0];d[e++]=f[1];d[e++]=f[2];var h,k;h=0;for(k=d.length;h<k;++h)n[l++]=d[h];t[d[0]]++;x[d[3]]++;s=b.length+c-1;r=null}var e,b,f,g,h,k={},p,q,r,n=G?new Uint16Array(2*a.length):[],l=0,s=0,t=new (G?Uint32Array:Array)(286),x=new (G?Uint32Array:Array)(30),E=d.w,B;if(!G){for(f=0;285>=f;)t[f++]=0;for(f=0;29>=f;)x[f++]=0}t[256]=1;e=0;for(b=a.length;e<b;++e){f=h=0;
for(g=3;f<g&&e+f!==b;++f)h=h<<8|a[e+f];k[h]===w&&(k[h]=[]);p=k[h];if(!(0<s--)){for(;0<p.length&&32768<e-p[0];)p.shift();if(e+3>=b){r&&c(r,-1);f=0;for(g=b-e;f<g;++f)B=a[e+f],n[l++]=B,++t[B];break}0<p.length?(q=ya(a,e,p),r?r.length<q.length?(B=a[e-1],n[l++]=B,++t[B],c(q,0)):c(r,-1):q.length<E?r=q:c(q,0)):r?c(r,-1):(B=a[e],n[l++]=B,++t[B])}p.push(e)}n[l++]=256;t[256]++;d.L=t;d.K=x;return G?n.subarray(0,l):n}
function ya(d,a,c){var e,b,f=0,g,h,k,p,q=d.length;h=0;p=c.length;a:for(;h<p;h++){e=c[p-h-1];g=3;if(3<f){for(k=f;3<k;k--)if(d[e+k-1]!==d[a+k-1])continue a;g=f}for(;258>g&&a+g<q&&d[e+g]===d[a+g];)++g;g>f&&(b=e,f=g);if(258===g)break}return new ua(f,a-b)}
function ra(d,a){var c=d.length,e=new ja(572),b=new (G?Uint8Array:Array)(c),f,g,h,k,p;if(!G)for(k=0;k<c;k++)b[k]=0;for(k=0;k<c;++k)0<d[k]&&e.push(k,d[k]);f=Array(e.length/2);g=new (G?Uint32Array:Array)(e.length/2);if(1===f.length)return b[e.pop().index]=1,b;k=0;for(p=e.length/2;k<p;++k)f[k]=e.pop(),g[k]=f[k].value;h=za(g,g.length,a);k=0;for(p=f.length;k<p;++k)b[f[k].index]=h[k];return b}
function za(d,a,c){function e(b){var c=k[b][p[b]];c===a?(e(b+1),e(b+1)):--g[c];++p[b]}var b=new (G?Uint16Array:Array)(c),f=new (G?Uint8Array:Array)(c),g=new (G?Uint8Array:Array)(a),h=Array(c),k=Array(c),p=Array(c),q=(1<<c)-a,r=1<<c-1,n,l,s,t,x;b[c-1]=a;for(l=0;l<c;++l)q<r?f[l]=0:(f[l]=1,q-=r),q<<=1,b[c-2-l]=(b[c-1-l]/2|0)+a;b[0]=f[0];h[0]=Array(b[0]);k[0]=Array(b[0]);for(l=1;l<c;++l)b[l]>2*b[l-1]+f[l]&&(b[l]=2*b[l-1]+f[l]),h[l]=Array(b[l]),k[l]=Array(b[l]);for(n=0;n<a;++n)g[n]=c;for(s=0;s<b[c-1];++s)h[c-
1][s]=d[s],k[c-1][s]=s;for(n=0;n<c;++n)p[n]=0;1===f[c-1]&&(--g[0],++p[c-1]);for(l=c-2;0<=l;--l){t=n=0;x=p[l+1];for(s=0;s<b[l];s++)t=h[l+1][x]+h[l+1][x+1],t>d[n]?(h[l][s]=t,k[l][s]=a,x+=2):(h[l][s]=d[n],k[l][s]=n,++n);p[l]=0;1===f[l]&&e(l)}return g}
function ta(d){var a=new (G?Uint16Array:Array)(d.length),c=[],e=[],b=0,f,g,h,k;f=0;for(g=d.length;f<g;f++)c[d[f]]=(c[d[f]]|0)+1;f=1;for(g=16;f<=g;f++)e[f]=b,b+=c[f]|0,b<<=1;f=0;for(g=d.length;f<g;f++){b=e[d[f]];e[d[f]]+=1;h=a[f]=0;for(k=d[f];h<k;h++)a[f]=a[f]<<1|b&1,b>>>=1}return a};function U(d,a){this.l=[];this.m=32768;this.e=this.g=this.c=this.q=0;this.input=G?new Uint8Array(d):d;this.s=!1;this.n=Aa;this.B=!1;if(a||!(a={}))a.index&&(this.c=a.index),a.bufferSize&&(this.m=a.bufferSize),a.bufferType&&(this.n=a.bufferType),a.resize&&(this.B=a.resize);switch(this.n){case Ba:this.b=32768;this.a=new (G?Uint8Array:Array)(32768+this.m+258);break;case Aa:this.b=0;this.a=new (G?Uint8Array:Array)(this.m);this.f=this.J;this.t=this.H;this.o=this.I;break;default:m(Error("invalid inflate mode"))}}
var Ba=0,Aa=1,Ca={D:Ba,C:Aa};
U.prototype.p=function(){for(;!this.s;){var d=V(this,3);d&1&&(this.s=z);d>>>=1;switch(d){case 0:var a=this.input,c=this.c,e=this.a,b=this.b,f=a.length,g=w,h=w,k=e.length,p=w;this.e=this.g=0;c+1>=f&&m(Error("invalid uncompressed block header: LEN"));g=a[c++]|a[c++]<<8;c+1>=f&&m(Error("invalid uncompressed block header: NLEN"));h=a[c++]|a[c++]<<8;g===~h&&m(Error("invalid uncompressed block header: length verify"));c+g>a.length&&m(Error("input buffer is broken"));switch(this.n){case Ba:for(;b+g>e.length;){p=
k-b;g-=p;if(G)e.set(a.subarray(c,c+p),b),b+=p,c+=p;else for(;p--;)e[b++]=a[c++];this.b=b;e=this.f();b=this.b}break;case Aa:for(;b+g>e.length;)e=this.f({v:2});break;default:m(Error("invalid inflate mode"))}if(G)e.set(a.subarray(c,c+g),b),b+=g,c+=g;else for(;g--;)e[b++]=a[c++];this.c=c;this.b=b;this.a=e;break;case 1:this.o(Da,Ea);break;case 2:for(var q=V(this,5)+257,r=V(this,5)+1,n=V(this,4)+4,l=new (G?Uint8Array:Array)(Sa.length),s=w,t=w,x=w,E=w,B=w,C=w,L=w,v=w,M=w,v=0;v<n;++v)l[Sa[v]]=V(this,3);if(!G){v=
n;for(n=l.length;v<n;++v)l[Sa[v]]=0}s=S(l);E=new (G?Uint8Array:Array)(q+r);v=0;for(M=q+r;v<M;)switch(B=Ta(this,s),B){case 16:for(L=3+V(this,2);L--;)E[v++]=C;break;case 17:for(L=3+V(this,3);L--;)E[v++]=0;C=0;break;case 18:for(L=11+V(this,7);L--;)E[v++]=0;C=0;break;default:C=E[v++]=B}t=G?S(E.subarray(0,q)):S(E.slice(0,q));x=G?S(E.subarray(q)):S(E.slice(q));this.o(t,x);break;default:m(Error("unknown BTYPE: "+d))}}return this.t()};
var Ua=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],Sa=G?new Uint16Array(Ua):Ua,Va=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258],Wa=G?new Uint16Array(Va):Va,Xa=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0],Ya=G?new Uint8Array(Xa):Xa,Za=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],$a=G?new Uint16Array(Za):Za,ab=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,
10,11,11,12,12,13,13],bb=G?new Uint8Array(ab):ab,cb=new (G?Uint8Array:Array)(288),W,db;W=0;for(db=cb.length;W<db;++W)cb[W]=143>=W?8:255>=W?9:279>=W?7:8;var Da=S(cb),eb=new (G?Uint8Array:Array)(30),fb,hb;fb=0;for(hb=eb.length;fb<hb;++fb)eb[fb]=5;var Ea=S(eb);function V(d,a){for(var c=d.g,e=d.e,b=d.input,f=d.c,g=b.length,h;e<a;)f>=g&&m(Error("input buffer is broken")),c|=b[f++]<<e,e+=8;h=c&(1<<a)-1;d.g=c>>>a;d.e=e-a;d.c=f;return h}
function Ta(d,a){for(var c=d.g,e=d.e,b=d.input,f=d.c,g=b.length,h=a[0],k=a[1],p,q;e<k&&!(f>=g);)c|=b[f++]<<e,e+=8;p=h[c&(1<<k)-1];q=p>>>16;q>e&&m(Error("invalid code length: "+q));d.g=c>>q;d.e=e-q;d.c=f;return p&65535}
U.prototype.o=function(d,a){var c=this.a,e=this.b;this.u=d;for(var b=c.length-258,f,g,h,k;256!==(f=Ta(this,d));)if(256>f)e>=b&&(this.b=e,c=this.f(),e=this.b),c[e++]=f;else{g=f-257;k=Wa[g];0<Ya[g]&&(k+=V(this,Ya[g]));f=Ta(this,a);h=$a[f];0<bb[f]&&(h+=V(this,bb[f]));e>=b&&(this.b=e,c=this.f(),e=this.b);for(;k--;)c[e]=c[e++-h]}for(;8<=this.e;)this.e-=8,this.c--;this.b=e};
U.prototype.I=function(d,a){var c=this.a,e=this.b;this.u=d;for(var b=c.length,f,g,h,k;256!==(f=Ta(this,d));)if(256>f)e>=b&&(c=this.f(),b=c.length),c[e++]=f;else{g=f-257;k=Wa[g];0<Ya[g]&&(k+=V(this,Ya[g]));f=Ta(this,a);h=$a[f];0<bb[f]&&(h+=V(this,bb[f]));e+k>b&&(c=this.f(),b=c.length);for(;k--;)c[e]=c[e++-h]}for(;8<=this.e;)this.e-=8,this.c--;this.b=e};
U.prototype.f=function(){var d=new (G?Uint8Array:Array)(this.b-32768),a=this.b-32768,c,e,b=this.a;if(G)d.set(b.subarray(32768,d.length));else{c=0;for(e=d.length;c<e;++c)d[c]=b[c+32768]}this.l.push(d);this.q+=d.length;if(G)b.set(b.subarray(a,a+32768));else for(c=0;32768>c;++c)b[c]=b[a+c];this.b=32768;return b};
U.prototype.J=function(d){var a,c=this.input.length/this.c+1|0,e,b,f,g=this.input,h=this.a;d&&("number"===typeof d.v&&(c=d.v),"number"===typeof d.F&&(c+=d.F));2>c?(e=(g.length-this.c)/this.u[2],f=258*(e/2)|0,b=f<h.length?h.length+f:h.length<<1):b=h.length*c;G?(a=new Uint8Array(b),a.set(h)):a=h;return this.a=a};
U.prototype.t=function(){var d=0,a=this.a,c=this.l,e,b=new (G?Uint8Array:Array)(this.q+(this.b-32768)),f,g,h,k;if(0===c.length)return G?this.a.subarray(32768,this.b):this.a.slice(32768,this.b);f=0;for(g=c.length;f<g;++f){e=c[f];h=0;for(k=e.length;h<k;++h)b[d++]=e[h]}f=32768;for(g=this.b;f<g;++f)b[d++]=a[f];this.l=[];return this.buffer=b};
U.prototype.H=function(){var d,a=this.b;G?this.B?(d=new Uint8Array(a),d.set(this.a.subarray(0,a))):d=this.a.subarray(0,a):(this.a.length>a&&(this.a.length=a),d=this.a);return this.buffer=d};function ib(d){if("string"===typeof d){var a=d.split(""),c,e;c=0;for(e=a.length;c<e;c++)a[c]=(a[c].charCodeAt(0)&255)>>>0;d=a}for(var b=1,f=0,g=d.length,h,k=0;0<g;){h=1024<g?1024:g;g-=h;do b+=d[k++],f+=b;while(--h);b%=65521;f%=65521}return(f<<16|b)>>>0};function jb(d,a){var c,e;this.input=d;this.c=0;if(a||!(a={}))a.index&&(this.c=a.index),a.verify&&(this.M=a.verify);c=d[this.c++];e=d[this.c++];switch(c&15){case kb:this.method=kb;break;default:m(Error("unsupported compression method"))}0!==((c<<8)+e)%31&&m(Error("invalid fcheck flag:"+((c<<8)+e)%31));e&32&&m(Error("fdict flag is not supported"));this.A=new U(d,{index:this.c,bufferSize:a.bufferSize,bufferType:a.bufferType,resize:a.resize})}
jb.prototype.p=function(){var d=this.input,a,c;a=this.A.p();this.c=this.A.c;this.M&&(c=(d[this.c++]<<24|d[this.c++]<<16|d[this.c++]<<8|d[this.c++])>>>0,c!==ib(a)&&m(Error("invalid adler-32 checksum")));return a};var kb=8;function lb(d,a){this.input=d;this.a=new (G?Uint8Array:Array)(32768);this.h=X.k;var c={},e;if((a||!(a={}))&&"number"===typeof a.compressionType)this.h=a.compressionType;for(e in a)c[e]=a[e];c.outputBuffer=this.a;this.z=new ka(this.input,c)}var X=oa;
lb.prototype.j=function(){var d,a,c,e,b,f,g,h=0;g=this.a;d=kb;switch(d){case kb:a=Math.LOG2E*Math.log(32768)-8;break;default:m(Error("invalid compression method"))}c=a<<4|d;g[h++]=c;switch(d){case kb:switch(this.h){case X.NONE:b=0;break;case X.r:b=1;break;case X.k:b=2;break;default:m(Error("unsupported compression type"))}break;default:m(Error("invalid compression method"))}e=b<<6|0;g[h++]=e|31-(256*c+e)%31;f=ib(this.input);this.z.b=h;g=this.z.j();h=g.length;G&&(g=new Uint8Array(g.buffer),g.length<=
h+4&&(this.a=new Uint8Array(g.length+4),this.a.set(g),g=this.a),g=g.subarray(0,h+4));g[h++]=f>>24&255;g[h++]=f>>16&255;g[h++]=f>>8&255;g[h++]=f&255;return g};function mb(d,a){var c,e,b,f;if(Object.keys)c=Object.keys(a);else for(e in c=[],b=0,a)c[b++]=e;b=0;for(f=c.length;b<f;++b)e=c[b],A(d+"."+e,a[e])};A("Zlib.Inflate",jb);A("Zlib.Inflate.prototype.decompress",jb.prototype.p);mb("Zlib.Inflate.BufferType",{ADAPTIVE:Ca.C,BLOCK:Ca.D});A("Zlib.Deflate",lb);A("Zlib.Deflate.compress",function(d,a){return(new lb(d,a)).j()});A("Zlib.Deflate.prototype.compress",lb.prototype.j);mb("Zlib.Deflate.CompressionType",{NONE:X.NONE,FIXED:X.r,DYNAMIC:X.k});}).call(this);
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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