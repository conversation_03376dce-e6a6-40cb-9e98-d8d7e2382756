{"homebrew_version": "1.6.9-10-gca892f8", "used_options": [], "unused_options": ["--with-qt", "--with-little-cms2", "--with-nss"], "built_as_bottle": true, "poured_from_bottle": true, "installed_as_dependency": false, "installed_on_request": true, "changed_files": ["ChangeLog", "INSTALL_RECEIPT.json", "lib/pkgconfig/poppler-cairo.pc", "lib/pkgconfig/poppler-cpp.pc", "lib/pkgconfig/poppler-glib.pc", "lib/pkgconfig/poppler-splash.pc", "lib/pkgconfig/poppler.pc", "share/pkgconfig/poppler-data.pc"], "time": 1530613008, "source_modified_time": 1529443140, "HEAD": null, "stdlib": "libcxx", "compiler": "clang", "aliases": [], "runtime_dependencies": [{"full_name": "libpng", "version": "1.6.34"}, {"full_name": "freetype", "version": "2.9.1"}, {"full_name": "fontconfig", "version": "2.13.0"}, {"full_name": "pixman", "version": "0.34.0"}, {"full_name": "gettext", "version": "********"}, {"full_name": "libffi", "version": "3.2.1"}, {"full_name": "pcre", "version": "8.42"}, {"full_name": "glib", "version": "2.56.1"}, {"full_name": "cairo", "version": "1.14.12"}, {"full_name": "jpeg", "version": "9c"}, {"full_name": "libtiff", "version": "4.0.9"}, {"full_name": "little-cms2", "version": "2.9"}, {"full_name": "openjpeg", "version": "2.3.0"}], "source": {"path": "/usr/local/Homebrew/Library/Taps/homebrew/homebrew-core/Formula/poppler.rb", "tap": "homebrew/core", "spec": "stable", "versions": {"stable": "0.66.0", "devel": "", "head": "HEAD", "version_scheme": 0}}}