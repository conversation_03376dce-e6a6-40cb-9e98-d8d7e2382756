package com.textract.api;

import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.Vertx;
import io.vertx.core.http.HttpServer;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.handler.BodyHandler;
import io.vertx.ext.web.handler.CorsHandler;
import io.vertx.ext.web.handler.StaticHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Set;

/**
 * Main Verticle for AWS Textract Document Extraction API
 * 
 * This verticle sets up the HTTP server and routes for document processing
 * using AWS Textract OCR technology.
 */
public class MainVerticle extends AbstractVerticle {

    private static final Logger logger = LoggerFactory.getLogger(MainVerticle.class);
    
    private static final int DEFAULT_PORT = 8080;
    private static final String DEFAULT_HOST = "0.0.0.0";
    
    private DocumentProcessor documentProcessor;
    private HttpServer server;

    @Override
    public void start(Promise<Void> startPromise) throws Exception {
        logger.info("Starting AWS Textract Document Extraction API...");
        
        // Initialize document processor
        documentProcessor = new DocumentProcessor(vertx);
        
        // Create HTTP server
        server = vertx.createHttpServer();
        
        // Setup router with routes
        Router router = setupRouter();
        
        // Get configuration - force port 3333 for now
        int port = 3333; // config().getInteger("http.port", DEFAULT_PORT);
        String host = config().getString("http.host", DEFAULT_HOST);
        
        // Start server
        server.requestHandler(router)
            .listen(port, host)
            .onSuccess(result -> {
                logger.info("✅ API Server started successfully on {}:{}", host, port);
                logger.info("📄 Document extraction endpoints available:");
                logger.info("   POST /upload - Upload and process documents");
                logger.info("   GET /api/grouped-by-person - Get grouped results");
                logger.info("   GET /health - Health check");
                startPromise.complete();
            })
            .onFailure(error -> {
                logger.error("❌ Failed to start API server: {}", error.getMessage());
                startPromise.fail(error);
            });
    }

    @Override
    public void stop(Promise<Void> stopPromise) throws Exception {
        logger.info("Stopping AWS Textract Document Extraction API...");
        
        if (server != null) {
            server.close()
                .onSuccess(result -> {
                    logger.info("✅ API Server stopped successfully");
                    stopPromise.complete();
                })
                .onFailure(error -> {
                    logger.error("❌ Error stopping API server: {}", error.getMessage());
                    stopPromise.fail(error);
                });
        } else {
            stopPromise.complete();
        }
    }

    /**
     * Setup router with all API routes and middleware
     */
    private Router setupRouter() {
        Router router = Router.router(vertx);
        
        // CORS handler - allow all origins for development
        CorsHandler corsHandler = CorsHandler.create()
            .addOrigin("*")
            .allowedMethods(Set.of(
                io.vertx.core.http.HttpMethod.GET,
                io.vertx.core.http.HttpMethod.POST,
                io.vertx.core.http.HttpMethod.PUT,
                io.vertx.core.http.HttpMethod.DELETE,
                io.vertx.core.http.HttpMethod.OPTIONS
            ))
            .allowedHeaders(Set.of(
                "Content-Type",
                "Authorization",
                "X-Requested-With",
                "Accept",
                "Origin"
            ));
        
        router.route().handler(corsHandler);
        
        // Body handler for file uploads (50MB limit)
        router.route().handler(BodyHandler.create()
            .setUploadsDirectory("uploads")
            .setDeleteUploadedFilesOnEnd(true)
            .setBodyLimit(50 * 1024 * 1024)); // 50MB
        
        // API Routes
        setupApiRoutes(router);
        
        // Error handler
        router.errorHandler(500, context -> {
            logger.error("Internal server error", context.failure());
            context.response()
                .setStatusCode(500)
                .putHeader("Content-Type", "application/json")
                .end(new JsonObject()
                    .put("success", false)
                    .put("error", "Internal server error")
                    .put("message", context.failure().getMessage())
                    .encode());
        });
        
        return router;
    }

    /**
     * Setup API routes
     */
    private void setupApiRoutes(Router router) {
        // Health check endpoint
        router.get("/health").handler(context -> {
            logger.debug("Health check requested");
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(new JsonObject()
                    .put("status", "OK")
                    .put("message", "Document Extraction API is running")
                    .put("timestamp", System.currentTimeMillis())
                    .put("version", "1.0.0")
                    .encode());
        });
        
        // Document upload endpoint
        router.post("/upload").handler(context -> {
            logger.info("Document upload requested");
            documentProcessor.handleUpload(context);
        });
        
        // Get grouped documents endpoint
        router.get("/api/grouped-by-person").handler(context -> {
            logger.info("Grouped documents requested");
            documentProcessor.handleGetGroupedDocuments(context);
        });
        
        // Process personal documents endpoint
        router.post("/api/process-personal-documents").handler(context -> {
            logger.info("Process personal documents requested");
            documentProcessor.handleProcessPersonalDocuments(context);
        });
        
        // API info endpoint
        router.get("/api/info").handler(context -> {
            context.response()
                .putHeader("Content-Type", "application/json")
                .end(new JsonObject()
                    .put("name", "AWS Textract Document Extraction API")
                    .put("version", "1.0.0")
                    .put("description", "Java Vert.x API for document processing using AWS Textract")
                    .put("endpoints", new JsonObject()
                        .put("upload", "POST /upload")
                        .put("grouped", "GET /api/grouped-by-person")
                        .put("process", "POST /api/process-personal-documents")
                        .put("health", "GET /health"))
                    .encode());
        });
        
        // Catch-all for undefined routes
        router.route().last().handler(context -> {
            context.response()
                .setStatusCode(404)
                .putHeader("Content-Type", "application/json")
                .end(new JsonObject()
                    .put("success", false)
                    .put("error", "Endpoint not found")
                    .put("path", context.request().path())
                    .put("method", context.request().method().toString())
                    .encode());
        });
    }

    /**
     * Main method to start the application
     */
    public static void main(String[] args) {
        Vertx vertx = Vertx.vertx();
        vertx.deployVerticle(new MainVerticle());
    }
}
