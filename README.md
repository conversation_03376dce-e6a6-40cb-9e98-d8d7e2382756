# AWS Textract Legal Document Scanner

A comprehensive document extraction service that uses AWS Textract to process legal documents like Aadhaar cards, PAN cards, and GST certificates. The application features a separated architecture with dedicated API and UI servers, and provides both rich formatted views and raw JSON output.

## 🏗️ Architecture

### Separated Server Architecture
- **API Server** (Port 3005): Handles document processing and data extraction
- **UI Server** (Port 3004): Serves the web interface and static files
- **CORS Enabled**: Allows seamless communication between UI and API

### Key Features
- ✅ **Dual View System**: Rich formatted view and JSON view
- ✅ **Document Grouping**: Groups documents by person/company
- ✅ **Export Functionality**: Export summaries and print results
- ✅ **Multi-file Upload**: Process multiple documents simultaneously
- ✅ **AWS Textract Integration**: Advanced OCR with forms and tables detection
- ✅ **Responsive Design**: Modern, mobile-friendly interface

## 📁 Project Structure

```
├── api/                          # API Server (Port 3005)
│   ├── server.js                 # Full Textract server with AWS integration
│   ├── test-server.js           # Mock server for testing
│   ├── simple-server.js         # Basic HTTP server
│   ├── package.json             # API dependencies
│   └── uploads/                 # Uploaded files storage
├── ui/                          # UI Server (Port 3004)
│   ├── server.js                # Express server for UI
│   ├── simple-server.js         # Basic HTTP server for UI
│   ├── package.json             # UI dependencies
│   └── public/                  # Static web files
│       ├── index.html           # Main interface
│       ├── styles.css           # Styling
│       └── script.js            # Frontend logic
├── new-jsont-extract/           # Original monolithic server
│   ├── server.js                # Combined API+UI server
│   └── Textract/                # Original Textract implementation
└── README.md                    # This file
```

## 🚀 Quick Start

### Prerequisites
- Node.js (v14 or higher)
- AWS Account with Textract access
- Git

### Installation

1. **Clone the repository**
   ```bash
   git clone https://gitlab.com/test-automation5562328/aws-textract-legal-document-scanner.git
   cd aws-textract-legal-document-scanner
   ```

2. **Install API dependencies**
   ```bash
   cd api
   npm install
   cd ..
   ```

3. **Install UI dependencies**
   ```bash
   cd ui
   npm install
   cd ..
   ```

4. **Configure AWS credentials** (for full Textract functionality)
   - Update AWS credentials in `api/server.js`
   - Or use environment variables/AWS CLI configuration

### Running the Application

#### Option 1: Separated Architecture (Recommended)

**Terminal 1 - Start API Server:**
```bash
cd api
node server.js
# API will run on http://localhost:3005
```

**Terminal 2 - Start UI Server:**
```bash
cd ui
node simple-server.js
# UI will run on http://localhost:3004
```

#### Option 2: Mock Testing
```bash
cd api
node test-server.js  # Mock API with sample data
```

#### Option 3: Original Monolithic Server
```bash
cd new-jsont-extract/Textract
npm start
# Combined server on http://localhost:3004
```

### Access the Application
- **Main Interface**: http://localhost:3004
- **API Health Check**: http://localhost:3005/health
- **API Endpoints**: http://localhost:3005/api/

## 📋 API Endpoints

| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/upload` | Upload and process documents |
| GET | `/api/grouped-by-person` | Get documents grouped by person |
| POST | `/api/process-personal-documents` | Process personal documents |
| GET | `/health` | API health check |

## 🎨 UI Features

### Rich View
- **Summary Dashboard**: Statistics on processed documents
- **Person Cards**: Organized display with avatars
- **Document Grid**: Clean layout with document icons
- **Field Display**: Structured presentation of extracted data

### JSON View
- **Raw Data**: Complete JSON output
- **Format Options**: Pretty-print JSON
- **Copy/Download**: Easy data export

### Export Options
- **📤 Export Summary**: Download formatted text summary
- **🖨️ Print**: Print-friendly document view
- **📋 Copy JSON**: Copy raw data to clipboard
- **💾 Download JSON**: Save JSON file

## 🔧 Configuration

### AWS Textract Setup
1. Create AWS account and enable Textract service
2. Create IAM user with Textract permissions
3. Update credentials in `api/server.js`:
   ```javascript
   credentials: {
     accessKeyId: "YOUR_ACCESS_KEY",
     secretAccessKey: "YOUR_SECRET_KEY"
   }
   ```

### Environment Variables
Create `.env` file in the api directory:
```env
AWS_REGION=ap-south-1
AWS_ACCESS_KEY_ID=your_access_key
AWS_SECRET_ACCESS_KEY=your_secret_key
PORT=3005
```

## 📄 Supported Document Types

- **Aadhaar Cards** 🆔
- **PAN Cards** 📄
- **Passport** 📘
- **Driving License** 🚗
- **Voter ID** 🗳️
- **GST Certificates** 🏢
- **PDF Documents** 📋

## 🛠️ Development

### Adding New Document Types
1. Update document type mapping in `api/server.js`
2. Add new icon in `ui/public/script.js` `getDocumentIcon()` method
3. Update field extraction logic if needed

### Customizing UI
- Modify `ui/public/styles.css` for styling changes
- Update `ui/public/script.js` for functionality changes
- Edit `ui/public/index.html` for layout modifications

## 🐛 Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Check what's using the port
   netstat -ano | findstr :3004
   # Kill the process or use different port
   ```

2. **AWS Credentials Error**
   - Verify AWS credentials are correct
   - Check IAM permissions for Textract
   - Ensure region is supported

3. **CORS Errors**
   - Verify API server is running on port 3005
   - Check CORS configuration in API server

4. **Upload Failures**
   - Check file size limits
   - Verify supported file formats
   - Ensure uploads directory exists

## 📝 License

This project is licensed under the MIT License.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For issues and questions:
- Create an issue in the GitLab repository
- Check the troubleshooting section
- Review the API documentation
