package com.textract.api;

import io.vertx.core.json.JsonObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.SdkBytes;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.textract.TextractClient;
import software.amazon.awssdk.services.textract.model.*;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Service for AWS Textract integration and document processing
 */
public class TextractService {
    
    private static final Logger logger = LoggerFactory.getLogger(TextractService.class);
    
    private TextractClient textractClient;
    private S3Client s3Client;
    private boolean useAwsTextract;
    private String s3BucketName;
    
    // Document type patterns
    private static final Map<String, Pattern> DOCUMENT_PATTERNS = Map.of(
        "AADHAAR", Pattern.compile("(?i)(aadhaar|आधार|unique identification|uid)"),
        "PAN", Pattern.compile("(?i)(permanent account number|pan|income tax|पैन)"),
        "PASSPORT", Pattern.compile("(?i)(passport|republic of india|भारत गणराज्य)"),
        "DRIVING_LICENSE", Pattern.compile("(?i)(driving licence|driving license|dl|ड्राइविंग लाइसेंस)"),
        "VOTER_ID", Pattern.compile("(?i)(voter|election|electoral|मतदाता)"),
        "GST_CERTIFICATE", Pattern.compile("(?i)(gst|goods and services tax|gstin|tax certificate)")
    );
    
    // Field extraction patterns
    private static final Map<String, Pattern> FIELD_PATTERNS = Map.of(
        "PAN_NUMBER", Pattern.compile("[A-Z]{5}\\d{4}[A-Z]"),
        "AADHAAR_NUMBER", Pattern.compile("\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}"),
        "PASSPORT_NUMBER", Pattern.compile("[A-Z]\\d{7}"),
        "DATE_PATTERN", Pattern.compile("\\d{1,2}[/\\-]\\d{1,2}[/\\-]\\d{4}"),
        "NAME_PATTERN", Pattern.compile("NAME[:\\s]*\\n([A-Z\\s]+?)(?:\\n|FATHER|DOB|GENDER|ADDRESS|$)", Pattern.CASE_INSENSITIVE),
        "DOB_PATTERN", Pattern.compile("(?:DATE OF BIRTH|DOB)[:\\s]*\\n?(\\d{1,2}[/\\-]\\d{1,2}[/\\-]\\d{4})", Pattern.CASE_INSENSITIVE),
        "ADDRESS_PATTERN", Pattern.compile("(?:ADDRESS|ADDR)[:\\s]*\\n([^\\n]+(?:\\n[^\\n]+)*?)(?:\\n\\n|$)", Pattern.CASE_INSENSITIVE)
    );

    public TextractService() {
        // Initialize with default configuration - will be updated when config is loaded
        this.textractClient = null;
        this.useAwsTextract = false;
    }

    /**
     * Initialize AWS Textract and S3 clients with configuration
     */
    public void initialize(JsonObject config) {
        try {
            JsonObject awsConfig = config.getJsonObject("aws", new JsonObject());

            String accessKeyId = awsConfig.getString("accessKeyId");
            String secretAccessKey = awsConfig.getString("secretAccessKey");
            String region = awsConfig.getString("region", "us-east-1");

            // Get S3 bucket name from configuration
            JsonObject s3Config = awsConfig.getJsonObject("s3", new JsonObject());
            this.s3BucketName = s3Config.getString("bucketName", "doc-scan-textract");

            if (accessKeyId != null && secretAccessKey != null) {
                // Create AWS credentials from configuration
                AwsBasicCredentials awsCredentials = AwsBasicCredentials.create(accessKeyId, secretAccessKey);

                // Map region string to Region enum
                Region awsRegion = Region.of(region);

                // Initialize Textract client
                this.textractClient = TextractClient.builder()
                    .region(awsRegion)
                    .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                    .build();

                // Initialize S3 client
                this.s3Client = S3Client.builder()
                    .region(awsRegion)
                    .credentialsProvider(StaticCredentialsProvider.create(awsCredentials))
                    .build();

                this.useAwsTextract = true;
                logger.info("✅ AWS Textract and S3 clients initialized successfully");
                logger.info("✅ Using S3 bucket: {} in region: {}", this.s3BucketName, region);

                // Test AWS connection
                testAwsConnection();
            } else {
                logger.warn("⚠️ AWS credentials not found in configuration, using mock processing");
                this.useAwsTextract = false;
            }
        } catch (Exception e) {
            logger.warn("⚠️ AWS services initialization failed, using mock processing: {}", e.getMessage());
            this.textractClient = null;
            this.s3Client = null;
            this.useAwsTextract = false;
        }
    }

    /**
     * Process document using AWS Textract only - no mock data
     */
    public JsonObject processDocument(byte[] documentBytes, String filename) {
        if (useAwsTextract) {
            logger.info("🔄 Processing document with AWS Textract: {}", filename);
            return processWithTextract(documentBytes, filename);
        } else {
            logger.error("❌ AWS Textract not available - cannot process document: {}", filename);
            logger.error("❌ Reason: AWS credentials not configured or connection failed");
            throw new RuntimeException("AWS Textract service is not available. Please check AWS credentials and configuration.");
        }
    }

    /**
     * Process document using AWS Textract
     */
    private JsonObject processWithTextract(byte[] documentBytes, String filename) {
        try {
            logger.info("Processing document with AWS Textract: {}", filename);
            
            // Create document object
            Document document = Document.builder()
                .bytes(SdkBytes.fromByteArray(documentBytes))
                .build();
            
            // Try FORMS analysis first
            JsonObject result = analyzeDocumentWithForms(document, filename);
            
            // If no key-value pairs found, try basic text detection
            if (result.getJsonObject("extracted_fields").isEmpty()) {
                logger.info("No forms detected, trying text detection for: {}", filename);
                result = analyzeDocumentWithTextDetection(document, filename);
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("Error processing document with Textract: {}", e.getMessage());
            // No fallback - throw exception to indicate AWS Textract failure
            throw new RuntimeException("AWS Textract processing failed: " + e.getMessage(), e);
        }
    }

    /**
     * Analyze document using FORMS feature
     */
    private JsonObject analyzeDocumentWithForms(Document document, String filename) {
        try {
            AnalyzeDocumentRequest request = AnalyzeDocumentRequest.builder()
                .document(document)
                .featureTypes(FeatureType.FORMS, FeatureType.TABLES)
                .build();
            
            AnalyzeDocumentResponse response = textractClient.analyzeDocument(request);
            
            return extractDataFromResponse(response.blocks(), filename);
            
        } catch (Exception e) {
            logger.error("Error in forms analysis: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Analyze document using basic text detection
     */
    private JsonObject analyzeDocumentWithTextDetection(Document document, String filename) {
        try {
            DetectDocumentTextRequest request = DetectDocumentTextRequest.builder()
                .document(document)
                .build();
            
            DetectDocumentTextResponse response = textractClient.detectDocumentText(request);
            
            return extractDataFromTextBlocks(response.blocks(), filename);
            
        } catch (Exception e) {
            logger.error("Error in text detection: {}", e.getMessage());
            throw e;
        }
    }

    /**
     * Extract structured data from Textract response blocks
     */
    private JsonObject extractDataFromResponse(List<Block> blocks, String filename) {
        Map<String, String> keyValuePairs = new HashMap<>();
        StringBuilder allText = new StringBuilder();
        
        // Extract key-value pairs and all text
        Map<String, Block> blockMap = blocks.stream()
            .collect(Collectors.toMap(Block::id, block -> block));
        
        for (Block block : blocks) {
            if (block.blockType() == BlockType.KEY_VALUE_SET) {
                if (block.entityTypes().contains(EntityType.KEY)) {
                    String key = getBlockText(block, blockMap).trim();
                    String value = "";
                    
                    if (block.relationships() != null) {
                        for (Relationship relationship : block.relationships()) {
                            if (relationship.type() == RelationshipType.VALUE) {
                                for (String valueId : relationship.ids()) {
                                    Block valueBlock = blockMap.get(valueId);
                                    if (valueBlock != null) {
                                        value = getBlockText(valueBlock, blockMap).trim();
                                        break;
                                    }
                                }
                            }
                        }
                    }
                    
                    if (!key.isEmpty() && !value.isEmpty()) {
                        keyValuePairs.put(key.toUpperCase(), value);
                    }
                }
            } else if (block.blockType() == BlockType.LINE) {
                allText.append(block.text()).append("\n");
            }
        }
        
        // Map to standard fields
        JsonObject extractedFields = mapToStandardFields(keyValuePairs, allText.toString(), filename);
        
        return new JsonObject()
            .put("extracted_fields", extractedFields)
            .put("raw_text", allText.toString())
            .put("processing_method", "AWS_TEXTRACT_FORMS");
    }

    /**
     * Extract data from text-only blocks
     */
    private JsonObject extractDataFromTextBlocks(List<Block> blocks, String filename) {
        StringBuilder allText = new StringBuilder();
        
        for (Block block : blocks) {
            if (block.blockType() == BlockType.LINE) {
                allText.append(block.text()).append("\n");
            }
        }
        
        String text = allText.toString();
        JsonObject extractedFields = extractFromPlainText(text, filename);
        
        return new JsonObject()
            .put("extracted_fields", extractedFields)
            .put("raw_text", text)
            .put("processing_method", "AWS_TEXTRACT_TEXT_DETECTION");
    }

    /**
     * Get text content from a block
     */
    private String getBlockText(Block block, Map<String, Block> blockMap) {
        StringBuilder text = new StringBuilder();
        
        if (block.relationships() != null) {
            for (Relationship relationship : block.relationships()) {
                if (relationship.type() == RelationshipType.CHILD) {
                    for (String childId : relationship.ids()) {
                        Block childBlock = blockMap.get(childId);
                        if (childBlock != null && childBlock.blockType() == BlockType.WORD) {
                            text.append(childBlock.text()).append(" ");
                        }
                    }
                }
            }
        }
        
        return text.toString().trim();
    }

    /**
     * Map extracted data to standard field format
     */
    private JsonObject mapToStandardFields(Map<String, String> keyValuePairs, String allText, String filename) {
        JsonObject fields = new JsonObject();
        
        // Detect document type
        String documentType = detectDocumentType(allText);
        fields.put("DOCUMENT_TYPE", documentType);
        
        // If no key-value pairs, extract from plain text
        if (keyValuePairs.isEmpty()) {
            return extractFromPlainText(allText, filename);
        }
        
        // Map known fields
        for (Map.Entry<String, String> entry : keyValuePairs.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue();
            
            // Map to standard field names
            if (key.contains("NAME") && !key.contains("FATHER")) {
                parseNameField(value, fields);
            } else if (key.contains("DOB") || key.contains("DATE OF BIRTH")) {
                fields.put("DATE_OF_BIRTH", value);
            } else if (key.contains("ADDRESS")) {
                fields.put("ADDRESS", value);
            } else if (key.contains("GENDER")) {
                fields.put("GENDER", value.toUpperCase());
            } else if (key.contains("NUMBER") || key.contains("ID")) {
                fields.put("DOCUMENT_NUMBER", value);
            }
        }
        
        // Extract document number using patterns if not found
        if (!fields.containsKey("DOCUMENT_NUMBER")) {
            String docNumber = extractDocumentNumber(allText, documentType);
            if (docNumber != null) {
                fields.put("DOCUMENT_NUMBER", docNumber);
            }
        }
        
        return fields;
    }

    /**
     * Extract data from plain text using patterns
     */
    private JsonObject extractFromPlainText(String text, String filename) {
        JsonObject fields = new JsonObject();
        
        // Detect document type
        String documentType = detectDocumentType(text);
        fields.put("DOCUMENT_TYPE", documentType);
        
        // Extract name
        Matcher nameMatcher = FIELD_PATTERNS.get("NAME_PATTERN").matcher(text);
        if (nameMatcher.find()) {
            String fullName = nameMatcher.group(1).trim();
            parseNameField(fullName, fields);
        }
        
        // Extract date of birth
        Matcher dobMatcher = FIELD_PATTERNS.get("DOB_PATTERN").matcher(text);
        if (dobMatcher.find()) {
            fields.put("DATE_OF_BIRTH", dobMatcher.group(1));
        }
        
        // Extract document number
        String docNumber = extractDocumentNumber(text, documentType);
        if (docNumber != null) {
            fields.put("DOCUMENT_NUMBER", docNumber);
        }
        
        // Extract address
        Matcher addressMatcher = FIELD_PATTERNS.get("ADDRESS_PATTERN").matcher(text);
        if (addressMatcher.find()) {
            fields.put("ADDRESS", addressMatcher.group(1).trim());
        }
        
        return fields;
    }

    /**
     * Parse name field into first, middle, last names
     */
    private void parseNameField(String fullName, JsonObject fields) {
        String[] nameParts = fullName.trim().split("\\s+");
        
        if (nameParts.length >= 1) {
            fields.put("FIRST_NAME", nameParts[0]);
        }
        if (nameParts.length >= 2) {
            fields.put("LAST_NAME", nameParts[nameParts.length - 1]);
        }
        if (nameParts.length >= 3) {
            String[] middleParts = Arrays.copyOfRange(nameParts, 1, nameParts.length - 1);
            fields.put("MIDDLE_NAME", String.join(" ", middleParts));
        }
    }

    /**
     * Detect document type from text content
     */
    private String detectDocumentType(String text) {
        for (Map.Entry<String, Pattern> entry : DOCUMENT_PATTERNS.entrySet()) {
            if (entry.getValue().matcher(text).find()) {
                return entry.getKey();
            }
        }
        return "UNKNOWN";
    }

    /**
     * Extract document number based on document type
     */
    private String extractDocumentNumber(String text, String documentType) {
        Pattern pattern = null;
        
        switch (documentType) {
            case "PAN":
                pattern = FIELD_PATTERNS.get("PAN_NUMBER");
                break;
            case "AADHAAR":
                pattern = FIELD_PATTERNS.get("AADHAAR_NUMBER");
                break;
            case "PASSPORT":
                pattern = FIELD_PATTERNS.get("PASSPORT_NUMBER");
                break;
        }
        
        if (pattern != null) {
            Matcher matcher = pattern.matcher(text);
            if (matcher.find()) {
                return matcher.group();
            }
        }
        
        return null;
    }





    /**
     * Upload file to S3 bucket
     */
    public String uploadToS3(byte[] fileBytes, String filename) {
        if (s3Client == null || s3BucketName == null) {
            logger.warn("S3 client not initialized, cannot upload file: {}", filename);
            return null;
        }

        try {
            // Generate unique key for S3 object
            String timestamp = String.valueOf(System.currentTimeMillis());
            String s3Key = "documents/" + timestamp + "_" + filename;

            // Create put object request
            PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                .bucket(s3BucketName)
                .key(s3Key)
                .contentType(getContentType(filename))
                .build();

            // Upload file to S3
            PutObjectResponse response = s3Client.putObject(putObjectRequest,
                software.amazon.awssdk.core.sync.RequestBody.fromBytes(fileBytes));

            logger.info("✅ File uploaded to S3: s3://{}/{}", s3BucketName, s3Key);
            return s3Key;

        } catch (Exception e) {
            logger.error("❌ Failed to upload file to S3: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Extract fields from text using patterns
     */
    private void extractFieldsFromText(String text, JsonObject fields) {
        // Extract name
        Pattern namePattern = FIELD_PATTERNS.get("NAME_PATTERN");
        if (namePattern != null) {
            Matcher nameMatcher = namePattern.matcher(text);
            if (nameMatcher.find()) {
                fields.put("FIRST_NAME", nameMatcher.group(1).trim());
            }
        }

        // Extract date of birth
        Pattern dobPattern = FIELD_PATTERNS.get("DOB_PATTERN");
        if (dobPattern != null) {
            Matcher dobMatcher = dobPattern.matcher(text);
            if (dobMatcher.find()) {
                fields.put("DATE_OF_BIRTH", dobMatcher.group(1));
            }
        }

        // Extract document-specific numbers
        for (Map.Entry<String, Pattern> entry : FIELD_PATTERNS.entrySet()) {
            String fieldName = entry.getKey();
            Pattern pattern = entry.getValue();

            if (fieldName.endsWith("_NUMBER")) {
                Matcher matcher = pattern.matcher(text);
                if (matcher.find()) {
                    fields.put(fieldName, matcher.group());
                }
            }
        }

        // Extract general dates
        Pattern datePattern = FIELD_PATTERNS.get("DATE_PATTERN");
        if (datePattern != null) {
            Matcher dateMatcher = datePattern.matcher(text);
            if (dateMatcher.find() && !fields.containsKey("DATE_OF_BIRTH")) {
                fields.put("DATE_OF_BIRTH", dateMatcher.group());
            }
        }
    }

    /**
     * Get content type based on file extension
     */
    private String getContentType(String filename) {
        String lowerFilename = filename.toLowerCase();
        if (lowerFilename.endsWith(".pdf")) {
            return "application/pdf";
        } else if (lowerFilename.endsWith(".jpg") || lowerFilename.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerFilename.endsWith(".png")) {
            return "image/png";
        } else {
            return "application/octet-stream";
        }
    }

    /**
     * Test AWS connection
     */
    private void testAwsConnection() {
        try {
            if (textractClient != null) {
                // Try to list available features (this is a lightweight operation)
                logger.info("🔍 Testing AWS Textract connection...");
                // Note: We can't easily test without making an actual API call
                // So we'll just log that the client was created successfully
                logger.info("✅ AWS Textract client created successfully");
            }

            if (s3Client != null) {
                logger.info("🔍 Testing AWS S3 connection...");
                // Try to check if bucket exists (lightweight operation)
                try {
                    s3Client.headBucket(builder -> builder.bucket(s3BucketName));
                    logger.info("✅ S3 bucket '{}' is accessible", s3BucketName);
                } catch (Exception e) {
                    logger.warn("⚠️ S3 bucket '{}' may not exist or is not accessible: {}", s3BucketName, e.getMessage());
                    logger.warn("⚠️ Files will still be processed, but S3 upload may fail");
                }
            }
        } catch (Exception e) {
            logger.error("❌ AWS connection test failed: {}", e.getMessage());
            this.useAwsTextract = false;
        }
    }

    /**
     * Get AWS connection status
     */
    public JsonObject getAwsStatus() {
        JsonObject status = new JsonObject();
        status.put("textract_available", useAwsTextract);
        status.put("s3_bucket", s3BucketName);
        status.put("textract_client_initialized", textractClient != null);
        status.put("s3_client_initialized", s3Client != null);
        return status;
    }

    /**
     * Get S3 bucket name
     */
    public String getS3BucketName() {
        return s3BucketName;
    }
}
