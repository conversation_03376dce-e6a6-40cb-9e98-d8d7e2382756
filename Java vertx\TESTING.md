# Testing Guide - Java Vert.x Implementation

## Prerequisites

Before testing, ensure you have:

1. **Java 11 or higher** installed
   ```bash
   java -version
   ```

2. **Maven 3.6+** installed
   ```bash
   mvn -version
   ```

3. **Ports available**: 8080 (API) and 8081 (UI)

## Quick Test (Without Maven)

If <PERSON><PERSON> is not available, you can verify the project structure:

### 1. Check Project Structure
```
Java vertx/
├── api/
│   ├── src/main/java/com/textract/api/
│   │   ├── MainVerticle.java      ✓ Created
│   │   ├── DocumentProcessor.java ✓ Created
│   │   └── TextractService.java   ✓ Created
│   ├── src/main/resources/
│   │   ├── application.json       ✓ Created
│   │   └── logback.xml           ✓ Created
│   └── pom.xml                   ✓ Created
├── ui/
│   ├── src/main/java/com/textract/ui/
│   │   └── UIVerticle.java       ✓ Created
│   ├── src/main/resources/
│   │   ├── webroot/              ✓ Created
│   │   │   ├── index.html        ✓ Created
│   │   │   ├── script.js         ✓ Created
│   │   │   ├── styles.css        ✓ Created
│   │   │   └── api-docs.html     ✓ Created
│   │   ├── application.json      ✓ Created
│   │   └── logback.xml          ✓ Created
│   └── pom.xml                  ✓ Created
└── README.md                    ✓ Created
```

### 2. Verify Java Files

Check that the main Java files compile without syntax errors:

**API Server Classes:**
- `MainVerticle.java` - Main API server with routing
- `DocumentProcessor.java` - Document processing logic
- `TextractService.java` - AWS Textract integration

**UI Server Classes:**
- `UIVerticle.java` - UI server with static file serving and API proxy

## Full Testing (With Maven)

### 1. Build Projects

```bash
# Build API server
cd "Java vertx/api"
mvn clean compile

# Build UI server
cd "../ui"
mvn clean compile
```

### 2. Start API Server

```bash
cd "Java vertx/api"
mvn exec:java -Dexec.mainClass="io.vertx.core.Launcher" -Dexec.args="run com.textract.api.MainVerticle"
```

Expected output:
```
Starting AWS Textract Document Extraction API...
✅ API Server started successfully on 0.0.0.0:8080
📄 Document extraction endpoints available:
   POST /upload - Upload and process documents
   GET /api/grouped-by-person - Get grouped results
   GET /health - Health check
```

### 3. Test API Endpoints

With the API server running, test these endpoints:

```bash
# Health check
curl http://localhost:8080/health

# Get mock data
curl http://localhost:8080/api/grouped-by-person

# API info
curl http://localhost:8080/api/info
```

### 4. Start UI Server

In a new terminal:

```bash
cd "Java vertx/ui"
mvn exec:java -Dexec.mainClass="io.vertx.core.Launcher" -Dexec.args="run com.textract.ui.UIVerticle"
```

Expected output:
```
Starting Document Extraction UI Server...
✅ UI Server started successfully on 0.0.0.0:8081
🌐 Web interface available at: http://0.0.0.0:8081
🔗 API server configured at: http://localhost:8080
```

### 5. Test Web Interface

1. **Open browser**: http://localhost:8081
2. **Verify UI loads** with "Java Vert.x" badge
3. **Test file upload** (should proxy to API server)
4. **Check API docs**: http://localhost:8081/api-docs.html

### 6. Test Integration

1. **Upload a test file** through the web interface
2. **Verify processing** - should show extracted data
3. **Test both views** - Rich View and JSON View
4. **Check grouped data** - should group by person

## Using Startup Scripts

### Windows

```bash
# Start both servers
"Java vertx/start-both.bat"

# Or start individually
"Java vertx/start-api.bat"
"Java vertx/start-ui.bat"
```

## Expected Behavior

### API Server (Port 8080)
- ✅ Starts without errors
- ✅ Responds to health checks
- ✅ Returns mock data for grouped documents
- ✅ Handles file uploads (with mock processing)
- ✅ Proper CORS headers
- ✅ JSON responses

### UI Server (Port 8081)
- ✅ Serves static files
- ✅ Proxies API requests to port 8080
- ✅ Handles file uploads via proxy
- ✅ Shows "Java Vert.x" technology badge
- ✅ API documentation accessible

### Web Interface
- ✅ Drag & drop file upload
- ✅ Multiple file selection
- ✅ Rich view formatting
- ✅ JSON view with syntax highlighting
- ✅ Export and download functions
- ✅ Responsive design

## Troubleshooting

### Common Issues

1. **Port already in use**
   ```
   Error: Address already in use
   ```
   Solution: Stop other services on ports 8080/8081 or change ports in config

2. **Maven not found**
   ```
   mvn: command not found
   ```
   Solution: Install Maven or use IDE with Maven integration

3. **Java version issues**
   ```
   Unsupported class file major version
   ```
   Solution: Ensure Java 11+ is installed and JAVA_HOME is set

4. **AWS credentials warning**
   ```
   AWS Textract not available, using mock processing
   ```
   This is expected - the system falls back to mock data

### Verification Checklist

- [ ] API server starts on port 8080
- [ ] UI server starts on port 8081
- [ ] Health endpoints respond
- [ ] Web interface loads
- [ ] File upload works
- [ ] Mock data is returned
- [ ] API documentation is accessible
- [ ] Both Rich and JSON views work
- [ ] CORS is properly configured

## Performance Comparison

### Java Vert.x vs Node.js

**Startup Time:**
- Java Vert.x: ~3-5 seconds (JVM startup)
- Node.js: ~1-2 seconds

**Memory Usage:**
- Java Vert.x: ~100-150MB (JVM overhead)
- Node.js: ~50-80MB

**Request Handling:**
- Java Vert.x: Better under high load
- Node.js: Good for I/O intensive tasks

**Development:**
- Java Vert.x: Type safety, better tooling
- Node.js: Faster prototyping

## Success Criteria

The Java Vert.x implementation is successful if:

1. ✅ **Functional Parity** - All features from Node.js version work
2. ✅ **API Compatibility** - Same endpoints and responses
3. ✅ **UI Consistency** - Same user interface and experience
4. ✅ **Performance** - Comparable or better performance
5. ✅ **Reliability** - Proper error handling and logging
6. ✅ **Documentation** - Complete API docs and examples

## Next Steps

After successful testing:

1. **Deploy to staging environment**
2. **Load testing with multiple files**
3. **AWS Textract integration testing**
4. **Performance benchmarking**
5. **Production deployment**
