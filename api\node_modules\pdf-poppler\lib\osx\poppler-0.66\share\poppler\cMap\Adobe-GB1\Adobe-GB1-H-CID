%!PS-Adobe-3.0 Resource-CMap
%%DocumentNeededResources: ProcSet (CIDInit)
%%IncludeResource: ProcSet (CIDInit)
%%BeginResource: CMap (Adobe-GB1-H-CID)
%%Title: (Adobe-GB1-H-CID Adobe GB1 0)
%%Version: 2.004
%%Copyright: -----------------------------------------------------------
%%Copyright: Copyright 1990-2015 Adobe Systems Incorporated.
%%Copyright: All rights reserved.
%%Copyright:
%%Copyright: Redistribution and use in source and binary forms, with or
%%Copyright: without modification, are permitted provided that the
%%Copyright: following conditions are met:
%%Copyright:
%%Copyright: Redistributions of source code must retain the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer.
%%Copyright:
%%Copyright: Redistributions in binary form must reproduce the above
%%Copyright: copyright notice, this list of conditions and the following
%%Copyright: disclaimer in the documentation and/or other materials
%%Copyright: provided with the distribution. 
%%Copyright:
%%Copyright: Neither the name of Adobe Systems Incorporated nor the names
%%Copyright: of its contributors may be used to endorse or promote
%%Copyright: products derived from this software without specific prior
%%Copyright: written permission. 
%%Copyright:
%%Copyright: THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND
%%Copyright: CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
%%Copyright: INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
%%Copyright: MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
%%Copyright: DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
%%Copyright: CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
%%Copyright: SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT
%%Copyright: NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
%%Copyright: LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
%%Copyright: HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
%%Copyright: CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
%%Copyright: OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
%%Copyright: SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
%%Copyright: -----------------------------------------------------------
%%EndComments

/CIDInit /ProcSet findresource begin

12 dict begin
		
begincmap

/CIDSystemInfo 3 dict dup begin
  /Registry (Adobe) def
  /Ordering (Adobe_GB1_H_CID) def
  /Supplement 0 def
end def

/CMapName /Adobe-GB1-H-CID def
/CMapVersion 2.004 def
/CMapType 1 def

/UIDOffset 0 def

/WMode 0 def

1 begincodespacerange
  <0000>   <FFFF>
endcodespacerange

% Map CID 0 to the full width space glyph. And all other glyphs to the same CID in font 0.
0 usefont
2 begincidrange
<0000> <FFFF> 0
<0000> <0000> 96
endcidrange

% Map CIDs 1 thru 95 to Ascii codes 0x20 - 0x7F in fontID 1.
1 usefont
1 beginbfrange
<0001> <005F> <20>
endbfrange

% Map CIDs 7717 - 9896 to fontID 2 for Supplement 1
2 usefont
4 begincidrange
<1E25> <1EFF> 7717
<1F00> <1FFF> 7936
<2000> <25FF> 8192
<2600> <26A8> 9728
endcidrange

% Map CIDs 9897 - 22126 to fontID 3 for Supplement 2
3 usefont
3 begincidrange
<26A9> <26FF>  9897
<2700> <55FF>  9984 
<5600> <566E>  22016
endcidrange

% Map CIDs 22127 - 22352 to fontID 4 for Supplement 3
% Also map CIDS upto 65535 to this fontID for any future supplements.
4 usefont
2 begincidrange
<566F> <56FF>  22127
<5700> <FFFF>  22272
endcidrange


endcmap
CMapName currentdict /CMap defineresource pop
end
end

%%EndResource
%%EOF
