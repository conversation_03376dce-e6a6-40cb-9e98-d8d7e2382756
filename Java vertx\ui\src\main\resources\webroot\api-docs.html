<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Documentation - AWS Textract Document Scanner</title>
    <link rel="stylesheet" href="api-docs.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1>📚 API Documentation</h1>
                <p>AWS Textract Legal Document Scanner API - Java Vert.x</p>
                <div class="header-actions">
                    <a href="index.html" class="btn btn-secondary">← Back to App</a>
                    <span class="api-status" id="apiStatus">Checking API...</span>
                </div>
            </div>
        </header>

        <nav class="sidebar">
            <ul class="nav-menu">
                <li><a href="#overview">Overview</a></li>
                <li><a href="#authentication">Authentication</a></li>
                <li><a href="#endpoints">Endpoints</a></li>
                <li><a href="#upload">Document Upload</a></li>
                <li><a href="#grouped-data">Grouped Data</a></li>
                <li><a href="#health">Health Check</a></li>
                <li><a href="#response-formats">Response Formats</a></li>
                <li><a href="#error-handling">Error Handling</a></li>
                <li><a href="#examples">Code Examples</a></li>
                <li><a href="#testing">API Testing</a></li>
            </ul>
        </nav>

        <main class="content">
            <section id="overview" class="section">
                <h2>🔍 Overview</h2>
                <div class="info-card">
                    <p>The AWS Textract Document Scanner API provides powerful document processing capabilities using AWS Textract OCR technology. Extract text, identify document types, and organize data from legal documents like Aadhaar cards, PAN cards, passports, and GST certificates.</p>
                    
                    <div class="feature-grid">
                        <div class="feature-item">
                            <h4>🆔 Document Types</h4>
                            <p>AADHAAR, PAN, PASSPORT, DRIVING_LICENSE, VOTER_ID, GST_CERTIFICATE</p>
                        </div>
                        <div class="feature-item">
                            <h4>📄 Supported Formats</h4>
                            <p>JPG, JPEG, PNG, PDF</p>
                        </div>
                        <div class="feature-item">
                            <h4>🔄 Processing</h4>
                            <p>Batch upload, automatic grouping, field extraction</p>
                        </div>
                        <div class="feature-item">
                            <h4>📊 Output</h4>
                            <p>JSON format, grouped by person, structured data</p>
                        </div>
                    </div>
                </div>
            </section>

            <section id="authentication" class="section">
                <h2>🔐 Authentication</h2>
                <div class="info-card">
                    <p>Currently, the API does not require authentication. All endpoints are publicly accessible.</p>
                    <div class="warning-box">
                        <strong>⚠️ Note:</strong> In production environments, implement proper authentication and rate limiting.
                    </div>
                </div>
            </section>

            <section id="endpoints" class="section">
                <h2>🛠️ API Endpoints</h2>
                <div class="endpoint-summary">
                    <div class="endpoint-card">
                        <span class="method post">POST</span>
                        <span class="path">/upload</span>
                        <span class="description">Upload and process documents</span>
                    </div>
                    <div class="endpoint-card">
                        <span class="method get">GET</span>
                        <span class="path">/api/grouped-by-person</span>
                        <span class="description">Get processed documents grouped by person</span>
                    </div>
                    <div class="endpoint-card">
                        <span class="method post">POST</span>
                        <span class="path">/api/process-personal-documents</span>
                        <span class="description">Process personal documents with grouping</span>
                    </div>
                    <div class="endpoint-card">
                        <span class="method get">GET</span>
                        <span class="path">/health</span>
                        <span class="description">API health check</span>
                    </div>
                </div>
            </section>

            <section id="upload" class="section">
                <h2>📤 Document Upload</h2>
                <div class="endpoint-detail">
                    <div class="endpoint-header">
                        <span class="method post">POST</span>
                        <span class="path">/upload</span>
                    </div>
                    
                    <h4>Description</h4>
                    <p>Upload one or more documents for OCR processing and text extraction.</p>
                    
                    <h4>Request</h4>
                    <div class="code-block">
                        <pre><code class="language-http">POST http://localhost:3005/upload
Content-Type: multipart/form-data

files: [File1, File2, ...]</code></pre>
                    </div>
                    
                    <h4>Parameters</h4>
                    <table class="params-table">
                        <thead>
                            <tr>
                                <th>Parameter</th>
                                <th>Type</th>
                                <th>Required</th>
                                <th>Description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>files</td>
                                <td>File[]</td>
                                <td>Yes</td>
                                <td>Array of document files (JPG, PNG, PDF)</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <h4>Response</h4>
                    <div class="code-block">
                        <pre><code class="language-json">{
  "success": true,
  "total_files_uploaded": 2,
  "successfully_processed": 2,
  "errors": [],
  "extracted_documents": [
    {
      "filename": "aadhaar.jpg",
      "FIRST_NAME": "John",
      "MIDDLE_NAME": "Kumar",
      "LAST_NAME": "Doe",
      "DOCUMENT_NUMBER": "1234 5678 9012",
      "DATE_OF_BIRTH": "01/01/1990",
      "ADDRESS": "123 Main Street",
      "GENDER": "MALE",
      "DOCUMENT_TYPE": "AADHAAR"
    }
  ],
  "processing_timestamp": "2024-01-15T10:30:00.000Z"
}</code></pre>
                    </div>
                </div>
            </section>

            <section id="grouped-data" class="section">
                <h2>👥 Grouped Data</h2>
                <div class="endpoint-detail">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="path">/api/grouped-by-person</span>
                    </div>
                    
                    <h4>Description</h4>
                    <p>Retrieve previously processed documents grouped by person/entity.</p>
                    
                    <h4>Request</h4>
                    <div class="code-block">
                        <pre><code class="language-http">GET http://localhost:3005/api/grouped-by-person</code></pre>
                    </div>
                    
                    <h4>Response</h4>
                    <div class="code-block">
                        <pre><code class="language-json">{
  "success": true,
  "total_files": 4,
  "processed_files": 4,
  "personal_documents": {
    "count": 4,
    "grouped_by_person": [
      {
        "group_id": 1,
        "person_name": "John Kumar Doe",
        "total_documents": 2,
        "documents": [
          {
            "filename": "john-aadhaar.jpg",
            "FIRST_NAME": "John",
            "MIDDLE_NAME": "Kumar",
            "LAST_NAME": "Doe",
            "DOCUMENT_NUMBER": "1234 5678 9012",
            "DATE_OF_BIRTH": "01/01/1990",
            "ADDRESS": "123 Main Street",
            "GENDER": "MALE",
            "DOCUMENT_TYPE": "AADHAAR"
          }
        ]
      }
    ]
  }
}</code></pre>
                    </div>
                </div>
            </section>

            <section id="health" class="section">
                <h2>❤️ Health Check</h2>
                <div class="endpoint-detail">
                    <div class="endpoint-header">
                        <span class="method get">GET</span>
                        <span class="path">/health</span>
                    </div>
                    
                    <h4>Description</h4>
                    <p>Check if the API service is running and healthy.</p>
                    
                    <h4>Request</h4>
                    <div class="code-block">
                        <pre><code class="language-http">GET http://localhost:3005/health</code></pre>
                    </div>
                    
                    <h4>Response</h4>
                    <div class="code-block">
                        <pre><code class="language-json">{
  "status": "OK",
  "message": "Document Extraction API is running"
}</code></pre>
                    </div>
                    
                    <div class="test-button-container">
                        <button id="testHealthBtn" class="btn btn-primary">🧪 Test Health Endpoint</button>
                        <div id="healthResult" class="test-result"></div>
                    </div>
                </div>
            </section>

            <section id="response-formats" class="section">
                <h2>📋 Response Formats</h2>
                <div class="info-card">
                    <h4>Document Fields</h4>
                    <table class="params-table">
                        <thead>
                            <tr>
                                <th>Field</th>
                                <th>Type</th>
                                <th>Description</th>
                                <th>Example</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>FIRST_NAME</td>
                                <td>String</td>
                                <td>First name of the person</td>
                                <td>"John"</td>
                            </tr>
                            <tr>
                                <td>MIDDLE_NAME</td>
                                <td>String</td>
                                <td>Middle name of the person</td>
                                <td>"Kumar"</td>
                            </tr>
                            <tr>
                                <td>LAST_NAME</td>
                                <td>String</td>
                                <td>Last name of the person</td>
                                <td>"Doe"</td>
                            </tr>
                            <tr>
                                <td>DOCUMENT_NUMBER</td>
                                <td>String</td>
                                <td>Document identification number</td>
                                <td>"ABCDE1234F"</td>
                            </tr>
                            <tr>
                                <td>DATE_OF_BIRTH</td>
                                <td>String</td>
                                <td>Date of birth (DD/MM/YYYY)</td>
                                <td>"01/01/1990"</td>
                            </tr>
                            <tr>
                                <td>ADDRESS</td>
                                <td>String</td>
                                <td>Address information</td>
                                <td>"123 Main Street"</td>
                            </tr>
                            <tr>
                                <td>GENDER</td>
                                <td>String</td>
                                <td>Gender (MALE/FEMALE)</td>
                                <td>"MALE"</td>
                            </tr>
                            <tr>
                                <td>DOCUMENT_TYPE</td>
                                <td>String</td>
                                <td>Type of document</td>
                                <td>"AADHAAR"</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <section id="error-handling" class="section">
                <h2>⚠️ Error Handling</h2>
                <div class="info-card">
                    <h4>HTTP Status Codes</h4>
                    <ul>
                        <li><strong>200 OK</strong> - Request successful</li>
                        <li><strong>400 Bad Request</strong> - Invalid request format</li>
                        <li><strong>404 Not Found</strong> - Endpoint not found</li>
                        <li><strong>500 Internal Server Error</strong> - Server processing error</li>
                    </ul>

                    <h4>Error Response Format</h4>
                    <div class="code-block">
                        <pre><code class="language-json">{
  "success": false,
  "error": "Error message description",
  "details": "Additional error details if available"
}</code></pre>
                    </div>
                </div>
            </section>

            <section id="examples" class="section">
                <h2>💻 Code Examples</h2>

                <div class="info-card">
                    <h4>JavaScript/Fetch API</h4>
                    <div class="code-block">
                        <pre><code class="language-javascript">// Upload documents
const formData = new FormData();
formData.append('files', fileInput.files[0]);
formData.append('files', fileInput.files[1]);

const response = await fetch('http://localhost:3005/upload', {
    method: 'POST',
    body: formData
});

const result = await response.json();
console.log('Upload result:', result);

// Get grouped data
const groupedResponse = await fetch('http://localhost:3005/api/grouped-by-person');
const groupedData = await groupedResponse.json();
console.log('Grouped data:', groupedData);</code></pre>
                    </div>
                </div>

                <div class="info-card">
                    <h4>Python/Requests</h4>
                    <div class="code-block">
                        <pre><code class="language-python">import requests

# Upload documents
files = {
    'files': ('document1.jpg', open('document1.jpg', 'rb')),
    'files': ('document2.jpg', open('document2.jpg', 'rb'))
}

response = requests.post('http://localhost:3005/upload', files=files)
result = response.json()
print('Upload result:', result)

# Get grouped data
grouped_response = requests.get('http://localhost:3005/api/grouped-by-person')
grouped_data = grouped_response.json()
print('Grouped data:', grouped_data)</code></pre>
                    </div>
                </div>

                <div class="info-card">
                    <h4>cURL</h4>
                    <div class="code-block">
                        <pre><code class="language-bash"># Upload documents
curl -X POST http://localhost:3005/upload \
  -F "files=@document1.jpg" \
  -F "files=@document2.jpg"

# Get grouped data
curl -X GET http://localhost:3005/api/grouped-by-person

# Health check
curl -X GET http://localhost:3005/health</code></pre>
                    </div>
                </div>
            </section>

            <section id="testing" class="section">
                <h2>🧪 API Testing</h2>
                <div class="info-card">
                    <h4>Testing Tools</h4>
                    <p>You can test the API using various tools:</p>
                    <ul>
                        <li><strong>Built-in Tester</strong> - Use the floating test panel (🧪 button)</li>
                        <li><strong>Postman</strong> - Import the API collection</li>
                        <li><strong>Insomnia</strong> - REST client for API testing</li>
                        <li><strong>Browser DevTools</strong> - Use the console for fetch requests</li>
                        <li><strong>cURL</strong> - Command line testing</li>
                    </ul>

                    <h4>Sample Test Data</h4>
                    <p>For testing purposes, you can use the mock data endpoint:</p>
                    <div class="code-block">
                        <pre><code class="language-http">GET http://localhost:3005/api/grouped-by-person</code></pre>
                    </div>

                    <h4>Rate Limiting</h4>
                    <div class="warning-box">
                        <strong>⚠️ Note:</strong> Currently no rate limiting is implemented. In production, implement appropriate rate limiting to prevent abuse.
                    </div>

                    <h4>CORS Configuration</h4>
                    <p>The API is configured to accept requests from any origin for development purposes. In production, configure CORS to only allow trusted domains.</p>
                </div>
            </section>

            <section id="sdk" class="section">
                <h2>📦 SDK & Libraries</h2>
                <div class="info-card">
                    <h4>JavaScript SDK</h4>
                    <p>A simple JavaScript SDK for easier integration:</p>
                    <div class="code-block">
                        <pre><code class="language-javascript">class TextractAPI {
    constructor(baseUrl = 'http://localhost:3005') {
        this.baseUrl = baseUrl;
    }

    async uploadDocuments(files) {
        const formData = new FormData();
        files.forEach(file => formData.append('files', file));

        const response = await fetch(`${this.baseUrl}/upload`, {
            method: 'POST',
            body: formData
        });

        return response.json();
    }

    async getGroupedData() {
        const response = await fetch(`${this.baseUrl}/api/grouped-by-person`);
        return response.json();
    }

    async checkHealth() {
        const response = await fetch(`${this.baseUrl}/health`);
        return response.json();
    }
}

// Usage
const api = new TextractAPI();
const result = await api.uploadDocuments([file1, file2]);</code></pre>
                    </div>
                </div>
            </section>
        </main>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/plugins/autoloader/prism-autoloader.min.js"></script>
    <script src="api-docs.js"></script>
</body>
</html>
