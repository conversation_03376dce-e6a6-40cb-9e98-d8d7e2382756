# AWS Textract Document Extraction - Java Vert.x

A complete Java Vert.x implementation of the AWS Textract document extraction system with separate API and UI microservices.

## 🏗️ Architecture

This project consists of two separate Java Vert.x applications:

- **API Server** (`api/`) - Document processing service with AWS Textract integration
- **UI Server** (`ui/`) - Web interface and static file server

## 🚀 Quick Start

### Prerequisites

- Java 11 or higher
- Maven 3.6+
- AWS credentials configured (optional - falls back to mock data)

### 1. Start the API Server

```bash
cd api
mvn clean compile
mvn exec:java -Dexec.mainClass="io.vertx.core.Launcher" -Dexec.args="run com.textract.api.MainVerticle"
```

The API server will start on `http://localhost:8080`

### 2. Start the UI Server

```bash
cd ui
mvn clean compile
mvn exec:java -Dexec.mainClass="io.vertx.core.Launcher" -Dexec.args="run com.textract.ui.UIVerticle"
```

The UI server will start on `http://localhost:8081`

### 3. Access the Application

- **Web Interface**: http://localhost:8081
- **API Documentation**: http://localhost:8081/api-docs.html
- **API Health Check**: http://localhost:8080/health

## 📁 Project Structure

```
Java vertx/
├── api/                          # API Server
│   ├── src/main/java/com/textract/api/
│   │   ├── MainVerticle.java     # Main API server
│   │   ├── DocumentProcessor.java # Document processing logic
│   │   └── TextractService.java  # AWS Textract integration
│   ├── src/main/resources/
│   │   ├── application.json      # API configuration
│   │   └── logback.xml          # Logging configuration
│   └── pom.xml                  # Maven dependencies
├── ui/                          # UI Server
│   ├── src/main/java/com/textract/ui/
│   │   └── UIVerticle.java      # UI server and API proxy
│   ├── src/main/resources/
│   │   ├── webroot/             # Static web files
│   │   │   ├── index.html       # Main web interface
│   │   │   ├── script.js        # Frontend JavaScript
│   │   │   ├── styles.css       # CSS styles
│   │   │   └── api-docs.html    # API documentation
│   │   ├── application.json     # UI configuration
│   │   └── logback.xml         # Logging configuration
│   └── pom.xml                 # Maven dependencies
└── README.md                   # This file
```

## 🔧 Configuration

### API Server Configuration (`api/src/main/resources/application.json`)

```json
{
  "http": {
    "port": 8080,
    "host": "0.0.0.0"
  },
  "aws": {
    "region": "us-east-1",
    "textract": {
      "enabled": true,
      "timeout": 30000
    }
  }
}
```

### UI Server Configuration (`ui/src/main/resources/application.json`)

```json
{
  "http": {
    "port": 8081,
    "host": "0.0.0.0"
  },
  "api": {
    "host": "localhost",
    "port": 8080
  }
}
```

## 🛠️ Building and Running

### Build Fat JARs

```bash
# Build API server
cd api
mvn clean package

# Build UI server
cd ui
mvn clean package
```

### Run Fat JARs

```bash
# Run API server
java -jar api/target/document-extraction-api-1.0.0-fat.jar

# Run UI server
java -jar ui/target/document-extraction-ui-1.0.0-fat.jar
```

## 📋 API Endpoints

### API Server (Port 8080)

- `POST /upload` - Upload and process documents
- `GET /api/grouped-by-person` - Get processed documents grouped by person
- `POST /api/process-personal-documents` - Process personal documents
- `GET /health` - Health check
- `GET /api/info` - API information

### UI Server (Port 8081)

- `GET /` - Main web interface
- `GET /api-docs.html` - API documentation
- `GET /ui/health` - UI server health check
- `GET /ui/config` - UI configuration
- Proxy routes to API server for all `/api/*` and `/upload` requests

## 🔍 Features

### Document Processing
- **AWS Textract Integration** - Real OCR processing with AWS Textract
- **Mock Data Fallback** - Works without AWS credentials for testing
- **Multiple Document Types** - AADHAAR, PAN, Passport, Driving License, etc.
- **Pattern-Based Extraction** - Fallback text extraction using regex patterns
- **Field Mapping** - Automatic mapping to standard field formats

### Web Interface
- **Drag & Drop Upload** - Easy file upload interface
- **Multiple File Support** - Process multiple documents at once
- **Rich View** - Formatted display of extracted data
- **JSON View** - Raw JSON output with formatting
- **Export Options** - Download results in various formats
- **Responsive Design** - Works on desktop and mobile

### API Documentation
- **Interactive Documentation** - Built-in API testing interface
- **Code Examples** - JavaScript, Python, and cURL examples
- **Real-time Testing** - Test endpoints directly in the browser

## 🔒 Security

- **CORS Configuration** - Proper cross-origin resource sharing setup
- **Input Validation** - File type and size validation
- **Error Handling** - Comprehensive error handling and logging

## 🧪 Testing

### Test with Mock Data

The system includes mock data for testing without AWS credentials:

```bash
curl -X GET http://localhost:8080/api/grouped-by-person
```

### Test File Upload

```bash
curl -X POST http://localhost:8080/upload \
  -F "files=@document.jpg"
```

## 📊 Monitoring

### Health Checks

- API Server: `GET http://localhost:8080/health`
- UI Server: `GET http://localhost:8081/ui/health`

### Logs

- API logs: `api/logs/api.log`
- UI logs: `ui/logs/ui.log`

## 🔄 Development

### Hot Reload

Use the Vert.x Maven plugin for development with hot reload:

```bash
# API server with hot reload
cd api
mvn compile vertx:run

# UI server with hot reload
cd ui
mvn compile vertx:run
```

## 🚀 Deployment

### Docker (Optional)

Create Dockerfiles for containerized deployment:

```dockerfile
FROM openjdk:11-jre-slim
COPY target/*-fat.jar app.jar
EXPOSE 8080
CMD ["java", "-jar", "app.jar"]
```

### Production Configuration

- Configure proper AWS credentials
- Set up load balancing
- Configure SSL/TLS
- Set up monitoring and alerting

## 🤝 Comparison with Node.js Version

This Java Vert.x implementation provides:

- **Same Functionality** - Complete feature parity with Node.js version
- **Better Performance** - JVM optimizations and Vert.x event loop
- **Type Safety** - Java's static typing for better reliability
- **Enterprise Ready** - Robust error handling and logging
- **Scalability** - Vert.x's reactive architecture

## 📝 License

This project is part of the AWS Textract Document Extraction system.
