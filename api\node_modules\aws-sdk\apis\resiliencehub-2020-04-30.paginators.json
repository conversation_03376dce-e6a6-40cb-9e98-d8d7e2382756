{"pagination": {"ListAlarmRecommendations": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListAppAssessmentComplianceDrifts": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListAppAssessmentResourceDrifts": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "resourceDrifts"}, "ListAppAssessments": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListAppComponentCompliances": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListAppComponentRecommendations": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListAppInputSources": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListAppVersionAppComponents": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListAppVersionResourceMappings": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListAppVersionResources": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListAppVersions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListApps": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListRecommendationTemplates": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListResiliencyPolicies": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListResourceGroupingRecommendations": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "groupingRecommendations"}, "ListSopRecommendations": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListSuggestedResiliencyPolicies": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListTestRecommendations": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListUnsupportedAppVersionResources": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}}}