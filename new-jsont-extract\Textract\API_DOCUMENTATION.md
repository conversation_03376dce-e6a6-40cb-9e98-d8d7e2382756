# Document Processing API Documentation

## Overview
This API provides document processing capabilities for Aadhaar/PAN cards and GST certificates using AWS Textract. It includes proper data mapping and grouping functionality.

## Base URL
```
http://localhost:3004
```

## API Endpoints

### 1. Process Personal Documents (Aadhaar/PAN)
**Endpoint:** `POST /api/process-personal-documents`

**Description:** Processes Aadhaar and PAN documents with name-based grouping

**Request:**
- Method: POST
- Content-Type: multipart/form-data
- Body: Form data with file uploads

**Parameters:**
- `documents` (file, multiple): Aadhaar or PAN document files (JPG, JPEG, PNG, PDF)

**Response Format:**
```json
{
  "success": true,
  "document_type": "personal_documents",
  "total_files_uploaded": 2,
  "successfully_processed": 2,
  "errors": [],
  "grouped_by_person": [
    {
      "group_id": 1,
      "person_name": "JOHN DOE SMITH",
      "total_documents": 2,
      "documents": [
        {
          "filename": "aadhaar.pdf",
          "document_type": "Aadhaar Card",
          "name": "JOHN DOE SMITH",
          "first_name": "JOH<PERSON>",
          "middle_name": "DOE",
          "last_name": "SMITH",
          "date_of_birth": "15/06/1990",
          "gender": "MALE",
          "document_number": "1234 5678 9012",
          "address": "123 Main Street, City, State",
          "upload_time": "2025-01-04T06:47:55.000Z"
        },
        {
          "filename": "pan.pdf",
          "document_type": "PAN Card",
          "name": "JOHN DOE SMITH",
          "first_name": "JOHN",
          "middle_name": "DOE",
          "last_name": "SMITH",
          "date_of_birth": "15/06/1990",
          "gender": "MALE",
          "document_number": "**********",
          "address": "123 Main Street, City, State",
          "upload_time": "2025-01-04T06:47:55.000Z"
        }
      ]
    }
  ],
  "processing_timestamp": "2025-01-04T06:47:55.000Z"
}
```

### 2. Process GST Documents
**Endpoint:** `POST /api/process-gst-documents`

**Description:** Processes GST certificates with company name-based grouping

**Request:**
- Method: POST
- Content-Type: multipart/form-data
- Body: Form data with file uploads

**Parameters:**
- `documents` (file, multiple): GST certificate files (JPG, JPEG, PNG, PDF)

**Response Format:**
```json
{
  "success": true,
  "document_type": "gst_certificates",
  "total_files_uploaded": 2,
  "successfully_processed": 2,
  "errors": [],
  "grouped_by_company": [
    {
      "group_id": 1,
      "company_name": "ABC PRIVATE LIMITED",
      "total_documents": 2,
      "documents": [
        {
          "filename": "gst_cert_1.pdf",
          "document_type": "GST Certificate",
          "gstin": "27**********1Z5",
          "legal_name": "ABC PRIVATE LIMITED",
          "trade_name": "ABC TRADING",
          "registration_date": "01/04/2017",
          "constitution": "Private Limited Company",
          "address": "123 Business Park, Mumbai, Maharashtra",
          "state": "MAHARASHTRA",
          "status": "ACTIVE",
          "upload_time": "2025-01-04T06:47:55.000Z"
        }
      ]
    }
  ],
  "processing_timestamp": "2025-01-04T06:47:55.000Z"
}
```

### 3. General Upload (UI Compatible)
**Endpoint:** `POST /upload`

**Description:** General upload endpoint that handles both personal and GST documents

**Request:**
- Method: POST
- Content-Type: multipart/form-data
- Body: Form data with file uploads

**Parameters:**
- `documents` (file, multiple): Document files (JPG, JPEG, PNG, PDF)

**Response Format:**
```json
{
  "success": true,
  "total_files": 3,
  "processed_files": 3,
  "personal_documents": {
    "count": 2,
    "grouped_by_person": [
      {
        "group_id": 1,
        "person_name": "JOHN DOE SMITH",
        "total_documents": 2,
        "documents": [...] // Same format as personal documents API
      }
    ]
  },
  "gst_documents": {
    "count": 1,
    "grouped_by_company": [
      {
        "group_id": 1,
        "company_name": "ABC PRIVATE LIMITED",
        "total_documents": 1,
        "documents": [...] // Same format as GST documents API
      }
    ]
  },
  "all_extracted_data": [...] // Raw extracted data for all documents
}
```

## Postman Testing Instructions

### Test 1: Personal Documents API
1. **Create New Request**
   - Method: POST
   - URL: `http://localhost:3004/api/process-personal-documents`

2. **Setup Request**
   - Go to Body tab
   - Select "form-data"
   - Add key: `documents` (change type to File)
   - Upload multiple Aadhaar/PAN PDF files

3. **Expected Result**
   - Documents with same name should be grouped together
   - Each group shows person name and document count
   - Individual documents show extracted fields

### Test 2: GST Documents API
1. **Create New Request**
   - Method: POST
   - URL: `http://localhost:3004/api/process-gst-documents`

2. **Setup Request**
   - Go to Body tab
   - Select "form-data"
   - Add key: `documents` (change type to File)
   - Upload multiple GST certificate PDF files

3. **Expected Result**
   - Documents with same company name should be grouped together
   - Each group shows company name and document count
   - Individual documents show GST-specific fields

### Test 3: Mixed Documents (General Upload)
1. **Create New Request**
   - Method: POST
   - URL: `http://localhost:3004/upload`

2. **Setup Request**
   - Go to Body tab
   - Select "form-data"
   - Add key: `documents` (change type to File)
   - Upload mix of Aadhaar, PAN, and GST documents

3. **Expected Result**
   - Response separates personal and GST documents
   - Each type is grouped appropriately
   - Raw data is also provided

## Error Handling

### Common Error Responses
```json
{
  "success": false,
  "error": "No files uploaded",
  "message": "Please upload at least one document"
}
```

```json
{
  "success": false,
  "error": "Internal server error",
  "message": "Detailed error message"
}
```

### Document Type Validation
- Personal documents API only accepts Aadhaar/PAN documents
- GST documents API only accepts GST certificates
- Invalid document types are reported in the errors array

## Field Mapping

### Personal Documents (Aadhaar/PAN)
- `FIRST_NAME`: First name of the person
- `MIDDLE_NAME`: Middle name (if available)
- `LAST_NAME`: Last name/surname
- `DOCUMENT_NUMBER`: Aadhaar number (12 digits) or PAN number (10 characters)
- `DATE_OF_BIRTH`: Date of birth in DD/MM/YYYY format
- `GENDER`: MALE/FEMALE
- `ADDRESS`: Full address
- `DOCUMENT_TYPE`: AADHAAR or PAN

### GST Documents
- `GSTIN`: 15-character GST identification number
- `LEGAL_NAME`: Legal name of the business
- `TRADE_NAME`: Trade name (if different from legal name)
- `REGISTRATION_DATE`: GST registration date
- `CONSTITUTION`: Business constitution type
- `ADDRESS`: Business address
- `STATE`: State of registration
- `STATUS`: Registration status (ACTIVE/CANCELLED/SUSPENDED)
- `DOCUMENT_TYPE`: GST_CERTIFICATE

## Grouping Logic

### Personal Documents
Documents are grouped by matching:
1. Full name (First + Middle + Last)
2. First and Last name combination
3. Case-insensitive matching with normalized spacing

### GST Documents
Documents are grouped by matching:
1. Legal name (exact match, case-insensitive)
2. Trade name fallback if legal name not available

## Notes
- Maximum file size: 10MB per file
- Maximum files per request: 20
- Supported formats: JPG, JPEG, PNG, PDF
- All uploaded files are automatically cleaned up after processing
- Server runs on port 3004
