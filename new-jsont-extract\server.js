const fs = require('fs');
const path = require('path');
const express = require('express');
const cors = require('cors');
const app = express();

app.use(cors());
const PORT = process.env.PORT || 3004;

function normalizeName(name) {
    return name ? name.replace(/[^a-zA-Z0-9 ]/g, '').trim().toLowerCase() : '';
}

function toTitleCase(str) {
    return str.replace(/\w\S*/g, function(txt){
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
}

function areNamesSimilar(nameA, nameB) {
    const a = normalizeName(nameA);
    const b = normalizeName(nameB);
    return a.includes(b) || b.includes(a);
}

function groupDocumentsByName(data) {
    const groups = [];
    function normalized(str) {
        return str ? str.replace(/[^a-zA-Z0-9 ]/g, '').replace(/\s+/g, ' ').trim().toLowerCase() : '';
    }

    data.forEach(item => {
        const filename = item.filename || '';
        const extracted = item.extracted_data || {};

        // Aadhaar
        if (Array.isArray(extracted.Aadhaar)) {
            extracted.Aadhaar.forEach(adhar => {
                const docName = adhar.name || '';
                if (!docName) return; // skip if name is missing
                const docObj = {
                    filename,
                    name: docName,
                    aadhaarNumber: adhar.aadhaarNumber || '',
                    dob: adhar.dob || '',
                    gender: adhar.gender || ''
                };
                let found = false;
                const normName = normalized(docName);
                for (const group of groups) {
                    if (normalized(group.name) === normName) {
                        group.documents.push(docObj);
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    groups.push({ name: toTitleCase(docName), documents: [docObj] });
                }
            });
        }
        // PAN
        if (Array.isArray(extracted.PAN)) {
            extracted.PAN.forEach(pan => {
                const docName = pan.name || '';
                if (!docName) return; // skip if name is missing
                const docObj = {
                    filename,
                    name: docName,
                    fatherName: pan.fatherName || '',
                    panNumber: pan.panNumber || '',
                    dob: pan.dob || ''
                };
                let found = false;
                const normName = normalized(docName);
                for (const group of groups) {
                    if (normalized(group.name) === normName) {
                        group.documents.push(docObj);
                        found = true;
                        break;
                    }
                }
                if (!found) {
                    groups.push({ name: toTitleCase(docName), documents: [docObj] });
                }
            });
        }
    });
    return groups;
}

// Example usage:
// const data = require('./Textract/extracted_results.json');
// const grouped = groupDocumentsByName(data);
// console.log(JSON.stringify(grouped, null, 2));


// API endpoint to get grouped documents by person
app.get('/api/grouped-by-person', (req, res) => {
    // Load extracted results
    const dataPath = path.join(__dirname, 'Textract', 'extracted_results.json');
    let fileData = {};
    try {
        fileData = JSON.parse(fs.readFileSync(dataPath, 'utf8'));
    } catch (err) {
        return res.status(500).json({ success: false, error: 'Could not read extracted_results.json' });
    }
    const extractedArray = fileData.all_extracted_data || [];
    const grouped = groupDocumentsByName(extractedArray);
    res.json({
        success: true,
        total_files: extractedArray.length,
        processed_files: extractedArray.length,
        personal_documents: {
            count: grouped.length,
            grouped_by_person: grouped
        }
    });
});

app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});