@echo off
echo Starting AWS Textract Document Extraction System (Java Vert.x)
echo =============================================================

echo Building both projects...

cd api
call mvn clean compile
if %ERRORLEVEL% NEQ 0 (
    echo API build failed!
    pause
    exit /b 1
)

cd ..\ui
call mvn clean compile
if %ERRORLEVEL% NEQ 0 (
    echo UI build failed!
    pause
    exit /b 1
)

cd ..

echo Starting API server in background...
start "API Server" cmd /k "cd api && mvn exec:java -Dexec.mainClass=\"io.vertx.core.Launcher\" -Dexec.args=\"run com.textract.api.MainVerticle\""

echo Waiting for API server to start...
timeout /t 10 /nobreak

echo Starting UI server...
start "UI Server" cmd /k "cd ui && mvn exec:java -Dexec.mainClass=\"io.vertx.core.Launcher\" -Dexec.args=\"run com.textract.ui.UIVerticle\""

echo.
echo ================================================================
echo Both servers are starting...
echo.
echo API Server: http://localhost:8080
echo UI Server:  http://localhost:8081
echo.
echo Web Interface: http://localhost:8081
echo API Documentation: http://localhost:8081/api-docs.html
echo ================================================================

pause
