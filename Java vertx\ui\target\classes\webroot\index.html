<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Extractor - Java Vert.x</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <div class="header-text">
                    <h1>📄 Document Data Extractor</h1>
                    <p>Java Vert.x - Upload multiple documents to extract and analyze data</p>
                </div>
                <div class="header-actions">
                    <a href="api-docs.html" class="btn btn-outline" target="_blank">📚 API Docs</a>
                    <span class="tech-badge">☕ Java Vert.x</span>
                </div>
            </div>
        </header>

        <div class="upload-section">
            <div class="upload-area" id="uploadArea">
                <div class="upload-icon">📁</div>
                <h3>Drop files here or click to browse</h3>
                <p>Supports: JPG, JPEG, PNG, PDF</p>
                <input type="file" id="fileInput" multiple accept=".jpg,.jpeg,.png,.pdf" style="display: none;">
                <button class="browse-btn" onclick="document.getElementById('fileInput').click()">
                    Browse Files
                </button>
            </div>
            
            <div class="file-list" id="fileList"></div>
            
            <div class="upload-controls">
                <button class="upload-btn" id="uploadBtn" disabled>
                    <span class="btn-text">Upload & Extract</span>
                    <span class="loader" style="display: none;"></span>
                </button>
                <button class="clear-btn" id="clearBtn">Clear All</button>
            </div>
        </div>

        <div class="results-section" id="resultsSection" style="display: none;">
            <div class="results-header">
                <h2>📄 Extraction Results</h2>
                <div class="view-toggle">
                    <button id="richViewBtn" class="view-btn active">📊 Rich View</button>
                    <button id="jsonViewBtn" class="view-btn">📝 JSON View</button>
                </div>
            </div>

            <div class="results-container">
                <!-- Rich View -->
                <div id="richView" class="view-content active">
                    <div class="rich-controls">
                        <button id="exportRichBtn" class="export-btn">📤 Export Summary</button>
                        <button id="printRichBtn" class="print-btn">🖨️ Print</button>
                    </div>
                    <div id="richOutput" class="rich-output"></div>
                </div>

                <!-- JSON View -->
                <div id="jsonView" class="view-content">
                    <div class="json-controls">
                        <button id="copyJsonBtn" class="copy-btn">📋 Copy JSON</button>
                        <button id="formatJsonBtn" class="format-btn">🎨 Format JSON</button>
                        <button id="downloadJsonBtn" class="download-btn">💾 Download JSON</button>
                    </div>
                    <pre id="jsonOutput"></pre>
                </div>
            </div>
        </div>

        <div class="error-section" id="errorSection" style="display: none;">
            <div class="error-message" id="errorMessage"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
