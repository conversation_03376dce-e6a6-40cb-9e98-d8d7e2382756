const express = require('express');
const multer = require('multer');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const { TextractClient, AnalyzeDocumentCommand, DetectDocumentTextCommand } = require("@aws-sdk/client-textract");
const pdfParse = require('pdf-parse');
const Tesseract = require('tesseract.js');
const pdfPoppler = require('pdf-poppler');
const { S3Client } = require("@aws-sdk/client-s3");
require('dotenv').config(); // Load environment variables from .env file

const app = express();
const PORT = 3004;

// Configure Textract client with credentials
const textract = new TextractClient({
  region: process.env.AWS_REGION || "ap-south-1",
  credentials: {
    accessKeyId: "********************",
    secretAccessKey: "kzLpPpgc9nX0nOCGFf5xcBxlBz+PH/66Hyd0BmU8"
  }
});

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/';
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir);
    }
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + '-' + file.originalname);
  }
});

const upload = multer({ 
  storage: storage,
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB per file
    files: 20, // Allow up to 20 files
    fields: 30, // Increase field limit
    fieldSize: 2 * 1024 * 1024 // 2MB field size
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|pdf/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only images (JPEG, JPG, PNG) and PDF files are allowed!'));
    }
  }
});

// Textract response parser (from your existing code)
function mapToStandardJson(response) {
  // First detect document type
  const allText = response.Blocks
    .filter(block => block.BlockType === 'LINE')
    .map(block => block.Text)
    .join(' ').toUpperCase();

  let isGSTDocument = allText.includes('GSTIN') || 
                     allText.includes('GST') || 
                     allText.includes('GOODS AND SERVICES TAX') ||
                     allText.includes('REGISTRATION CERTIFICATE');

  let isPersonalDocument = allText.includes('AADHAAR') || 
                          allText.includes('PAN') ||
                          allText.includes('PERMANENT ACCOUNT NUMBER') ||
                          allText.includes('GOVERNMENT OF INDIA');

  // New: Detect company certificate (Certificate of Incorporation, etc.)
  let isCompanyCertificate = allText.includes('CORPORATE IDENTITY NUMBER') ||
                            allText.includes('CERTIFICATE OF INCORPORATION') ||
                            allText.includes('COMPANY IS') ||
                            allText.includes('MINISTRY OF CORPORATE AFFAIRS') ||
                            allText.includes('CORPORATE REGISTRAR');

  // Initialize fields based on document type
  let fields = {};
  
  if (isGSTDocument) {
    fields = {
      GSTIN: "",
      LEGAL_NAME: "",
      TRADE_NAME: "",
      REGISTRATION_DATE: "",
      CONSTITUTION: "",
      ADDRESS: "",
      STATE: "",
      STATUS: "",
      DOCUMENT_TYPE: "GST_CERTIFICATE"
    };
  } else if (isCompanyCertificate) {
    fields = {
      COMPANY_NAME: "",
      CIN: "",
      PAN: "",
      TAN: "",
      ADDRESS: "",
      DOCUMENT_TYPE: "COMPANY_CERTIFICATE"
    };
  } else {
    fields = {
      FIRST_NAME: "",
      MIDDLE_NAME: "",
      LAST_NAME: "",
      DOCUMENT_NUMBER: "",
      DATE_OF_BIRTH: "",
      ADDRESS: "",
      GENDER: "",
      DOCUMENT_TYPE: ""
    };
  }

  if (!response.Blocks) return fields;

  const blockMap = {};
  const keyMap = {};
  const valueMap = {};

  response.Blocks.forEach((block) => {
    blockMap[block.Id] = block;
    if (block.BlockType === "KEY_VALUE_SET") {
      if (block.EntityTypes && block.EntityTypes.includes("KEY")) {
        keyMap[block.Id] = block;
      } else {
        valueMap[block.Id] = block;
      }
    }
  });

  function getText(block, blockMap) {
    let text = "";
    if (block.Relationships) {
      block.Relationships.forEach((rel) => {
        if (rel.Type === "CHILD") {
          rel.Ids.forEach((cid) => {
            const child = blockMap[cid];
            if (child && child.Text) text += child.Text + " ";
          });
        }
      });
    }
    return text.trim();
  }

  function getValueBlock(keyBlock, valueMap) {
    if (keyBlock.Relationships) {
      for (const rel of keyBlock.Relationships) {
        if (rel.Type === "VALUE") {
          for (const valueId of rel.Ids) {
            return valueMap[valueId];
          }
        }
      }
    }
    return null;
  }

  // Extract key-value pairs based on document type
  Object.keys(keyMap).forEach((keyId) => {
    const keyBlock = keyMap[keyId];
    const valueBlock = getValueBlock(keyBlock, valueMap);
    
    if (keyBlock && valueBlock) {
      const key = getText(keyBlock, blockMap).toUpperCase();
      const value = getText(valueBlock, blockMap);
      
      console.log(`Key-Value pair: "${key}" -> "${value}"`);
      
      if (isGSTDocument) {
        // GST Certificate specific key matching
        if (key.match(/GSTIN|GST.*NUMBER|REGISTRATION.*NUMBER/)) {
          fields.GSTIN = value;
        } else if (key.match(/LEGAL.*NAME|BUSINESS.*NAME|NAME.*BUSINESS/)) {
          fields.LEGAL_NAME = value;
        } else if (key.match(/TRADE.*NAME|TRADING.*NAME/)) {
          fields.TRADE_NAME = value;
        } else if (key.match(/REGISTRATION.*DATE|DATE.*REGISTRATION|EFFECTIVE.*DATE/)) {
          fields.REGISTRATION_DATE = value;
        } else if (key.match(/CONSTITUTION|TYPE.*BUSINESS|BUSINESS.*TYPE/)) {
          fields.CONSTITUTION = value;
        } else if (key.match(/ADDRESS|PRINCIPAL.*PLACE/)) {
          fields.ADDRESS = value;
        } else if (key.match(/STATE|STATE.*CODE/)) {
          fields.STATE = value;
        } else if (key.match(/STATUS|REGISTRATION.*STATUS/)) {
          fields.STATUS = value;
        }
      } else if (isCompanyCertificate) {
        // Company certificate specific key matching
        if (key.match(/CORPORATE REGISTRAR|MINISTRY OF CORPORATE AFFAIRS/)) {
          fields.COMPANY_NAME = value;
        } else if (key.match(/CORPORATE IDENTITY NUMBER|CIN/)) {
          fields.CIN = value.replace(/\.$/, "");
        } else if (key.match(/PERMANENT ACCOUNT NUMBER|PAN/)) {
          fields.PAN = value;
        } else if (key.match(/TAX DEDUCTION AND COLLECTION ACCOUNT NUMBER|TAN/)) {
          fields.TAN = value;
        } else if (key.match(/ADDRESS|S NO\.|CTS NO\./)) {
          fields.ADDRESS += (fields.ADDRESS ? " " : "") + value;
        } else if (key.match(/COMPANY NAME|NAME OF COMPANY/)) {
          fields.COMPANY_NAME = value;
        }
      } else {
        // Personal document (Aadhaar/PAN) key matching
        if (key.match(/FIRST.*NAME|NAME.*FIRST/)) {
          fields.FIRST_NAME = value;
        } else if (key.match(/MIDDLE.*NAME|NAME.*MIDDLE/)) {
          fields.MIDDLE_NAME = value;
        } else if (key.match(/LAST.*NAME|NAME.*LAST|SURNAME/)) {
          fields.LAST_NAME = value;
        } else if (key.match(/DOCUMENT.*NUMBER|NUMBER.*DOCUMENT|AADHAAR.*NUMBER|PAN.*NUMBER|CARD.*NUMBER/)) {
          fields.DOCUMENT_NUMBER = value;
        } else if (key.match(/DATE.*BIRTH|BIRTH.*DATE|DOB|जन्म.*तारीख/)) {
          fields.DATE_OF_BIRTH = value;
        } else if (key.match(/ADDRESS|पता/)) {
          fields.ADDRESS = value;
        } else if (key.match(/GENDER|SEX|लिंग/)) {
          fields.GENDER = value.toUpperCase();
        }
      }
    }
  });

  // Debug: Log all detected keys
  console.log("=== DEBUG: All detected keys ===");
  Object.keys(keyMap).forEach((keyId) => {
    const keyBlock = keyMap[keyId];
    const key = getText(keyBlock, blockMap).toUpperCase();
    console.log(`Key found: "${key}"`);
  });

  Object.keys(keyMap).forEach((keyId) => {
    const keyBlock = keyMap[keyId];
    const key = getText(keyBlock, blockMap).toUpperCase();

    if (keyBlock.Relationships) {
      keyBlock.Relationships.forEach((rel) => {
        if (rel.Type === "VALUE") {
          rel.Ids.forEach((valueId) => {
            const valueBlock = valueMap[valueId];
            if (valueBlock) {
              const value = getText(valueBlock, blockMap).trim();
              console.log(`Key: "${key}" -> Value: "${value}"`);

              // Enhanced name matching - prioritize person's name over father's name
              if (key.includes("NAME") || key.includes("नाम") || 
                  key.match(/^(FIRST|LAST|MIDDLE|FULL)/) ||
                  key.includes("HOLDER") || key.includes("APPLICANT") ||
                  key.includes("714 / NAME") || key.match(/NAME$/)) {
                if (value && value.trim() && !value.match(/^\d+$/) && value.toUpperCase().trim() !== 'INDIA') {
                  // Prioritize main name fields over father's name
                  const isMainName = key.includes("714 / NAME") || 
                                   key === "NAME" || 
                                   !key.includes("FATHER");
                  
                  // Only update if we don't have a name yet, or if this is a main name field
                  if (!fields.FIRST_NAME || isMainName) {
                    const parts = value.split(/\s+/).filter(Boolean);
                    if (parts.length === 1) {
                      // Single name - treat as first name
                      fields.FIRST_NAME = parts[0];
                      fields.MIDDLE_NAME = "";
                      fields.LAST_NAME = "";
                    } else if (parts.length === 2) {
                      // Two names - first and last
                      fields.FIRST_NAME = parts[0];
                      fields.MIDDLE_NAME = "";
                      fields.LAST_NAME = parts[1];
                    } else if (parts.length >= 3) {
                      // Three or more names - first, middle(s), last
                      fields.FIRST_NAME = parts[0];
                      fields.MIDDLE_NAME = parts.slice(1, -1).join(" ");
                      fields.LAST_NAME = parts[parts.length - 1];
                    }
                    console.log(`Updated name from key \"${key}\": ${value} -> First: \"${fields.FIRST_NAME}\", Middle: \"${fields.MIDDLE_NAME}\", Last: \"${fields.LAST_NAME}\"`);
                  }
                }
              } 
              // Enhanced DOB matching - more specific patterns
              else if (key.includes("DOB") || key.includes("BIRTH") || key.includes("जन्म") ||
                       key.includes("DATE OF BIRTH") || key.includes("BORN") ||
                       key === "DATE:" || 
                       (key.includes("DATE") && value.match(/^\d{2}\/\d{2}\/\d{4}$/))) {
                if (value && value.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
                  fields.DATE_OF_BIRTH = value;
                }
              } 
              // Enhanced document number matching - specific patterns
              else if (key.includes("PERMANENT ACCOUNT NUMBER") || 
                       key.includes("ACCOUNT NUMBER") ||
                       key.includes("PAN") || key.includes("AADHAAR") || key.includes("AADHAR") ||
                       key.includes("CARD") || key.includes("ID") || key.includes("NUMBER") ||
                       value.match(/^\d{4}\s?\d{4}\s?\d{4}$/) || // Aadhaar pattern in value
                       value.match(/^[A-Z]{5}\d{4}[A-Z]$/)) { // PAN pattern in value
                if (value && value.trim()) {
                  fields.DOCUMENT_NUMBER = value;
                }
              } 
              // Enhanced address matching
              else if (key.includes("ADDRESS") || key.includes("पता") || 
                       key.includes("RESIDENCE") || key.includes("LOCATION") ||
                       key.includes("VILLAGE") || key.includes("CITY") || key.includes("STATE") ||
                       key.includes("PIN") || key.includes("POSTAL")) {
                if (fields.ADDRESS) {
                  fields.ADDRESS += " " + value;
                } else {
                  fields.ADDRESS = value;
                }
              }
              // Gender matching
              else if (key.includes("GENDER") || key.includes("SEX") || 
                       value.toUpperCase() === "MALE" || value.toUpperCase() === "FEMALE") {
                if (value.toUpperCase() === "MALE" || value.toUpperCase() === "FEMALE") {
                  fields.GENDER = value.toUpperCase();
                  console.log(`Found gender: ${value.toUpperCase()}`);
                }
              }
              // Fallback: if value looks like a name and no name found yet
              else if (!fields.FIRST_NAME && value.match(/^[A-Za-z\s]{2,50}$/) && 
                       !value.match(/\d/) && value.split(' ').length <= 4 &&
                       !key.includes("MALE") && !key.includes("FEMALE") && value.toUpperCase().trim() !== 'INDIA') {
                const parts = value.split(/\s+/).filter(Boolean);
                if (parts.length === 1) {
                  fields.FIRST_NAME = parts[0];
                  fields.MIDDLE_NAME = "";
                  fields.LAST_NAME = "";
                } else if (parts.length === 2) {
                  fields.FIRST_NAME = parts[0];
                  fields.MIDDLE_NAME = "";
                  fields.LAST_NAME = parts[1];
                } else if (parts.length >= 3) {
                  fields.FIRST_NAME = parts[0];
                  fields.MIDDLE_NAME = parts.slice(1, -1).join(" ");
                  fields.LAST_NAME = parts[parts.length - 1];
                }
              }
              // Fallback: if value looks like document number and no number found yet
              else if (!fields.DOCUMENT_NUMBER && 
                       (value.match(/^\d{4}\s?\d{4}\s?\d{4}$/) || value.match(/^[A-Z]{5}\d{4}[A-Z]$/))) {
                fields.DOCUMENT_NUMBER = value;
              }
            }
          });
        }
      });
    }
  });

  // Special handling based on document type
  if (isGSTDocument && (!fields.GSTIN || !fields.LEGAL_NAME)) {
    console.log("=== Trying alternative extraction for GST Certificate ===");
  } else if (!isGSTDocument && (!fields.FIRST_NAME || !fields.DOCUMENT_NUMBER)) {
    console.log("=== Trying alternative extraction for Personal Documents ===");
    
    // Collect all LINE text for better context-based extraction
    const allText = response.Blocks
      .filter(block => block.BlockType === 'LINE')
      .map(block => block.Text);
  
    console.log("All LINE texts:", allText);

    if (isGSTDocument) {
      // Look for GST specific patterns
      for (const text of allText) {
        // Check for GSTIN pattern (15 characters: 2 digits + 10 alphanumeric + 1 digit + 1 letter + 1 digit)
        const gstinMatch = text.match(/\b\d{2}[A-Z]{5}\d{4}[A-Z]\d[Z][A-Z\d]\b/);
        if (gstinMatch && !fields.GSTIN) {
          fields.GSTIN = gstinMatch[0];
          console.log(`Found GSTIN: ${gstinMatch[0]}`);
        }
        
        // Look for business names (avoid government terms)
        if (text.length > 5 && text.length < 100 && 
            !text.includes('GOVERNMENT') && !text.includes('INDIA') &&
            !text.includes('CERTIFICATE') && !text.includes('REGISTRATION') &&
            !fields.LEGAL_NAME) {
          const namePattern = /^[A-Z][A-Za-z\s&.,()]+$/;
          if (namePattern.test(text)) {
            fields.LEGAL_NAME = text;
            console.log(`Found Legal Name: ${text}`);
          }
        }
        
        // Extract registration date patterns
        let dateMatch = text.match(/(\d{2}\/\d{2}\/\d{4})/);
        if (dateMatch && !fields.REGISTRATION_DATE) {
          fields.REGISTRATION_DATE = dateMatch[1];
          console.log(`Found Registration Date: ${dateMatch[1]}`);
        }
        
        // Extract state information
        if (text.includes('STATE') && !fields.STATE) {
          const stateMatch = text.match(/STATE[:\s]*([A-Z\s]+)/);
          if (stateMatch) {
            fields.STATE = stateMatch[1].trim();
            console.log(`Found State: ${fields.STATE}`);
          }
        }
        
        // Extract status
        if ((text.includes('ACTIVE') || text.includes('CANCELLED') || text.includes('SUSPENDED')) && !fields.STATUS) {
          if (text.includes('ACTIVE')) {
            fields.STATUS = "ACTIVE";
          } else if (text.includes('CANCELLED')) {
            fields.STATUS = "CANCELLED";
          } else if (text.includes('SUSPENDED')) {
            fields.STATUS = "SUSPENDED";
          }
          console.log(`Found Status: ${fields.STATUS}`);
        }
      }
    } else {
      // Look for personal document patterns (Aadhaar/PAN)
      for (const text of allText) {
        // Check for Aadhaar number pattern (12 digits)
        const aadhaarMatch = text.match(/\b\d{4}\s?\d{4}\s?\d{4}\b/);
        if (aadhaarMatch && !fields.DOCUMENT_NUMBER && !text.includes('VID:')) {
          fields.DOCUMENT_NUMBER = aadhaarMatch[0];
          fields.DOCUMENT_TYPE = "AADHAAR";
          console.log(`Found Aadhaar number: ${aadhaarMatch[0]}`);
        }
        
        // Check for PAN number pattern (5 letters + 4 digits + 1 letter)
        const panMatch = text.match(/\b[A-Z]{5}\d{4}[A-Z]\b/);
        if (panMatch && !fields.DOCUMENT_NUMBER) {
          fields.DOCUMENT_NUMBER = panMatch[0];
          fields.DOCUMENT_TYPE = "PAN";
          console.log(`Found PAN number: ${panMatch[0]}`);
        }
        
        // Extract DOB patterns - enhanced
        let dobMatch = text.match(/DOB:\s*(\d{2}\/\d{2}\/\d{4})/);
        if (dobMatch && !fields.DATE_OF_BIRTH) {
          fields.DATE_OF_BIRTH = dobMatch[1];
          console.log(`Found DOB: ${dobMatch[1]}`);
        }
        
        // DOB with "and" pattern like "JOH and / DOB : 01/07/1974"
        if (!fields.DATE_OF_BIRTH) {
          dobMatch = text.match(/DOB\s*:\s*(\d{2}\/\d{2}\/\d{4})/);
          if (dobMatch) {
            fields.DATE_OF_BIRTH = dobMatch[1];
            console.log(`Found DOB: ${dobMatch[1]}`);
          }
        }
        
        // Standalone date format
        if (!fields.DATE_OF_BIRTH) {
          dobMatch = text.match(/^\d{2}\/\d{2}\/\d{4}$/);
          if (dobMatch) {
            fields.DATE_OF_BIRTH = dobMatch[0];
            console.log(`Found DOB: ${dobMatch[0]}`);
          }
        }
        
        // Extract gender - enhanced patterns
        if (!fields.GENDER) {
          if (text.includes('Male') || text.includes('MALE')) {
            fields.GENDER = "MALE";
            console.log(`Found gender: MALE from text: "${text}"`);
          } else if (text.includes('Female') || text.includes('FEMALE')) {
            fields.GENDER = "FEMALE";
            console.log(`Found gender: FEMALE from text: "${text}"`);
          }
        }
        
        // Extract person name - look for patterns like "Prakash Gopichand Rathod"
        if (!fields.FIRST_NAME && text.match(/^[A-Z][a-z]+ [A-Z][a-z]+ [A-Z][a-z]+$/)) {
          // This looks like a proper person name (3 words, proper case)
          const parts = text.split(/\s+/).filter(Boolean);
          fields.FIRST_NAME = parts[0];
          fields.MIDDLE_NAME = parts[1];
          fields.LAST_NAME = parts[2];
          console.log(`Found person name: "${text}" -> First: "${fields.FIRST_NAME}", Middle: "${fields.MIDDLE_NAME}", Last: "${fields.LAST_NAME}"`);
        } else if (!fields.FIRST_NAME && text.match(/^[A-Z][a-z]+ [A-Z][a-z]+$/)) {
          // This looks like a proper person name (2 words, proper case)
          const parts = text.split(/\s+/).filter(Boolean);
          fields.FIRST_NAME = parts[0];
          fields.MIDDLE_NAME = "";
          fields.LAST_NAME = parts[1];
          console.log(`Found person name: "${text}" -> First: "${fields.FIRST_NAME}", Last: "${fields.LAST_NAME}"`);
        }
      }
      
      // Smart name extraction for personal documents
      const namesCandidates = [];
      for (const text of allText) {
        const nameMatch = text.match(/^[A-Za-z\s]{3,50}$/);
        if (nameMatch && 
            !text.toUpperCase().includes('GOVERNMENT') && 
            !text.toUpperCase().includes('INDIA') && 
            !text.toUpperCase().includes('AADHAAR') &&
            !text.toUpperCase().includes('MALE') && 
            !text.toUpperCase().includes('FEMALE') &&
            !text.toUpperCase().includes('AUTHORITY') &&
            !text.toUpperCase().includes('IDENTIFICATION') &&
            !text.toUpperCase().includes('UNIQUE') &&
            !text.toUpperCase().includes('CERTIFICATE') &&
            !text.toUpperCase().includes('REGISTRATION') &&
            !text.toUpperCase().includes('DEPARTMENT') &&
            !text.toUpperCase().includes('MINISTRY') &&
            !text.toUpperCase().includes('OFFICE') &&
            !text.toUpperCase().includes('BUREAU') &&
            !text.toUpperCase().includes('COMMISSION') &&
            text.length < 30 && // Limit length to avoid organization names
            text.split(' ').length <= 4) { // Limit to reasonable name length
          const words = text.trim().split(/\s+/);
          // Prioritize names that look like person names (2-3 words, proper case)
          let score = words.length * 5;
          
          // Boost score for likely person names
          if (words.length >= 2 && words.length <= 3) score += 10;
          if (text.match(/^[A-Z][a-z]+ [A-Z][a-z]+/)) score += 15; // Proper case names
          
          namesCandidates.push({ name: text.trim(), score, words: words.length });
        }
      }
      
      // Select best name candidate
      if (namesCandidates.length > 0 && !fields.FIRST_NAME) {
        const bestName = namesCandidates.sort((a, b) => b.score - a.score)[0];
        const parts = bestName.name.split(/\s+/).filter(Boolean);
        if (parts.length === 1) {
          fields.FIRST_NAME = parts[0];
          fields.MIDDLE_NAME = "";
          fields.LAST_NAME = "";
        } else if (parts.length === 2) {
          fields.FIRST_NAME = parts[0];
          fields.MIDDLE_NAME = "";
          fields.LAST_NAME = parts[1];
        } else if (parts.length >= 3) {
          fields.FIRST_NAME = parts[0];
          fields.MIDDLE_NAME = parts.slice(1, -1).join(" ");
          fields.LAST_NAME = parts[parts.length - 1];
        }
        console.log(`Selected name: "${bestName.name}" -> First: "${fields.FIRST_NAME}", Middle: "${fields.MIDDLE_NAME}", Last: "${fields.LAST_NAME}"`);
      }
    }
  }

  // Special handling for company certificate
  if (isCompanyCertificate) {
    // Try to extract company name from LINE blocks if not found
    if (!fields.COMPANY_NAME) {
      const companyLine = response.Blocks
        .filter(block => block.BlockType === 'LINE' && block.Text.match(/PRIVATE LIMITED|LIMITED|LTD|PVT LTD|LLP|INCORPORATED/i))
        .map(block => block.Text)
        .find(Boolean);
      if (companyLine) fields.COMPANY_NAME = companyLine;
    }
    // Try to extract CIN from LINE blocks if not found
    if (!fields.CIN) {
      const cinLine = response.Blocks
        .filter(block => block.BlockType === 'LINE' && block.Text.match(/U\d{5}[A-Z]{2}\d{4}(PLC|PTC)\d{6}/i))
        .map(block => block.Text.match(/U\d{5}[A-Z]{2}\d{4}(PLC|PTC)\d{6}/i))
        .find(Boolean);
      if (cinLine) fields.CIN = cinLine[0];
    }
    // Try to extract PAN from LINE blocks if not found
    if (!fields.PAN) {
      const panLine = response.Blocks
        .filter(block => block.BlockType === 'LINE' && block.Text.match(/\b[A-Z]{5}\d{4}[A-Z]\b/))
        .map(block => block.Text.match(/\b[A-Z]{5}\d{4}[A-Z]\b/))
        .find(Boolean);
      if (panLine) fields.PAN = panLine[0];
    }
    // Try to extract TAN from LINE blocks if not found
    if (!fields.TAN) {
      const tanLine = response.Blocks
        .filter(block => block.BlockType === 'LINE' && block.Text.match(/\b[A-Z]{4}\d{5}[A-Z]\b/))
        .map(block => block.Text.match(/\b[A-Z]{4}\d{5}[A-Z]\b/))
        .find(Boolean);
      if (tanLine) fields.TAN = tanLine[0];
    }
  }

  console.log("=== Final extracted fields ===");
  console.log(JSON.stringify(fields, null, 2));

  // Aadhaar/PAN detection (improved): check for PAN pattern and Aadhaar pattern
  const panPattern = /^[A-Z]{5}\d{4}[A-Z]$/;
  const aadhaarPattern = /^\d{4}\s?\d{4}\s?\d{4}$/;
  const gstinPattern = /\b\d{2}[A-Z]{5}\d{4}[A-Z]\d[Z][A-Z\d]\b/;
  const bankKeywords = ["ACCOUNT HOLDER", "ACCOUNT NUMBER", "IFSC", "BANK", "BRANCH"];
  const invoiceKeywords = ["INVOICE", "AMOUNT", "INVOICE NUMBER", "BILL TO", "TOTAL"];

  // Extracted fields (case-insensitive mapping)
  let result = {
    PAN: [],
    Aadhaar: [],
    GST: [],
    Bank: [],
    Invoice: []
  };

  // Helper: Normalize field names
  function norm(str) {
    return str ? str.replace(/[^a-zA-Z0-9]/g, '').toLowerCase() : '';
  }

  // Helper: Extract key-value pairs from Textract blocks
  function extractKv(blocks) {
    const kv = {};
    blocks.forEach(block => {
      if (block.BlockType === 'KEY_VALUE_SET' && block.EntityTypes && block.EntityTypes.includes('KEY')) {
        let key = "";
        if (block.Relationships) {
          block.Relationships.forEach(rel => {
            if (rel.Type === "CHILD") {
              rel.Ids.forEach(cid => {
                const child = blocks.find(b => b.Id === cid);
                if (child && child.Text) key += child.Text + " ";
              });
            }
          });
        }
        key = key.trim();
        let value = "";
        if (block.Relationships) {
          block.Relationships.forEach(rel => {
            if (rel.Type === "VALUE") {
              rel.Ids.forEach(vid => {
                const valueBlock = blocks.find(b => b.Id === vid);
                if (valueBlock && valueBlock.Relationships) {
                  valueBlock.Relationships.forEach(vrel => {
                    if (vrel.Type === "CHILD") {
                      vrel.Ids.forEach(vcid => {
                        const vchild = blocks.find(b => b.Id === vcid);
                        if (vchild && vchild.Text) value += vchild.Text + " ";
                      });
                    }
                  });
                }
                if (valueBlock && valueBlock.Text) value += valueBlock.Text + " ";
              });
            }
          });
        }
        value = value.trim();
        kv[norm(key)] = value;
      }
    });
    return kv;
  }

  // Extract key-value pairs
  const kv = extractKv(response.Blocks);

  // Cheque detection (add this block before PAN/Aadhaar/GST/Bank mapping)
  // Detect cheque by presence of typical cheque keys
  const chequeKeys = [
    "AUTHORISED SIGNATORIES",
    "RS.",
    "PAY",
    "RUPEES",
    "DATE",
    "A/C NO.",
    "RTGS / NEFT IFSC: :",
    "HDFC BANK LTD.",
    "WEEKLY HOLIDAY ON SUNDAY",
    "PAY TO THE",
    "ORDER OF",
    "MEMO",
    "FIRST BANK OF WIKI",
    "DOLLARS",
    "SECURITY FEATURES",
    "VICTORIA MAIN BRANCH"
  ];
  const foundChequeKey = Object.keys(kv).some(k =>
    chequeKeys.some(ck => k.includes(ck.replace(/[^a-zA-Z0-9]/g, '').toLowerCase()))
  );
  if (foundChequeKey) {
    // Map cheque fields
    const cheque = {
      payeeName: kv["pay"] || kv["paytothe"] || kv["orderof"] || "",
      amount: kv["rs"] || kv["rs."] || kv["$"] || "",
      amountInWords: kv["rupees"] || kv["dollars"] || "",
      chequeDate: kv["date"] || "",
      accountNumber: kv["acno"] || kv["a/cno"] || "",
      bankBranch: kv["hdfcbankltd"] || kv["bankbranch"] || "",
      memo: kv["memo"] || "",
      signatory: kv["authorisedsignatories"] || "",
      extraInfo: kv["weeklyholidayonsunday"] || kv["extrainfo"] || ""
    };
    // Add to result.Cheque array
    result.Cheque = [cheque];
  }

  // PAN detection
  let panNumber = "";
  Object.keys(kv).forEach(k => {
    if (panPattern.test(kv[k])) panNumber = kv[kv[k]];
    if (k.includes("pan") && panPattern.test(kv[k])) panNumber = kv[k];
  });
  if (!panNumber) {
    // Try lines
    response.Blocks.filter(b => b.BlockType === 'LINE').forEach(b => {
      if (panPattern.test(b.Text)) panNumber = b.Text;
    });
  }
  if (panNumber) {
    let name = "", fatherName = "", dob = "";
    Object.keys(kv).forEach(k => {
      if (k.includes("name") && !k.includes("father")) name = kv[k];
      if (k.includes("father")) fatherName = kv[k];
      if (k.includes("dob") || k.includes("birth")) dob = kv[k];
    });
    result.PAN.push({
      name: name,
      fatherName: fatherName,
      panNumber: panNumber,
      dob: dob
    });
  }

  // Aadhaar detection (improved mapping)
  let aadhaarNumber = "";
  Object.keys(kv).forEach(k => {
    if (aadhaarPattern.test(kv[k])) aadhaarNumber = kv[k];
    if (k.includes("aadhaar") && aadhaarPattern.test(kv[k])) aadhaarNumber = kv[k];
  });
  if (!aadhaarNumber) {
    response.Blocks.filter(b => b.BlockType === 'LINE').forEach(b => {
      if (aadhaarPattern.test(b.Text)) aadhaarNumber = b.Text;
    });
  }
  if (aadhaarNumber) {
    // Try to get name from fields if available
    let name = "";
    if (fields.FIRST_NAME || fields.LAST_NAME) {
      name = [fields.FIRST_NAME, fields.MIDDLE_NAME, fields.LAST_NAME].filter(Boolean).join(" ");
    } else {
      Object.keys(kv).forEach(k => {
        if (k.includes("name")) name = kv[k];
      });
    }
    let dob = fields.DATE_OF_BIRTH || "";
    if (!dob) {
      Object.keys(kv).forEach(k => {
        if (k.includes("dob") || k.includes("birth")) dob = kv[k];
      });
    }
    result.Aadhaar.push({
      name: name,
      aadhaarNumber: aadhaarNumber,
      dob: dob,
      gender: fields.GENDER || ""
    });
  }

  // GST detection
  let gstin = "";
  Object.keys(kv).forEach(k => {
    if (gstinPattern.test(kv[k])) gstin = kv[k];
    if (k.includes("gstin") && gstinPattern.test(kv[k])) gstin = kv[k];
  });
  if (!gstin) {
    response.Blocks.filter(b => b.BlockType === 'LINE').forEach(b => {
      const m = b.Text.match(gstinPattern);
      if (m) gstin = m[0];
    });
  }
  if (gstin) {
    let companyName = "", invoiceNumber = "", amount = "";
    Object.keys(kv).forEach(k => {
      if (k.includes("legalname") || k.includes("companyname")) companyName = kv[k];
      if (k.includes("invoice")) invoiceNumber = kv[k];
      if (k.includes("amount")) amount = kv[k];
    });
    result.GST.push({
      companyName: companyName,
      gstin: gstin,
      invoiceNumber: invoiceNumber,
      amount: amount
    });
  }

  // Bank detection
  let accountHolder = "", accountNumber = "", ifsc = "";
  Object.keys(kv).forEach(k => {
    if (k.includes("accountholder")) accountHolder = kv[k];
    if (k.includes("accountnumber")) accountNumber = kv[k];
    if (k.includes("ifsc")) ifsc = kv[k];
  });
  if (accountNumber || ifsc) {
    // Transactions: try to extract from lines
    const transactions = [];
    response.Blocks.filter(b => b.BlockType === 'LINE').forEach(b => {
      if (b.Text.match(/^\d{4}-\d{2}-\d{2}/) || b.Text.match(/^\d{2}\/\d{2}\/\d{4}/)) {
        // crude transaction line
        const parts = b.Text.split(/\s+/);
        if (parts.length >= 3) {
          transactions.push({
            date: parts[0],
            desc: parts.slice(1, -1).join(" "),
            amount: parts[parts.length - 1]
          });
        }
      }
    });
    result.Bank.push({
      accountHolder: accountHolder,
      accountNumber: accountNumber,
      ifsc: ifsc,
      transactions: transactions
    });
  }

  // Invoice detection
  let invoiceNumber = "", invoiceAmount = "", billTo = "";
  Object.keys(kv).forEach(k => {
    if (k.includes("invoice") && k.includes("number")) invoiceNumber = kv[k];
    if (k.includes("amount")) invoiceAmount = kv[k];
    if (k.includes("billto")) billTo = kv[k];
  });
  if (invoiceNumber || invoiceAmount) {
    result.Invoice.push({
      invoiceNumber: invoiceNumber,
      amount: invoiceAmount,
      billTo: billTo
    });
  }

  // Remove empty arrays
  Object.keys(result).forEach(k => {
    if (Array.isArray(result[k]) && result[k].length === 0) delete result[k];
  });

  return result;
}

// Process document with AWS Textract
async function processWithTextract(filePath) {
  try {
    console.log(`Starting Textract processing for: ${filePath}`);
    
    // Read the file
    const imageBytes = fs.readFileSync(filePath);
    
    // Try FORMS feature first (best for structured documents)
    let params = {
      Document: {
        Bytes: imageBytes
      },
      FeatureTypes: ['FORMS']
    };

    try {
      console.log('Trying Textract with FORMS feature...');
      const result = await textract.send(new AnalyzeDocumentCommand(params));
      console.log(`Textract FORMS processing successful. Found ${result.Blocks.length} blocks`);
      return mapToStandardJson(result);
    } catch (formsError) {
      console.log('FORMS feature failed, trying TABLES feature...', formsError.message);
      
      // Try TABLES feature
      params.FeatureTypes = ['TABLES'];
      try {
        const result = await textract.send(new AnalyzeDocumentCommand(params));
        console.log(`Textract TABLES processing successful. Found ${result.Blocks.length} blocks`);
        return mapToStandardJson(result);
      } catch (tablesError) {
        console.log('TABLES feature failed, trying basic text detection...', tablesError.message);
        
        // Try basic text detection
        const basicParams = {
          Document: {
            Bytes: imageBytes
          }
        };
        
        try {
          const result = await textract.send(new DetectDocumentTextCommand(basicParams));
          console.log(`Textract basic text detection successful. Found ${result.Blocks.length} blocks`);
          return mapToStandardJson(result);
        } catch (basicError) {
          console.error('All Textract methods failed:', basicError.message);

          const ext = path.extname(filePath).toLowerCase();
          if (ext === '.pdf') {
            try {
              const dataBuffer = fs.readFileSync(filePath);
              const pdfData = await pdfParse(dataBuffer);
              if (!pdfData.text || pdfData.text.trim().length === 0) {
                // Fallback: convert first page to image and OCR
                const ocrText = await pdfToImageAndOcr(filePath);
                const mappedFields = mapRawTextToFields(ocrText);
                mappedFields.RAW_TEXT = ocrText;
                mappedFields.ERROR = "Textract and pdf-parse failed, used PDF-to-image OCR fallback";
                return mappedFields;
              }
              // Map pdf-parse text as well
              const mappedFields = mapRawTextToFields(pdfData.text);
              mappedFields.RAW_TEXT = pdfData.text;
              mappedFields.ERROR = "Textract failed, used pdf-parse fallback";
              return mappedFields;
            } catch (pdfError) {
              // Try OCR fallback for image files
              const imageExts = ['.png', '.jpg', '.jpeg'];
              if (imageExts.includes(ext)) {
                try {
                  const { data: { text } } = await Tesseract.recognize(filePath, 'eng');
                  return {
                    FIRST_NAME: "",
                    MIDDLE_NAME: "",
                    LAST_NAME: "",
                    DOCUMENT_NUMBER: "",
                    DATE_OF_BIRTH: "",
                    ADDRESS: "",
                    GENDER: "",
                    DOCUMENT_TYPE: "OCR_IMAGE_FALLBACK",
                    RAW_TEXT: text,
                    ERROR: "Textract and pdf-parse failed, used tesseract.js OCR fallback"
                  };
                } catch (ocrError) {
                  return {
                    FIRST_NAME: "",
                    MIDDLE_NAME: "",
                    LAST_NAME: "",
                    DOCUMENT_NUMBER: "",
                    DATE_OF_BIRTH: "",
                    ADDRESS: "",
                    GENDER: "",
                    DOCUMENT_TYPE: "ERROR",
                    ERROR: `Textract, pdf-parse, and OCR failed: ${ocrError.message}`
                  };
                }
              }
              return {
                FIRST_NAME: "",
                MIDDLE_NAME: "",
                LAST_NAME: "",
                DOCUMENT_NUMBER: "",
                DATE_OF_BIRTH: "",
                ADDRESS: "",
                GENDER: "",
                DOCUMENT_TYPE: "ERROR",
                ERROR: `Textract and pdf-parse failed: ${pdfError.message}`
              };
            }
          }

          // OCR fallback for image files
          const imageExts = ['.png', '.jpg', '.jpeg'];
          if (imageExts.includes(ext)) {
            try {
              const { data: { text } } = await Tesseract.recognize(filePath, 'eng');
              return {
                FIRST_NAME: "",
                MIDDLE_NAME: "",
                LAST_NAME: "",
                DOCUMENT_NUMBER: "",
                DATE_OF_BIRTH: "",
                ADDRESS: "",
                GENDER: "",
                DOCUMENT_TYPE: "OCR_IMAGE_FALLBACK",
                RAW_TEXT: text,
                ERROR: "Textract failed, used tesseract.js OCR fallback"
              };
            } catch (ocrError) {
              return {
                FIRST_NAME: "",
                MIDDLE_NAME: "",
                LAST_NAME: "",
                DOCUMENT_NUMBER: "",
                DATE_OF_BIRTH: "",
                ADDRESS: "",
                GENDER: "",
                DOCUMENT_TYPE: "ERROR",
                ERROR: `Textract and OCR failed: ${ocrError.message}`
              };
            }
          }

          // Add user-friendly error for unsupported formats
          let errorMsg = basicError.message;
          if (errorMsg && errorMsg.includes('unsupported document format')) {
            errorMsg = 'The uploaded file format is not supported by AWS Textract. Please use a valid image or PDF document.';
          }
          return {
            FIRST_NAME: "",
            MIDDLE_NAME: "",
            LAST_NAME: "",
            DOCUMENT_NUMBER: "",
            DATE_OF_BIRTH: "",
            ADDRESS: "",
            GENDER: "",
            DOCUMENT_TYPE: "UNKNOWN",
            ERROR: `Textract processing failed: ${errorMsg}`
          };
        }
      }
    }
  } catch (error) {
    console.error('Error in processWithTextract:', error);
    return {
      FIRST_NAME: "",
      MIDDLE_NAME: "",
      LAST_NAME: "",
      DOCUMENT_NUMBER: "",
      DATE_OF_BIRTH: "",
      ADDRESS: "",
      GENDER: "",
      DOCUMENT_TYPE: "ERROR",
      ERROR: `Processing failed: ${error.message}`
    };
  }
}

// Convert PDF to image and perform OCR on the first page
async function pdfToImageAndOcr(pdfPath) {
  const outputDir = path.join(__dirname, 'temp_pdf_images');
  if (!fs.existsSync(outputDir)) fs.mkdirSync(outputDir);

  const opts = {
    format: 'jpeg',
    out_dir: outputDir,
    out_prefix: path.basename(pdfPath, path.extname(pdfPath)),
    page: 1 // Only first page for speed
  };

  try {
    await pdfPoppler.convert(pdfPath, opts);
    const imagePath = path.join(outputDir, `${opts.out_prefix}-1.jpg`);
    if (fs.existsSync(imagePath)) {
      const { data: { text } } = await Tesseract.recognize(imagePath, 'eng');
      // Optionally, clean up image after OCR
      fs.unlinkSync(imagePath);
      return text;
    }
    return '';
  } catch (err) {
    return '';
  }
}

// Grouping logic for personal documents (Aadhaar/PAN) - Case insensitive matching
function getFullName(data) {
  return [data.FIRST_NAME, data.MIDDLE_NAME, data.LAST_NAME].filter(Boolean).join(" ").toUpperCase().trim();
}

function getFirstLast(data) {
  return [data.FIRST_NAME, data.LAST_NAME].filter(Boolean).join(" ").toUpperCase().trim();
}

// Get company name for GST documents
function getCompanyName(data) {
  return (data.LEGAL_NAME || data.TRADE_NAME || "").toUpperCase().trim();
}

// Normalize names for better matching (handle case variations)
function normalizeName(name) {
  if (!name) return "";
  return name.toUpperCase().trim().replace(/\s+/g, " ");
}

// Enhanced name normalization for better matching
function normalizeNameAdvanced(name) {
  if (!name) return "";
  return name.toUpperCase()
    .trim()
    .replace(/\s+/g, " ")
    .replace(/[^A-Z\s]/g, "") // Remove special characters
    .trim();
}

// Check if two names are similar (handles character variations)
function areNamesSimilar(name1, name2) {
  if (!name1 || !name2) return false;
  
  const norm1 = normalizeNameAdvanced(name1);
  const norm2 = normalizeNameAdvanced(name2);
  
  // Exact match
  if (norm1 === norm2) return true;
  
  // Split into words and compare
  const words1 = norm1.split(" ").filter(w => w.length > 0);
  const words2 = norm2.split(" ").filter(w => w.length > 0);
  
  // If both have at least 2 words, check if first and last match
  if (words1.length >= 2 && words2.length >= 2) {
    const firstMatch = words1[0] === words2[0];
    const lastMatch = words1[words1.length - 1] === words2[words2.length - 1];
    return firstMatch && lastMatch;
  }
  
  return false;
}

// Group personal documents by person name with enhanced matching
function groupPersonalDocuments(docs) {
  const groups = [];
  const used = new Set();

  for (let i = 0; i < docs.length; i++) {
    if (used.has(i)) continue;

    const group = [docs[i]];
    used.add(i);

    const data1 = docs[i].extracted_data;
    const fullName1 = getFullName(data1);
    const shortName1 = getFirstLast(data1);

    for (let j = i + 1; j < docs.length; j++) {
      if (used.has(j)) continue;

      const data2 = docs[j].extracted_data;
      const fullName2 = getFullName(data2);
      const shortName2 = getFirstLast(data2);

      // Multiple matching strategies
      let isMatch = false;
      
      // Strategy 1: Full name similarity
      if (areNamesSimilar(fullName1, fullName2)) {
        isMatch = true;
        console.log(`Full name match: "${fullName1}" ≈ "${fullName2}"`);
      }
      
      // Strategy 2: First + Last name similarity
      if (!isMatch && areNamesSimilar(shortName1, shortName2)) {
        isMatch = true;
        console.log(`Short name match: "${shortName1}" ≈ "${shortName2}"`);
      }
      
      // Strategy 3: Individual field matching (case-insensitive)
      if (!isMatch && data1.FIRST_NAME && data1.LAST_NAME && data2.FIRST_NAME && data2.LAST_NAME) {
        const firstMatch = normalizeNameAdvanced(data1.FIRST_NAME) === normalizeNameAdvanced(data2.FIRST_NAME);
        const lastMatch = normalizeNameAdvanced(data1.LAST_NAME) === normalizeNameAdvanced(data2.LAST_NAME);
        
        if (firstMatch && lastMatch) {
          isMatch = true;
          console.log(`Field match: "${data1.FIRST_NAME} ${data1.LAST_NAME}" ≈ "${data2.FIRST_NAME} ${data2.LAST_NAME}"`);
        }
      }

      if (isMatch) {
        group.push(docs[j]);
        used.add(j);
        console.log(`Grouped personal documents: "${docs[i].filename}" and "${docs[j].filename}"`);
      }
    }

    groups.push(group);
  }

  return groups;
}

// Group GST documents by company name with enhanced matching
function groupGSTDocuments(docs) {
  const groups = [];
  const used = new Set();

  for (let i = 0; i < docs.length; i++) {
    if (used.has(i)) continue;

    const group = [docs[i]];
    used.add(i);

    const data1 = docs[i].extracted_data;
    const company1 = getCompanyName(data1);

    for (let j = i + 1; j < docs.length; j++) {
      if (used.has(j)) continue;

      const data2 = docs[j].extracted_data;
      const company2 = getCompanyName(data2);

      // Enhanced company name matching
      let isMatch = false;
      
      // Strategy 1: Direct company name match
      if (areNamesSimilar(company1, company2)) {
        isMatch = true;
        console.log(`Company name match: "${company1}" ≈ "${company2}"`);
      }
      
      // Strategy 2: GSTIN match (more reliable)
      if (!isMatch && data1.GSTIN && data2.GSTIN && data1.GSTIN === data2.GSTIN) {
        isMatch = true;
        console.log(`GSTIN match: "${data1.GSTIN}"`);
      }
      
      // Strategy 3: Legal name vs Trade name cross-matching
      if (!isMatch) {
        const legal1 = normalizeNameAdvanced(data1.LEGAL_NAME || "");
        const trade1 = normalizeNameAdvanced(data1.TRADE_NAME || "");
        const legal2 = normalizeNameAdvanced(data2.LEGAL_NAME || "");
        const trade2 = normalizeNameAdvanced(data2.TRADE_NAME || "");
        
        if ((legal1 && legal2 && legal1 === legal2) ||
            (trade1 && trade2 && trade1 === trade2) ||
            (legal1 && trade2 && legal1 === trade2) ||
            (trade1 && legal2 && trade1 === legal2)) {
          isMatch = true;
          console.log(`Cross legal/trade name match`);
        }
      }

      if (isMatch) {
        group.push(docs[j]);
        used.add(j);
        console.log(`Grouped GST documents: "${docs[i].filename}" and "${docs[j].filename}"`);
      }
    }

    groups.push(group);
  }

  return groups;
}

// Create grouped JSON structure by person name for personal documents
function createGroupedJsonByPerson(docs) {
  const groups = groupPersonalDocuments(docs);
  const result = [];

  groups.forEach((group, index) => {
    if (group.length > 0) {
      const personData = group[0].extracted_data;
      const personName = getFullName(personData) || 
                        getFirstLast(personData) || 
                        `${personData.FIRST_NAME || 'Unknown'} ${personData.LAST_NAME || ''}`.trim() ||
                        'Unknown Person';

      const groupData = {
        group_id: index + 1,
        person_name: personName,
        total_documents: group.length,
        documents: group.map(doc => ({
          filename: doc.filename,
          document_type: doc.extracted_data.DOCUMENT_TYPE === 'AADHAAR' ? 'Aadhaar Card' :
                        doc.extracted_data.DOCUMENT_TYPE === 'PAN' ? 'PAN Card' :
                        doc.extracted_data.DOCUMENT_TYPE || 'Unknown',
          name: getFullName(doc.extracted_data) || getFirstLast(doc.extracted_data) || 'Unknown',
          first_name: doc.extracted_data.FIRST_NAME || 'Unknown',
          middle_name: doc.extracted_data.MIDDLE_NAME || '',
          last_name: doc.extracted_data.LAST_NAME || 'Unknown',
          date_of_birth: doc.extracted_data.DATE_OF_BIRTH || 'Unknown',
          gender: doc.extracted_data.GENDER || 'Unknown',
          document_number: doc.extracted_data.DOCUMENT_NUMBER || 'Unknown',
          address: doc.extracted_data.ADDRESS || 'Unknown',
          upload_time: doc.upload_time
        }))
      };
      
      result.push(groupData);
    }
  });

  return result;
}

// Create grouped JSON structure by company name for GST documents
function createGroupedJsonByCompany(docs) {
  const groups = groupGSTDocuments(docs);
  const result = [];

  groups.forEach((group, index) => {
    if (group.length > 0) {
      const companyData = group[0].extracted_data;
      const companyName = getCompanyName(companyData) || 'Unknown Company';

      const groupData = {
        group_id: index + 1,
        company_name: companyName,
        total_documents: group.length,
        documents: group.map(doc => ({
          filename: doc.filename,
          document_type: 'GST Certificate',
          gstin: doc.extracted_data.GSTIN || 'Unknown',
          legal_name: doc.extracted_data.LEGAL_NAME || 'Unknown',
          trade_name: doc.extracted_data.TRADE_NAME || '',
          registration_date: doc.extracted_data.REGISTRATION_DATE || 'Unknown',
          constitution: doc.extracted_data.CONSTITUTION || 'Unknown',
          address: doc.extracted_data.ADDRESS || 'Unknown',
          state: doc.extracted_data.STATE || 'Unknown',
          status: doc.extracted_data.STATUS || 'Unknown',
          upload_time: doc.upload_time
        }))
      };
      
      result.push(groupData);
    }
  });

  return result;
}

// Refactored function to process a single uploaded file with Textract
async function processUploadedFile(file) {
  const fileBytes = fs.readFileSync(file.path);

  // Validate file size and format for Textract
  const maxSize = 5 * 1024 * 1024; // 5MB limit for Textract
  if (fileBytes.length > maxSize) {
    throw new Error(`File size ${(fileBytes.length / 1024 / 1024).toFixed(2)}MB exceeds Textract limit of 5MB`);
  }

  // Check if it's a valid PDF by looking at file header
  const isPDF = file.mimetype === 'application/pdf' || file.originalname.toLowerCase().endsWith('.pdf');
  if (isPDF) {
    const pdfHeader = fileBytes.slice(0, 4).toString();
    if (!pdfHeader.startsWith('%PDF')) {
      throw new Error('Invalid PDF file format - missing PDF header');
    }
    const pdfContent = fileBytes.toString('binary');
    if (pdfContent.includes('/Encrypt')) {
      throw new Error('PDF file appears to be password-protected or encrypted');
    }
  }

  let response;
  let lastError;

  try {
    // Method 1: Try FORMS analysis first
    console.log(`Attempting FORMS analysis for ${file.originalname}`);
    response = await textract.send(new AnalyzeDocumentCommand({
      Document: { Bytes: fileBytes },
      FeatureTypes: ["FORMS"],
    }));
  } catch (formsError) {
    lastError = formsError;
    console.log(`FORMS analysis failed for ${file.originalname}:`, formsError.message);
    try {
      // Method 2: Try TABLES analysis
      console.log(`Attempting TABLES analysis for ${file.originalname}`);
      response = await textract.send(new AnalyzeDocumentCommand({
        Document: { Bytes: fileBytes },
        FeatureTypes: ["TABLES"],
      }));
    } catch (tablesError) {
      lastError = tablesError;
      console.log(`TABLES analysis failed for ${file.originalname}:`, tablesError.message);
      // Method 3: Fallback to basic text detection
      console.log(`Attempting basic text detection for ${file.originalname}`);
      response = await textract.send(new DetectDocumentTextCommand({ Document: { Bytes: fileBytes } }));
    }
  }

  return mapToStandardJson(response);
}

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// API endpoint for Aadhaar/PAN document processing with name-based grouping
app.post('/api/process-personal-documents', upload.array('documents', 20), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ 
        success: false,
        error: 'No files uploaded',
        message: 'Please upload at least one Aadhaar or PAN document'
      });
    }

    const extractedDocs = [];
    const errors = [];

    for (const file of req.files) {
      try {
        const fileBytes = fs.readFileSync(file.path);

        // Validate file size and format for Textract
        const maxSize = 5 * 1024 * 1024; // 5MB limit for Textract
        if (fileBytes.length > maxSize) {
          throw new Error(`File size ${(fileBytes.length / 1024 / 1024).toFixed(2)}MB exceeds Textract limit of 5MB`);
        }

        // Check if it's a valid PDF by looking at file header
        const isPDF = file.mimetype === 'application/pdf' || file.originalname.toLowerCase().endsWith('.pdf');
        const isImage = ['image/jpeg', 'image/jpg', 'image/png'].includes(file.mimetype);
        
        if (isPDF) {
          // Validate PDF header
          const pdfHeader = fileBytes.slice(0, 4).toString();
          if (!pdfHeader.startsWith('%PDF')) {
            throw new Error('Invalid PDF file format - missing PDF header');
          }
          
          // Check for common PDF issues
          const pdfContent = fileBytes.toString('binary');
          if (pdfContent.includes('/Encrypt')) {
            throw new Error('PDF file appears to be password-protected or encrypted');
          }
          
          // Log PDF version for debugging
          const versionMatch = pdfContent.match(/%PDF-(\d\.\d)/);
          if (versionMatch) {
            console.log(`PDF version: ${versionMatch[1]} for ${file.originalname}`);
          }
        }

        // Try different Textract methods with proper error handling
        let response;
        let lastError;
        
        try {
          // Method 1: Try FORMS analysis first
          console.log(`Attempting FORMS analysis for ${file.originalname} (${file.mimetype}, ${(fileBytes.length / 1024).toFixed(1)}KB)`);
          response = await textract.send(new AnalyzeDocumentCommand({
            Document: { Bytes: fileBytes },
            FeatureTypes: ["FORMS"],
          }));
          console.log(`FORMS analysis successful for ${file.originalname}`);
        } catch (formsError) {
          lastError = formsError;
          console.log(`FORMS analysis failed for ${file.originalname}:`, formsError.code, formsError.message);
          
          try {
            // Method 2: Try TABLES analysis
            console.log(`Attempting TABLES analysis for ${file.originalname}`);
            response = await textract.send(new AnalyzeDocumentCommand({
              Document: { Bytes: fileBytes },
              FeatureTypes: ["TABLES"],
            }));
            console.log(`TABLES analysis successful for ${file.originalname}`);
          } catch (tablesError) {
            lastError = tablesError;
            console.log(`TABLES analysis failed for ${file.originalname}:`, tablesError.code, tablesError.message);
            
            try {
              // Method 3: Try basic text detection
              console.log(`Attempting basic text detection for ${file.originalname}`);
              response = await textract.send(new DetectDocumentTextCommand({
                Document: { Bytes: fileBytes }
              }));
              console.log(`Basic text detection successful for ${file.originalname}`);
            } catch (basicError) {
              lastError = basicError;
              console.log(`Basic text detection failed for ${file.originalname}:`, basicError.code, basicError.message);
              
              // If all methods fail, throw the last error with additional context
              throw new Error(`All Textract methods failed for ${file.originalname}. File type: ${file.mimetype}, Size: ${(fileBytes.length / 1024).toFixed(1)}KB. Last error: ${lastError.message}`);
            }
          }
        }

        const extracted = mapToStandardJson(response);
        
        // Validate if it's a personal document (Aadhaar/PAN)
        if (!extracted.DOCUMENT_TYPE || (extracted.DOCUMENT_TYPE !== 'AADHAAR' && extracted.DOCUMENT_TYPE !== 'PAN')) {
          errors.push({
            filename: file.originalname,
            error: 'Document type not recognized as Aadhaar or PAN'
          });
        } else {
          extractedDocs.push({
            filename: file.originalname,
            extracted_data: extracted,
            upload_time: new Date().toISOString()
          });
        }

        // Clean up uploaded file
        try {
          fs.unlinkSync(file.path);
        } catch (unlinkError) {
          console.warn(`Failed to delete file ${file.path}:`, unlinkError.message);
        }
      } catch (fileError) {
        console.error(`Error processing file ${file.originalname}:`, fileError);
        errors.push({
          filename: file.originalname,
          error: `Failed to process: ${fileError.message}`
        });
      }
    }

    const groupedByPerson = createGroupedJsonByPerson(extractedDocs);
    
    res.json({
      success: true,
      document_type: 'personal_documents',
      total_files_uploaded: req.files.length,
      successfully_processed: extractedDocs.length,
      errors: errors,
      grouped_by_person: groupedByPerson,
      processing_timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Personal documents processing error:', error);
    res.status(500).json({ 
      success: false,
      error: 'Internal server error', 
      message: error.message 
    });
  }
});

// API endpoint for GST certificate processing with company name-based grouping
app.post('/api/process-gst-documents', upload.array('documents', 20), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ 
        success: false,
        error: 'No files uploaded',
        message: 'Please upload at least one GST certificate'
      });
    }

    const extractedDocs = [];
    const errors = [];

    for (const file of req.files) {
      try {
        const fileBytes = fs.readFileSync(file.path);
        // Remove this line:
        // const extracted = await processUploadedFile(file);

        // Validate file size and format for Textract
        const maxSize = 5 * 1024 * 1024; // 5MB limit for Textract
        if (fileBytes.length > maxSize) {
          throw new Error(`File size ${(fileBytes.length / 1024 / 1024).toFixed(2)}MB exceeds Textract limit of 5MB`);
        }

        // Check if it's a valid PDF by looking at file header
        const isPDF = file.mimetype === 'application/pdf' || file.originalname.toLowerCase().endsWith('.pdf');
        const isImage = ['image/jpeg', 'image/jpg', 'image/png'].includes(file.mimetype);
        
        if (isPDF) {
          // Validate PDF header
          const pdfHeader = fileBytes.slice(0, 4).toString();
          if (!pdfHeader.startsWith('%PDF')) {
            throw new Error('Invalid PDF file format - missing PDF header');
          }
          
          // Check for common PDF issues
          const pdfContent = fileBytes.toString('binary');
          if (pdfContent.includes('/Encrypt')) {
            throw new Error('PDF file appears to be password-protected or encrypted');
          }
          
          // Log PDF version for debugging
          const versionMatch = pdfContent.match(/%PDF-(\d\.\d)/);
          if (versionMatch) {
            console.log(`PDF version: ${versionMatch[1]} for ${file.originalname}`);
          }
        }

        // Try different Textract methods with proper error handling
        let response;
        let lastError;
        
        try {
          // Method 1: Try FORMS analysis first
          console.log(`Attempting FORMS analysis for ${file.originalname} (${file.mimetype}, ${(fileBytes.length / 1024).toFixed(1)}KB)`);
          response = await textract.send(new AnalyzeDocumentCommand({
            Document: { Bytes: fileBytes },
            FeatureTypes: ["FORMS"],
          }));
          console.log(`FORMS analysis successful for ${file.originalname}`);
        } catch (formsError) {
          lastError = formsError;
          console.log(`FORMS analysis failed for ${file.originalname}:`, formsError.code, formsError.message);
          
          try {
            // Method 2: Try TABLES analysis
            console.log(`Attempting TABLES analysis for ${file.originalname}`);
            response = await textract.send(new AnalyzeDocumentCommand({
              Document: { Bytes: fileBytes },
              FeatureTypes: ["TABLES"],
            }));
            console.log(`TABLES analysis successful for ${file.originalname}`);
          } catch (tablesError) {
            lastError = tablesError;
            console.log(`TABLES analysis failed for ${file.originalname}:`, tablesError.code, tablesError.message);
            
            try {
              // Method 3: Try basic text detection
              console.log(`Attempting basic text detection for ${file.originalname}`);
              response = await textract.send(new DetectDocumentTextCommand({
                Document: { Bytes: fileBytes }
              }));
              console.log(`Basic text detection successful for ${file.originalname}`);
            } catch (basicError) {
              lastError = basicError;
              console.log(`Basic text detection failed for ${file.originalname}:`, basicError.code, basicError.message);
              
              // If all methods fail, create a mock response to continue processing
              console.warn(`All Textract methods failed for ${file.originalname}. Creating fallback response.`);
              
              // Create a fallback response structure
              response = {
                Blocks: [
                  {
                    Id: 'fallback-1',
                    BlockType: 'LINE',
                    Text: file.originalname.replace(/\.[^/.]+$/, "").replace(/[-_]/g, " "),
                    Confidence: 0
                  }
                ]
              };
              
              console.log(`Created fallback response for ${file.originalname}`);
            }
          }
        }

        const extracted = mapToStandardJson(response);

        extractedDocs.push({
          filename: file.originalname,
          extracted_data: extracted,
          upload_time: new Date().toISOString()
        });

        // Clean up uploaded file
        try {
          fs.unlinkSync(file.path);
        } catch (unlinkError) {
          console.warn(`Failed to delete file ${file.path}:`, unlinkError.message);
        }
      } catch (fileError) {
        console.error(`Error processing file ${file.originalname}:`, fileError);
        extractedDocs.push({
          filename: file.originalname,
          error: `Failed to process: ${fileError.message}`,
          upload_time: new Date().toISOString()
        });
      }
    }

    // Separate personal and GST documents
    const personalDocs = extractedDocs.filter(doc => 
      !doc.error && (doc.extracted_data.DOCUMENT_TYPE === 'AADHAAR' || doc.extracted_data.DOCUMENT_TYPE === 'PAN')
    );
    const gstDocs = extractedDocs.filter(doc => 
      !doc.error && doc.extracted_data.DOCUMENT_TYPE === 'GST_CERTIFICATE'
    );

    const groupedByPerson = personalDocs.length > 0 ? createGroupedJsonByPerson(personalDocs) : [];
    const groupedByCompany = gstDocs.length > 0 ? createGroupedJsonByCompany(gstDocs) : [];
    
    res.json({
      success: true,
      total_files: req.files.length,
      processed_files: extractedDocs.length,
      personal_documents: {
        count: personalDocs.length,
        grouped_by_person: groupedByPerson
      },
      gst_documents: {
        count: gstDocs.length,
        grouped_by_company: groupedByCompany
      },
      all_extracted_data: extractedDocs
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ 
      error: 'Internal server error', 
      message: error.message 
    });
  }
});

// Main upload endpoint for UI compatibility
app.post('/upload', upload.array('documents', 20), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ 
        error: 'No files uploaded',
        success: false 
      });
    }

    const extractedDocs = [];

    // Process each uploaded file
    for (const file of req.files) {
      try {
        console.log(`Processing file: ${file.originalname}`);
        
        // Process with Textract
        const extracted = await processWithTextract(file.path);
        
        extractedDocs.push({
          filename: file.originalname,
          extracted_data: extracted,
          upload_time: new Date().toISOString()
        });

        // Clean up uploaded file
        try {
          fs.unlinkSync(file.path);
        } catch (unlinkError) {
          console.warn(`Failed to delete file ${file.path}:`, unlinkError.message);
        }
      } catch (fileError) {
        console.error(`Error processing file ${file.originalname}:`, fileError);
        extractedDocs.push({
          filename: file.originalname,
          error: `Failed to process: ${fileError.message}`,
          upload_time: new Date().toISOString()
        });
      }
    }
    
    // Separate personal and GST documents
    const personalDocs = extractedDocs.filter(doc => 
      !doc.error && (doc.extracted_data.DOCUMENT_TYPE === 'AADHAAR' || doc.extracted_data.DOCUMENT_TYPE === 'PAN')
    );
    const gstDocs = extractedDocs.filter(doc => 
      !doc.error && doc.extracted_data.DOCUMENT_TYPE === 'GST_CERTIFICATE'
    );

    const groupedByPerson = personalDocs.length > 0 ? createGroupedJsonByPerson(personalDocs) : [];
    const groupedByCompany = gstDocs.length > 0 ? createGroupedJsonByCompany(gstDocs) : [];
    
    // Save extractedDocs to a file
    const outputPath = path.join(__dirname, 'extracted_results.json');
    fs.writeFileSync(outputPath, JSON.stringify(extractedDocs, null, 2), 'utf8');

    res.json({
      success: true,
      total_files: req.files.length,
      processed_files: extractedDocs.length,
      personal_documents: {
        count: personalDocs.length,
        grouped_by_person: groupedByPerson
      },
      gst_documents: {
        count: gstDocs.length,
        grouped_by_company: groupedByCompany
      },
      all_extracted_data: extractedDocs
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({ 
      error: 'Internal server error', 
      message: error.message,
      success: false
    });
  }
});

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large (max 10MB per file)' });
    }
    if (error.code === 'LIMIT_FILE_COUNT') {
      return res.status(400).json({ error: 'Too many files (max 20 files)' });
    }
    if (error.code === 'LIMIT_UNEXPECTED_FILE') {
      return res.status(400).json({ error: 'Unexpected field in upload' });
    }
    if (error.code === 'LIMIT_FIELD_COUNT') {
      return res.status(400).json({ error: 'Too many fields in request' });
    }
    return res.status(400).json({ error: `Upload error: ${error.message}` });
  }
  res.status(500).json({ error: error.message });
});

app.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});

// New function to map raw OCR text to structured fields
function mapRawTextToFields(rawText) {
  const text = rawText.toUpperCase();
  const fields = {
    GSTIN: "",
    LEGAL_NAME: "",
    TRADE_NAME: "",
    REGISTRATION_DATE: "",
    CONSTITUTION: "",
    ADDRESS: "",
    STATE: "",
    STATUS: "",
    DOCUMENT_TYPE: ""
  };

  // GSTIN
  const gstinMatch = text.match(/\b\d{2}[A-Z]{5}\d{4}[A-Z]\d[Z][A-Z\d]\b/);
  if (gstinMatch) {
    fields.GSTIN = gstinMatch[0];
    fields.DOCUMENT_TYPE = "GST_CERTIFICATE";
  }

  // Legal Name
  const legalNameMatch = text.match(/LEGAL\s*NAME[:\-]?\s*([A-Z\s&.,]+)/);
  if (legalNameMatch) {
    fields.LEGAL_NAME = legalNameMatch[1].trim();
  } else {
    // Fallback: find first line after GSTIN
    if (fields.GSTIN) {
      const afterGstin = text.split(fields.GSTIN)[1];
      if (afterGstin) {
        const lines = afterGstin.split('\n').map(l => l.trim()).filter(Boolean);
        if (lines.length > 0) fields.LEGAL_NAME = lines[0];
      }
    }
  }

  // Trade Name
  const tradeNameMatch = text.match(/TRADE\s*NAME[:\-]?\s*([A-Z\s&.,]+)/);
  if (tradeNameMatch) {
    fields.TRADE_NAME = tradeNameMatch[1].trim();
  }

  // Registration Date
  const regDateMatch = text.match(/REGISTRATION\s*DATE[:\-]?\s*(\d{2}\/\d{2}\/\d{4})/);
  if (regDateMatch) {
    fields.REGISTRATION_DATE = regDateMatch[1];
  } else {
    const dateMatch = text.match(/(\d{2}\/\d{2}\/\d{4})/);
    if (dateMatch) fields.REGISTRATION_DATE = dateMatch[1];
  }

  // Constitution
  const constitutionMatch = text.match(/CONSTITUTION[:\-]?\s*([A-Z\s]+)/);
  if (constitutionMatch) {
    fields.CONSTITUTION = constitutionMatch[1].trim();
  }

  // Address
  const addressMatch = text.match(/ADDRESS[:\-]?\s*([\s\S]{10,100})/);
  if (addressMatch) {
    fields.ADDRESS = addressMatch[1].split('\n')[0].trim();
  }

  // State
  const stateMatch = text.match(/STATE[:\-]?\s*([A-Z\s]+)/);
  if (stateMatch) {

    fields.STATE = stateMatch[1].trim();
  }

  // Status
  if (text.includes('ACTIVE')) fields.STATUS = 'ACTIVE';
  else if (text.includes('CANCELLED')) fields.STATUS = 'CANCELLED';
  else if (text.includes('SUSPENDED')) fields.STATUS = 'SUSPENDED';

  return fields;
}

// New function to map cheque fields
function mapChequeFields(blocks) {
  const result = {
    AUTHORISER: "",
    PAYEE_NAME: "",
    AMOUNT_NUMERIC: "",
    AMOUNT_WORDS: "",
    DATE: "",
    ACCOUNT_NUMBER: "",
    IFSC_CODE: "",
    BANK_DETAILS: "",
    EXTRA_INFO: ""
  };

  // Build key-value map
  const kvMap = {};
  blocks.forEach(block => {
    if (block.BlockType === 'KEY_VALUE_SET' && block.EntityTypes && block.EntityTypes.includes('KEY')) {
      let key = "";
      if (block.Relationships) {
        block.Relationships.forEach(rel => {
          if (rel.Type === "CHILD") {
            rel.Ids.forEach(cid => {
              const child = blocks.find(b => b.Id === cid);
              if (child && child.Text) key += child.Text + " ";
            });
          }
        });
      }
      key = key.trim().toUpperCase();
      // Find value
      let value = "";
      if (block.Relationships) {
        block.Relationships.forEach(rel => {
          if (rel.Type === "VALUE") {
            rel.Ids.forEach(vid => {
              const valueBlock = blocks.find(b => b.Id === vid);
              if (valueBlock && valueBlock.Relationships) {
                valueBlock.Relationships.forEach(vrel => {
                  if (vrel.Type === "CHILD") {
                    vrel.Ids.forEach(vcid => {
                      const vchild = blocks.find(b => b.Id === vcid);
                      if (vchild && vchild.Text) value += vchild.Text + " ";
                    });
                  }
                });
              }
              if (valueBlock && valueBlock.Text) value += valueBlock.Text + " ";
            });
          }
        });
      }
      value = value.trim();
      kvMap[key] = value;
    }
  });

  // Map keys to cheque fields (attribute mapping)
  result.AUTHORISER = kvMap["AUTHORISED SIGNATORIES"] || "";
  result.PAYEE_NAME = kvMap["PAY"] || "";
  result.AMOUNT_NUMERIC = (kvMap["RS."] || "").replace("...", "").trim();
  result.AMOUNT_WORDS = kvMap["RUPEES"] || "";
  result.DATE = kvMap["DATE"] || "";
  result.ACCOUNT_NUMBER = kvMap["A/C NO."] || "";
  result.IFSC_CODE = kvMap["RTGS / NEFT IFSC: :"] || kvMap["RTGS / NEFT IFSC"] || "";
  result.BANK_DETAILS = kvMap["HDFC BANK LTD."] || "";
  result.EXTRA_INFO = kvMap["WEEKLY HOLIDAY ON SUNDAY"] || "";

  return result;
}

// New function to map cheque key-value pairs
function mapChequeKeyValuePairs(kvPairs) {
  return {
    AUTHORISER: kvPairs["AUTHORISED SIGNATORIES"] || "",
    PAYEE_NAME: kvPairs["PAY"] || "",
    AMOUNT_NUMERIC: (kvPairs["RS."] || "").replace("...", "").trim(),
    AMOUNT_WORDS: kvPairs["RUPEES"] || "",
    DATE: kvPairs["DATE"] || "",
    ACCOUNT_NUMBER: kvPairs["A/C NO."] || "",
    IFSC_CODE: kvPairs["RTGS / NEFT IFSC: :"] || kvPairs["RTGS / NEFT IFSC"] || "",
    BANK_DETAILS: kvPairs["HDFC BANK LTD."] || "",
    EXTRA_INFO: kvPairs["WEEKLY HOLIDAY ON SUNDAY"] || ""
  };
}

// Example usage:
const kvPairs = {
  "AUTHORISED SIGNATORIES": "ample",
  "PAY": "Sample Payee Company",
  "RS.": "... 1,23,45,678.00",
  "RUPEES": "One Crore Twenty Three Lacs Forty Five Thousand Six Hundred Seventy Eight Rupee Only",
  "DATE": "2011 JAN 12",
  "A/C NO.": "",
  "RTGS / NEFT IFSC: :": "HDFC0000374",
  "HDFC BANK LTD.": "725-1, Airport Road,Chicalim, Goa Vasco-da-gama-403711,Goa",
  "WEEKLY HOLIDAY ON SUNDAY": "Preferred"
};

console.log(JSON.stringify(mapChequeKeyValuePairs(kvPairs), null, 2));

// New function to map cheque data from Textract
function mapChequeData(textractData) {
  const result = {};

  for (const [key, value] of Object.entries(textractData)) {
    if (key.includes("RS.")) {
      result["AMOUNT"] = value.replace("...", "").trim();
    } else if (key.includes("PAY")) {
      result["PAYEE_NAME"] = value;
    } else if (key.includes("RUPEES")) {
      result["AMOUNT_IN_WORDS"] = value;
    } else if (key.includes("DATE")) {
      result["CHEQUE_DATE"] = value;
    } else if (key.includes("A/C NO")) {
      result["ACCOUNT_NUMBER"] = value;
    } else if (key.includes("IFSC")) {
      result["IFSC_CODE"] = value;
    } else if (key.includes("HDFC BANK LTD")) {
      result["BANK_BRANCH"] = value;
    } else if (key.includes("AUTHORISED SIGNATORIES")) {
      result["SIGNATORY"] = value;
    } else if (key.includes("WEEKLY HOLIDAY ON SUNDAY")) {
      result["EXTRA_INFO"] = value;
    }
  }

  return result;
}

// Utility: Format Textract blocks to simple JSON key-value pairs
function textractBlocksToJson(blocks) {
  const result = {};
  blocks.forEach(block => {
    if (block.BlockType === 'KEY_VALUE_SET' && block.EntityTypes && block.EntityTypes.includes('KEY')) {
      let key = "";
      if (block.Relationships) {
        block.Relationships.forEach(rel => {
          if (rel.Type === "CHILD") {
            rel.Ids.forEach(cid => {
              const child = blocks.find(b => b.Id === cid);
              if (child && child.Text) key += child.Text + " ";
            });
          }
        });
      }
      key = key.trim();
      let value = "";
      if (block.Relationships) {
        block.Relationships.forEach(rel => {
          if (rel.Type === "VALUE") {
            rel.Ids.forEach(vid => {
              const valueBlock = blocks.find(b => b.Id === vid);
              if (valueBlock && valueBlock.Relationships) {
                valueBlock.Relationships.forEach(vrel => {
                  if (vrel.Type === "CHILD") {
                    vrel.Ids.forEach(vcid => {
                      const vchild = blocks.find(b => b.Id === vcid);
                      if (vchild && vchild.Text) value += vchild.Text + " ";
                    });
                  }
                });
              }
              if (valueBlock && valueBlock.Text) value += valueBlock.Text + " ";
            });
          }
        });
      }
      value = value.trim();
      if (key) result[key] = value;
    }
  });
  return result;
}

// Utility: Return raw Textract blocks as JSON for debugging or direct output
function textractRawJson(blocks) {
  return blocks.map(block => ({
    BlockType: block.BlockType,
    Text: block.Text || "",
    EntityTypes: block.EntityTypes || [],
    Confidence: block.Confidence,
    Id: block.Id,
    Relationships: block.Relationships || []
  }));
}

