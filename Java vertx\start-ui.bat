@echo off
echo Starting AWS Textract Document Extraction UI Server (Java Vert.x)
echo ===============================================================

cd ui

echo Building project...
call mvn clean compile

if %ERRORLEVEL% NEQ 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo Starting UI server on port 8081...
echo Web interface will be available at: http://localhost:8081
call mvn exec:java -Dexec.mainClass="io.vertx.core.Launcher" -Dexec.args="run com.textract.ui.UIVerticle"

pause
