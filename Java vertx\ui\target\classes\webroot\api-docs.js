// API Documentation JavaScript

class APIDocumentation {
    constructor() {
        this.apiBaseUrl = 'http://localhost:8080';
        this.init();
    }

    init() {
        this.setupNavigation();
        this.checkAPIStatus();
        this.setupTestButtons();
        this.setupCodeCopyButtons();
    }

    setupNavigation() {
        // Smooth scrolling for navigation links
        const navLinks = document.querySelectorAll('.nav-menu a');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetId = link.getAttribute('href').substring(1);
                const targetElement = document.getElementById(targetId);
                
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                    
                    // Update active navigation
                    navLinks.forEach(l => l.classList.remove('active'));
                    link.classList.add('active');
                }
            });
        });

        // Update active navigation on scroll
        window.addEventListener('scroll', () => {
            const sections = document.querySelectorAll('.section');
            const scrollPos = window.scrollY + 100;

            sections.forEach(section => {
                const top = section.offsetTop;
                const bottom = top + section.offsetHeight;
                const id = section.getAttribute('id');

                if (scrollPos >= top && scrollPos <= bottom) {
                    navLinks.forEach(link => {
                        link.classList.remove('active');
                        if (link.getAttribute('href') === `#${id}`) {
                            link.classList.add('active');
                        }
                    });
                }
            });
        });
    }

    async checkAPIStatus() {
        const statusElement = document.getElementById('apiStatus');
        
        try {
            statusElement.textContent = 'Checking API...';
            statusElement.className = 'api-status checking';
            
            const response = await fetch(`${this.apiBaseUrl}/health`);
            
            if (response.ok) {
                const data = await response.json();
                statusElement.textContent = '🟢 API Online';
                statusElement.className = 'api-status online';
            } else {
                throw new Error('API not responding');
            }
        } catch (error) {
            statusElement.textContent = '🔴 API Offline';
            statusElement.className = 'api-status offline';
        }
    }

    setupTestButtons() {
        // Health endpoint test button
        const testHealthBtn = document.getElementById('testHealthBtn');
        const healthResult = document.getElementById('healthResult');

        if (testHealthBtn) {
            testHealthBtn.addEventListener('click', async () => {
                testHealthBtn.disabled = true;
                testHealthBtn.textContent = '🔄 Testing...';
                
                try {
                    const response = await fetch(`${this.apiBaseUrl}/health`);
                    const data = await response.json();
                    
                    healthResult.className = 'test-result success';
                    healthResult.innerHTML = `
                        <strong>✅ Success!</strong><br>
                        Status: ${response.status}<br>
                        Response: ${JSON.stringify(data, null, 2)}
                    `;
                } catch (error) {
                    healthResult.className = 'test-result error';
                    healthResult.innerHTML = `
                        <strong>❌ Error!</strong><br>
                        ${error.message}
                    `;
                } finally {
                    testHealthBtn.disabled = false;
                    testHealthBtn.textContent = '🧪 Test Health Endpoint';
                }
            });
        }
    }

    setupCodeCopyButtons() {
        // Add copy buttons to code blocks
        const codeBlocks = document.querySelectorAll('.code-block');
        
        codeBlocks.forEach(block => {
            const copyBtn = document.createElement('button');
            copyBtn.className = 'copy-btn';
            copyBtn.innerHTML = '📋 Copy';
            copyBtn.style.cssText = `
                position: absolute;
                top: 10px;
                right: 10px;
                background: #667eea;
                color: white;
                border: none;
                padding: 0.5rem;
                border-radius: 4px;
                cursor: pointer;
                font-size: 0.8rem;
            `;
            
            block.style.position = 'relative';
            block.appendChild(copyBtn);
            
            copyBtn.addEventListener('click', () => {
                const code = block.querySelector('code').textContent;
                navigator.clipboard.writeText(code).then(() => {
                    copyBtn.innerHTML = '✅ Copied!';
                    setTimeout(() => {
                        copyBtn.innerHTML = '📋 Copy';
                    }, 2000);
                });
            });
        });
    }

    // Utility method to format JSON
    formatJSON(obj) {
        return JSON.stringify(obj, null, 2);
    }

    // Method to test any endpoint
    async testEndpoint(method, endpoint, data = null) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            }
        };

        if (data && method !== 'GET') {
            options.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}${endpoint}`, options);
            const result = await response.json();
            
            return {
                success: true,
                status: response.status,
                data: result
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }
}

// Interactive API Testing
class APITester {
    constructor(apiDocs) {
        this.apiDocs = apiDocs;
        this.createTestingInterface();
    }

    createTestingInterface() {
        // Create a floating test panel
        const testPanel = document.createElement('div');
        testPanel.id = 'apiTestPanel';
        testPanel.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 300px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            padding: 1rem;
            z-index: 1000;
            display: none;
        `;

        testPanel.innerHTML = `
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                <h4>🧪 API Tester</h4>
                <button id="closeTestPanel" style="background: none; border: none; font-size: 1.2rem; cursor: pointer;">×</button>
            </div>
            <div>
                <select id="testMethod" style="width: 100%; padding: 0.5rem; margin-bottom: 0.5rem;">
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                </select>
                <input type="text" id="testEndpoint" placeholder="/health" style="width: 100%; padding: 0.5rem; margin-bottom: 0.5rem;">
                <button id="executeTest" style="width: 100%; padding: 0.5rem; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;">Execute Test</button>
                <div id="testOutput" style="margin-top: 1rem; font-family: monospace; font-size: 0.8rem; max-height: 200px; overflow-y: auto;"></div>
            </div>
        `;

        document.body.appendChild(testPanel);

        // Add toggle button
        const toggleBtn = document.createElement('button');
        toggleBtn.id = 'toggleTestPanel';
        toggleBtn.innerHTML = '🧪 Test API';
        toggleBtn.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #667eea;
            color: white;
            border: none;
            padding: 1rem;
            border-radius: 50px;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            z-index: 999;
        `;

        document.body.appendChild(toggleBtn);

        // Event listeners
        toggleBtn.addEventListener('click', () => {
            testPanel.style.display = testPanel.style.display === 'none' ? 'block' : 'none';
            toggleBtn.style.display = testPanel.style.display === 'block' ? 'none' : 'block';
        });

        document.getElementById('closeTestPanel').addEventListener('click', () => {
            testPanel.style.display = 'none';
            toggleBtn.style.display = 'block';
        });

        document.getElementById('executeTest').addEventListener('click', async () => {
            const method = document.getElementById('testMethod').value;
            const endpoint = document.getElementById('testEndpoint').value;
            const output = document.getElementById('testOutput');

            output.innerHTML = 'Testing...';

            const result = await this.apiDocs.testEndpoint(method, endpoint);
            
            if (result.success) {
                output.innerHTML = `
                    <div style="color: green;">✅ Success (${result.status})</div>
                    <pre style="background: #f8f9fa; padding: 0.5rem; border-radius: 4px; margin-top: 0.5rem;">${JSON.stringify(result.data, null, 2)}</pre>
                `;
            } else {
                output.innerHTML = `
                    <div style="color: red;">❌ Error</div>
                    <pre style="background: #f8f9fa; padding: 0.5rem; border-radius: 4px; margin-top: 0.5rem;">${result.error}</pre>
                `;
            }
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    const apiDocs = new APIDocumentation();
    const apiTester = new APITester(apiDocs);
    
    // Refresh API status every 30 seconds
    setInterval(() => {
        apiDocs.checkAPIStatus();
    }, 30000);
});
