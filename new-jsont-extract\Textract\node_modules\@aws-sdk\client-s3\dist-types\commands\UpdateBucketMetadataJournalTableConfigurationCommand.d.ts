import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { UpdateBucketMetadataJournalTableConfigurationRequest } from "../models/models_1";
import { S3ClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes } from "../S3Client";
/**
 * @public
 */
export type { __MetadataBearer };
export { $Command };
/**
 * @public
 *
 * The input for {@link UpdateBucketMetadataJournalTableConfigurationCommand}.
 */
export interface UpdateBucketMetadataJournalTableConfigurationCommandInput extends UpdateBucketMetadataJournalTableConfigurationRequest {
}
/**
 * @public
 *
 * The output of {@link UpdateBucketMetadataJournalTableConfigurationCommand}.
 */
export interface UpdateBucketMetadataJournalTableConfigurationCommandOutput extends __MetadataBearer {
}
declare const UpdateBucketMetadataJournalTableConfigurationCommand_base: {
    new (input: UpdateBucketMetadataJournalTableConfigurationCommandInput): import("@smithy/smithy-client").CommandImpl<UpdateBucketMetadataJournalTableConfigurationCommandInput, UpdateBucketMetadataJournalTableConfigurationCommandOutput, S3ClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    new (input: UpdateBucketMetadataJournalTableConfigurationCommandInput): import("@smithy/smithy-client").CommandImpl<UpdateBucketMetadataJournalTableConfigurationCommandInput, UpdateBucketMetadataJournalTableConfigurationCommandOutput, S3ClientResolvedConfig, ServiceInputTypes, ServiceOutputTypes>;
    getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
/**
 * <p>Enables or disables journal table record expiration for an S3 Metadata configuration on a general
 *       purpose bucket. For more information, see
 *       <a href="https://docs.aws.amazon.com/AmazonS3/latest/userguide/metadata-tables-overview.html">Accelerating
 *         data discovery with S3 Metadata</a> in the <i>Amazon S3 User Guide</i>.</p>
 *          <dl>
 *             <dt>Permissions</dt>
 *             <dd>
 *                <p>To use this operation, you must have the <code>s3:UpdateBucketMetadataJournalTableConfiguration</code>
 *             permission. For more information, see <a href="https://docs.aws.amazon.com/AmazonS3/latest/userguide/metadata-tables-permissions.html">Setting up permissions for
 *               configuring metadata tables</a> in the <i>Amazon S3 User Guide</i>. </p>
 *             </dd>
 *          </dl>
 *          <p>The following operations are related to <code>UpdateBucketMetadataJournalTableConfiguration</code>:</p>
 *          <ul>
 *             <li>
 *                <p>
 *                   <a href="https://docs.aws.amazon.com/AmazonS3/latest/API/API_CreateBucketMetadataConfiguration.html">CreateBucketMetadataConfiguration</a>
 *                </p>
 *             </li>
 *             <li>
 *                <p>
 *                   <a href="https://docs.aws.amazon.com/AmazonS3/latest/API/API_DeleteBucketMetadataConfiguration.html">DeleteBucketMetadataConfiguration</a>
 *                </p>
 *             </li>
 *             <li>
 *                <p>
 *                   <a href="https://docs.aws.amazon.com/AmazonS3/latest/API/API_GetBucketMetadataConfiguration.html">GetBucketMetadataConfiguration</a>
 *                </p>
 *             </li>
 *             <li>
 *                <p>
 *                   <a href="https://docs.aws.amazon.com/AmazonS3/latest/API/API_UpdateBucketMetadataInventoryTableConfiguration.html">UpdateBucketMetadataInventoryTableConfiguration</a>
 *                </p>
 *             </li>
 *          </ul>
 * @example
 * Use a bare-bones client and the command you need to make an API call.
 * ```javascript
 * import { S3Client, UpdateBucketMetadataJournalTableConfigurationCommand } from "@aws-sdk/client-s3"; // ES Modules import
 * // const { S3Client, UpdateBucketMetadataJournalTableConfigurationCommand } = require("@aws-sdk/client-s3"); // CommonJS import
 * const client = new S3Client(config);
 * const input = { // UpdateBucketMetadataJournalTableConfigurationRequest
 *   Bucket: "STRING_VALUE", // required
 *   ContentMD5: "STRING_VALUE",
 *   ChecksumAlgorithm: "CRC32" || "CRC32C" || "SHA1" || "SHA256" || "CRC64NVME",
 *   JournalTableConfiguration: { // JournalTableConfigurationUpdates
 *     RecordExpiration: { // RecordExpiration
 *       Expiration: "ENABLED" || "DISABLED", // required
 *       Days: Number("int"),
 *     },
 *   },
 *   ExpectedBucketOwner: "STRING_VALUE",
 * };
 * const command = new UpdateBucketMetadataJournalTableConfigurationCommand(input);
 * const response = await client.send(command);
 * // {};
 *
 * ```
 *
 * @param UpdateBucketMetadataJournalTableConfigurationCommandInput - {@link UpdateBucketMetadataJournalTableConfigurationCommandInput}
 * @returns {@link UpdateBucketMetadataJournalTableConfigurationCommandOutput}
 * @see {@link UpdateBucketMetadataJournalTableConfigurationCommandInput} for command's `input` shape.
 * @see {@link UpdateBucketMetadataJournalTableConfigurationCommandOutput} for command's `response` shape.
 * @see {@link S3ClientResolvedConfig | config} for S3Client's `config` shape.
 *
 * @throws {@link S3ServiceException}
 * <p>Base exception class for all service exceptions from S3 service.</p>
 *
 *
 * @public
 */
export declare class UpdateBucketMetadataJournalTableConfigurationCommand extends UpdateBucketMetadataJournalTableConfigurationCommand_base {
    /** @internal type navigation helper, not in runtime. */
    protected static __types: {
        api: {
            input: UpdateBucketMetadataJournalTableConfigurationRequest;
            output: {};
        };
        sdk: {
            input: UpdateBucketMetadataJournalTableConfigurationCommandInput;
            output: UpdateBucketMetadataJournalTableConfigurationCommandOutput;
        };
    };
}
