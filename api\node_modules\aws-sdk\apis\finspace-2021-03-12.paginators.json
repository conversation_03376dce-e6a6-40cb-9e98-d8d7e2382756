{"pagination": {"ListKxChangesets": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListKxClusterNodes": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListKxDatabases": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListKxDataviews": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}, "ListKxEnvironments": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "environments"}, "ListKxScalingGroups": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults"}}}